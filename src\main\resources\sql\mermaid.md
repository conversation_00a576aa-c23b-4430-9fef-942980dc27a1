```mermaid
erDiagram
    %% =============================================
    %% 1. 核心人力资源模块 (Core HR)
    %% =============================================
    department {
        INT department_id PK "部门ID"
        VARCHAR department_name "部门名称"
        VARCHAR department_description "部门描述"
        INT parent_department_id FK "上级部门ID"
        VARCHAR status "状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    position {
        INT position_id PK "职位ID"
        VARCHAR position_name "职位名称"
        VARCHAR position_description "职位描述"
        VARCHAR status "状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    position_department {
        INT position_id PK,FK "职位ID"
        INT department_id PK,FK "部门ID"
        DATETIME create_time "创建时间"
    }

    employee {
        INT employee_id PK "员工ID"
        VARCHAR name "姓名"
        VARCHAR phone "手机号"
        VARCHAR email "邮箱 (登录账号)"
        VARCHAR password "密码"
        DATE entry_date "入职时间"
        DATE exit_date "离职时间"
        VARCHAR id_card "身份证号"
        INT department_id FK "部门ID"
        INT position_id FK "职位ID"
        VARCHAR logistics_route "所属物流航线"
        VARCHAR status "工作状态"
        VARCHAR role "角色"
        JSON accessible_menu_ids "可访问菜单ID"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    department_leader {
        INT id PK "关联记录ID"
        INT department_id FK "部门ID"
        INT employee_id FK "员工ID（负责人）"
        VARCHAR leader_role "负责人角色"
        DATE start_date "任职开始日期"
        DATE end_date "任职结束日期"
        BOOLEAN is_active "是否激活"
        VARCHAR remark "备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    department ||--o{ department : "拥有子部门"
    department }o--o{ position_department : "关联"
    position }o--o{ position_department : "关联"
    department ||--o{ employee : "拥有"
    position ||--o{ employee : "担任"
    department }o--o{ department_leader : "由...领导"
    employee }o--o{ department_leader : "担任负责人"


    %% =============================================
    %% 2. 财务与薪酬模块 (Finance & Compensation)
    %% =============================================
    salary {
        INT id PK "工资记录ID"
        INT employee_id FK "员工ID"
        VARCHAR date "日期（年月）"
        DECIMAL basic_salary "基本工资"
        DECIMAL performance_bonus "奖金"
        DECIMAL full_attendance_bonus "全勤奖金"
        DECIMAL business_operation_bonus "业务操作奖金"
        DECIMAL sum_salary "实得金额"
        DECIMAL leave_deduction "请假扣款"
        DECIMAL deduction "扣款"
        DECIMAL late_deduction "迟到缺卡扣款"
        DECIMAL social_security_personal "社保个人部分"
        DECIMAL provident_fund "公积金"
        DECIMAL tax "个税"
        DECIMAL water_electricity_fee "水电费"
        DECIMAL actual_salary "实发工资"
        DECIMAL reimbursement "报销"
        DECIMAL private_account "私账"
        DECIMAL total_salary "合计"
        VARCHAR remark "备注"
    }

    performance {
        INT id PK "业绩记录ID"
        INT employee_id FK "员工ID"
        VARCHAR date "日期（年月）"
        DECIMAL estimated_performance "预估业绩"
        DECIMAL actual_performance "实际业绩"
    }

    petty_cash {
        INT id PK "备用金ID"
        INT employee_id FK "员工ID"
        VARCHAR date "年月"
        VARCHAR purpose "用途"
        DECIMAL amount "金额"
        VARCHAR status "状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    department_expense {
        INT id PK "开销ID"
        INT department_id FK "部门ID"
        DATE expense_date "开销日期"
        VARCHAR item_name "项目名称"
        DECIMAL amount "金额"
        VARCHAR remark "备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    employee_other_expense {
        INT id PK "费用ID"
        INT employee_id FK "员工ID"
        DATE expense_date "费用日期"
        VARCHAR item_name "项目名称"
        DECIMAL amount "金额"
        VARCHAR remark "备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    employee ||--o{ salary : "的工资"
    employee ||--o{ performance : "的业绩"
    employee ||--o{ petty_cash : "申请备用金"
    employee ||--o{ employee_other_expense : "有其他费用"
    department ||--o{ department_expense : "有日常开销"


    %% =============================================
    %% 3. 客户与销售模块 (CRM & Sales)
    %% =============================================
    client {
        INT client_id PK "客户ID"
        VARCHAR name "客户名称"
        VARCHAR contact_person "联系人"
        INT employee_id FK "员工ID (销售)"
        INT operator_id FK "操作员ID"
        VARCHAR category "客户分类"
        VARCHAR status "审核状态"
        VARCHAR client_status "客户合作状态"
        DATETIME operation_time "操作时间"
        VARCHAR nationality "国籍"
        VARCHAR email "客户邮箱"
        VARCHAR phone "客户电话"
        VARCHAR remark "备注"
        VARCHAR reject_remark "拒绝备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    sales_daily_report {
        BIGINT id PK "日报ID"
        INT employee_id FK "员工ID"
        DATE report_date "日报日期"
        INT yearly_new_clients "年度新客户总数"
        INT monthly_new_clients "当月新客户总数"
        INT days_since_last_new_client "距离上次出新客户天数"
        JSON inquiry_clients "询价客户ID列表"
        JSON shipping_clients "出货客户ID列表"
        JSON key_development_clients "重点开发客户ID列表"
        ENUM responsibility_level "责任心评级"
        JSON end_of_day_checklist "下班准备工作检查清单"
        TEXT daily_results "今日效果"
        TEXT meeting_report "会议报告"
        TEXT work_diary "工作日记"
        TEXT manager_evaluation "领导评价"
        DATETIME evaluation_time "评价时间"
        INT evaluator_id FK "评价人ID"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    employee ||--o{ client : "跟进"
    employee ||--o{ client : "操作"
    employee ||--o{ sales_daily_report : "填写日报"
    employee ||--o{ sales_daily_report : "评价日报"


    %% =============================================
    %% 4. 推广模块 (Promotion)
    %% =============================================
    promotion {
        BIGINT id PK "推广ID"
        VARCHAR title "推广标题"
        LONGTEXT content "推广内容"
        ENUM content_type "内容类型"
        JSON attachments "附件文件信息列表"
        VARCHAR content_summary "内容摘要"
        JSON images "推广图片URL列表"
        INT author_id FK "发布人员工ID"
        ENUM status "审核状态"
        DATETIME submit_time "提交审核时间"
        DATETIME audit_time "审核完成时间"
        INT auditor_id FK "审核人员工ID"
        VARCHAR reject_reason "拒绝理由"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    promotion_attachments {
        BIGINT id PK "附件ID"
        BIGINT promotion_id FK "推广ID"
        VARCHAR file_name "原始文件名"
        VARCHAR file_path "文件存储路径"
        BIGINT file_size "文件大小"
        VARCHAR file_type "文件MIME类型"
        VARCHAR file_extension "文件扩展名"
        DATETIME upload_time "上传时间"
        INT uploader_id FK "上传者员工ID"
        INT download_count "下载次数"
        BOOLEAN is_deleted "是否已删除"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    employee ||--o{ promotion : "发布"
    employee ||--o{ promotion : "审核"
    promotion ||--o{ promotion_attachments : "包含附件"
    employee ||--o{ promotion_attachments : "上传附件"


    %% =============================================
    %% 5. 会议管理模块 (Meeting Management)
    %% =============================================
    meeting_location {
        INT id PK "地点ID"
        VARCHAR name "地点名称"
        TEXT description "地点描述"
        INT capacity "容纳人数"
        TEXT facilities "设施设备描述"
        TIME open_time "开放开始时间"
        TIME close_time "开放结束时间"
        VARCHAR available_days "可用星期"
        DATE available_start_date "开放开始日期"
        DATE available_end_date "开放结束日期"
        ENUM status "状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    meeting {
        BIGINT id PK "会议ID"
        VARCHAR title "会议主题"
        TEXT content "会议内容描述"
        DATETIME start_time "会议开始时间"
        DATETIME end_time "会议结束时间"
        VARCHAR location "会议地点（文本）"
        INT location_id FK "会议地点ID"
        INT creator_id FK "发起人ID"
        INT responsible_id FK "会议负责人ID"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    meeting_participant {
        BIGINT id PK "参与者记录ID"
        BIGINT meeting_id FK "会议ID"
        ENUM participant_type "参与者类型"
        INT participant_id "参与者ID"
        DATETIME create_time "创建时间"
    }

    meeting_summary {
        BIGINT id PK "总结ID"
        BIGINT meeting_id FK "会议ID"
        INT employee_id FK "总结人ID"
        TEXT summary_content "总结内容"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    meeting_location ||--o{ meeting : "举办于"
    employee ||--o{ meeting : "发起"
    employee ||--o{ meeting : "负责"
    meeting ||--o{ meeting_participant : "包含参与者"
    meeting ||--o{ meeting_summary : "产出总结"
    employee ||--o{ meeting_summary : "撰写总结"


    %% =============================================
    %% 6. 股票管理模块 (Stock Management)
    %% =============================================
    stock_price {
        BIGINT id PK "股票价格ID"
        DECIMAL unit_price "股票单价"
        DATE time "股票价格时间"
        VARCHAR remark "备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    employee_stock {
        BIGINT id PK "员工股票记录ID"
        BIGINT stock_id FK "股票价格表ID"
        INT employee_id FK "员工ID"
        INT quantity "股票数量"
        DATETIME acquisition_time "获取时间"
        DATETIME unlock_time "解禁时间"
        VARCHAR remark "备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    employee_stock_summary {
        INT employee_id PK,FK "员工ID"
        INT total_quantity "总股票数量"
        INT unlocked_quantity "已解禁数量"
        INT withdrawn_quantity "已提现数量"
        INT available_quantity "可提现数量 (计算列)"
        DATETIME last_calculated_time "最后计算时间"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    stock_withdrawal {
        BIGINT id PK "提现申请ID"
        INT employee_id FK "员工ID"
        INT quantity "提现股票数量"
        DECIMAL unit_price "申请时股票单价"
        DECIMAL total_amount "提现总金额"
        ENUM status "申请状态"
        DATETIME apply_time "申请时间"
        DATETIME audit_time "审核时间"
        INT auditor_id FK "审核人ID"
        VARCHAR reject_reason "拒绝理由"
        VARCHAR remark "备注"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    stock_price ||--o{ employee_stock : "定价"
    employee ||--o{ employee_stock : "持有"
    employee ||--|| employee_stock_summary : "的股票汇总"
    employee ||--o{ stock_withdrawal : "申请提现"
    employee ||--o{ stock_withdrawal : "审核提现"


    %% =============================================
    %% 7. 系统通知模块 (Notification System)
    %% =============================================
    notification_rules {
        INT rule_id PK "规则ID"
        VARCHAR rule_name "规则名称"
        VARCHAR description "规则描述"
        VARCHAR target_position_name "目标职位名称"
        VARCHAR condition_type "条件类型"
        VARCHAR message_template_cn "中文通知消息模板"
        INT default_show_again_after_days "默认再次提示间隔天数"
        INT priority "优先级"
        BOOLEAN is_active "规则是否激活"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    employee_notifications {
        BIGINT notification_id PK "通知ID"
        INT employee_id FK "员工ID"
        INT rule_id FK "关联的通知规则ID"
        VARCHAR notification_type "通知类型"
        VARCHAR title_cn "中文通知标题"
        VARCHAR message_cn "中文通知内容"
        DATETIME generated_at "通知生成时间"
        DATETIME last_shown_at "上次弹窗显示时间"
        DATETIME dismissed_at "用户忽略时间"
        INT show_again_after_days "下次提示间隔天数"
        DATE next_prompt_date "下次应提示日期"
        BOOLEAN is_read_in_bell "铃铛中是否已读"
        VARCHAR status "状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    notification_rules ||--o{ employee_notifications : "生成"
    employee ||--o{ employee_notifications : "接收"
```

---

## 🔄 **双数据库系统架构关系图**

### **1. 系统架构概览图**

```mermaid
flowchart TB
    subgraph CMS["🏢 公司管理系统 (company_management_system)"]
        direction TB
        EMP[("👥 employee<br/>员工表")]
        DEPT[("🏛️ department<br/>部门表")]
        CLIENT[("🤝 client<br/>客户表")]
        POS[("💼 position<br/>职位表")]

        EMP -.-> DEPT
        EMP -.-> POS
        CLIENT -.-> EMP
    end

    subgraph MQ["📨 消息队列 (RabbitMQ/Kafka)"]
        direction LR
        EMP_QUEUE["employee.events"]
        CLIENT_QUEUE["client.events"]
        DEPT_QUEUE["department.events"]
    end

    subgraph LPS["🚢 物流平台系统 (logistics_platform_system)"]
        direction TB

        subgraph REPLICAS["📋 主数据副本"]
            EMP_REP[("👥 employee_replicas<br/>员工副本表")]
            CLIENT_REP[("🤝 client_replicas<br/>客户副本表")]
        end

        subgraph SYNC["🔄 同步机制"]
            SYNC_EVENTS[("📝 sync_events<br/>同步事件表")]
            SYNC_STATUS[("📊 sync_status<br/>同步状态表")]
        end

        subgraph LOGISTICS["🚛 物流业务"]
            ORDERS[("📦 logistics_orders<br/>物流订单表")]
            PORTS[("🏗️ ports<br/>港口表")]
        end

        EMP_REP -.-> ORDERS
        CLIENT_REP -.-> ORDERS
        PORTS -.-> ORDERS
        SYNC_EVENTS -.-> EMP_REP
        SYNC_EVENTS -.-> CLIENT_REP
    end

    %% 数据流向
    EMP ==>|"事件发布"| EMP_QUEUE
    CLIENT ==>|"事件发布"| CLIENT_QUEUE
    DEPT ==>|"事件发布"| DEPT_QUEUE

    EMP_QUEUE ==>|"事件消费"| SYNC_EVENTS
    CLIENT_QUEUE ==>|"事件消费"| SYNC_EVENTS
    DEPT_QUEUE ==>|"事件消费"| SYNC_EVENTS

    SYNC_EVENTS ==>|"数据同步"| EMP_REP
    SYNC_EVENTS ==>|"数据同步"| CLIENT_REP

    %% 样式
    classDef cmsStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef lpsStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef mqStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef replicaStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef syncStyle fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef logisticsStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class EMP,DEPT,CLIENT,POS cmsStyle
    class EMP_REP,CLIENT_REP replicaStyle
    class SYNC_EVENTS,SYNC_STATUS syncStyle
    class ORDERS,PORTS logisticsStyle
    class EMP_QUEUE,CLIENT_QUEUE,DEPT_QUEUE mqStyle
```

### **2. 数据库表结构关系图**

```mermaid
erDiagram
    %% =============================================
    %% 公司管理系统核心表 (company_management_system)
    %% =============================================

    CMS_employee {
        INT employee_id PK "员工ID"
        VARCHAR name "员工姓名"
        VARCHAR email "邮箱地址"
        INT department_id FK "部门ID"
        INT position_id FK "职位ID"
        VARCHAR status "员工状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    CMS_department {
        INT department_id PK "部门ID"
        VARCHAR department_name "部门名称"
        VARCHAR department_description "部门描述"
        INT parent_department_id FK "上级部门ID"
        VARCHAR status "状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    CMS_client {
        INT client_id PK "客户ID"
        VARCHAR name "客户名称"
        INT employee_id FK "负责员工ID"
        VARCHAR email "客户邮箱"
        VARCHAR phone "客户电话"
        VARCHAR category "客户分类"
        VARCHAR status "审核状态"
        VARCHAR client_status "合作状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    CMS_position {
        INT position_id PK "职位ID"
        VARCHAR position_name "职位名称"
        VARCHAR position_description "职位描述"
        VARCHAR status "状态"
        DATETIME create_time "创建时间"
        DATETIME update_time "更新时间"
    }

    %% =============================================
    %% 物流平台系统核心表 (logistics_platform_system)
    %% =============================================

    LPS_employee_replicas {
        BIGINT id PK "自增主键"
        INT source_id UK "源系统员工ID"
        VARCHAR name "员工姓名"
        VARCHAR email "邮箱地址"
        VARCHAR department_name "部门名称"
        VARCHAR status "员工状态"
        DATETIME last_sync_time "最后同步时间"
        DATETIME created_at "创建时间"
        DATETIME updated_at "更新时间"
    }

    LPS_client_replicas {
        BIGINT id PK "自增主键"
        INT source_id UK "源系统客户ID"
        VARCHAR name "客户名称"
        VARCHAR contact_person "联系人姓名"
        VARCHAR email "邮箱地址"
        VARCHAR phone "联系电话"
        INT employee_id "负责员工源ID"
        VARCHAR employee_name "负责员工姓名"
        VARCHAR status "客户状态"
        DATETIME last_sync_time "最后同步时间"
        DATETIME created_at "创建时间"
        DATETIME updated_at "更新时间"
    }

    LPS_logistics_orders {
        BIGINT id PK "订单ID"
        VARCHAR order_no UK "订单号"
        INT client_source_id "客户源ID"
        VARCHAR client_name "客户名称"
        INT employee_source_id "负责员工源ID"
        VARCHAR employee_name "负责员工姓名"
        TEXT cargo_description "货物描述"
        DECIMAL cargo_weight "货物重量"
        DECIMAL cargo_volume "货物体积"
        INT origin_port_id FK "起运港口ID"
        INT destination_port_id FK "目的港口ID"
        VARCHAR transport_mode "运输方式"
        DECIMAL estimated_cost "预估费用"
        VARCHAR currency "币种"
        DATE estimated_departure "预计发货日期"
        DATE estimated_arrival "预计到达日期"
        VARCHAR status "订单状态"
        TEXT remark "备注信息"
        DATETIME created_at "创建时间"
        DATETIME updated_at "更新时间"
    }

    LPS_ports {
        INT id PK "港口ID"
        VARCHAR port_code UK "港口代码"
        VARCHAR port_name "港口名称"
        VARCHAR country "所属国家"
        VARCHAR city "所在城市"
        ENUM port_type "港口类型"
        BOOLEAN is_active "是否启用"
        DATETIME created_at "创建时间"
        DATETIME updated_at "更新时间"
    }

    LPS_sync_events {
        BIGINT id PK "事件ID"
        VARCHAR event_id UK "事件唯一标识"
        VARCHAR event_type "事件类型"
        VARCHAR source_system "源系统标识"
        VARCHAR target_table "目标表名"
        BIGINT source_id "源数据ID"
        VARCHAR operation "操作类型"
        JSON event_data "事件数据"
        VARCHAR status "处理状态"
        INT retry_count "重试次数"
        TEXT error_message "错误信息"
        DATETIME processed_at "处理完成时间"
        DATETIME created_at "创建时间"
    }

    LPS_sync_status {
        BIGINT id PK "状态ID"
        VARCHAR table_name UK "表名"
        DATETIME last_sync_time "最后同步时间"
        BIGINT total_records "总记录数"
        BIGINT sync_records "已同步记录数"
        BIGINT failed_records "同步失败记录数"
        BOOLEAN is_syncing "是否正在同步"
        DATETIME created_at "创建时间"
        DATETIME updated_at "更新时间"
    }

    %% =============================================
    %% 公司管理系统内部关系
    %% =============================================
    CMS_department ||--o{ CMS_department : "拥有子部门"
    CMS_department ||--o{ CMS_employee : "拥有员工"
    CMS_position ||--o{ CMS_employee : "员工担任"
    CMS_employee ||--o{ CMS_client : "负责客户"

    %% =============================================
    %% 物流平台系统内部关系
    %% =============================================
    LPS_employee_replicas ||--o{ LPS_logistics_orders : "负责订单"
    LPS_client_replicas ||--o{ LPS_logistics_orders : "下达订单"
    LPS_ports ||--o{ LPS_logistics_orders : "起运港"
    LPS_ports ||--o{ LPS_logistics_orders : "目的港"
    LPS_sync_events ||--o{ LPS_employee_replicas : "同步到"
    LPS_sync_events ||--o{ LPS_client_replicas : "同步到"

    %% =============================================
    %% 跨系统数据同步关系 (通过source_id关联)
    %% =============================================
    CMS_employee ||..o{ LPS_employee_replicas : "同步副本"
    CMS_client ||..o{ LPS_client_replicas : "同步副本"
```

### **3. 事件驱动数据同步流程图**

```mermaid
flowchart TD
    subgraph CMS_SYSTEM["🏢 公司管理系统"]
        CMS_EMP[("👥 employee")]
        CMS_CLIENT[("🤝 client")]
        CMS_DEPT[("🏛️ department")]

        CMS_SERVICE["📡 事件发布服务<br/>(EventPublisher)"]

        CMS_EMP --> CMS_SERVICE
        CMS_CLIENT --> CMS_SERVICE
        CMS_DEPT --> CMS_SERVICE
    end

    subgraph MESSAGE_QUEUE["📨 消息队列中间件"]
        direction LR
        EMP_TOPIC["employee.exchange<br/>📋 employee.*"]
        CLIENT_TOPIC["client.exchange<br/>📋 client.*"]
        DEPT_TOPIC["department.exchange<br/>📋 department.*"]

        DLX["💀 死信队列<br/>(Dead Letter Exchange)"]
    end

    subgraph LPS_SYSTEM["🚢 物流平台系统"]
        direction TB

        subgraph CONSUMERS["🔄 事件消费者"]
            EMP_CONSUMER["👥 EmployeeEventConsumer"]
            CLIENT_CONSUMER["🤝 ClientEventConsumer"]
            DEPT_CONSUMER["🏛️ DepartmentEventConsumer"]
        end

        subgraph SYNC_LAYER["📊 同步处理层"]
            SYNC_SERVICE["🔄 SyncEventService"]
            CONSISTENCY_CHECKER["✅ DataConsistencyChecker"]
        end

        subgraph DATA_LAYER["💾 数据存储层"]
            SYNC_EVENTS_TABLE[("📝 sync_events")]
            SYNC_STATUS_TABLE[("📊 sync_status")]
            EMP_REPLICAS[("👥 employee_replicas")]
            CLIENT_REPLICAS[("🤝 client_replicas")]
        end

        EMP_CONSUMER --> SYNC_SERVICE
        CLIENT_CONSUMER --> SYNC_SERVICE
        DEPT_CONSUMER --> SYNC_SERVICE

        SYNC_SERVICE --> SYNC_EVENTS_TABLE
        SYNC_SERVICE --> EMP_REPLICAS
        SYNC_SERVICE --> CLIENT_REPLICAS
        SYNC_SERVICE --> SYNC_STATUS_TABLE

        CONSISTENCY_CHECKER --> EMP_REPLICAS
        CONSISTENCY_CHECKER --> CLIENT_REPLICAS
        CONSISTENCY_CHECKER --> SYNC_STATUS_TABLE
    end

    %% 数据流向
    CMS_SERVICE ==>|"1. 发布事件"| EMP_TOPIC
    CMS_SERVICE ==>|"1. 发布事件"| CLIENT_TOPIC
    CMS_SERVICE ==>|"1. 发布事件"| DEPT_TOPIC

    EMP_TOPIC ==>|"2. 消费事件"| EMP_CONSUMER
    CLIENT_TOPIC ==>|"2. 消费事件"| CLIENT_CONSUMER
    DEPT_TOPIC ==>|"2. 消费事件"| DEPT_CONSUMER

    EMP_TOPIC -.->|"失败重试"| DLX
    CLIENT_TOPIC -.->|"失败重试"| DLX
    DEPT_TOPIC -.->|"失败重试"| DLX

    %% 定时任务
    CONSISTENCY_CHECKER -.->|"定期检查"| CMS_SYSTEM

    %% 样式定义
    classDef cmsStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef mqStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef lpsStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef consumerStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef syncStyle fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef dataStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class CMS_EMP,CMS_CLIENT,CMS_DEPT,CMS_SERVICE cmsStyle
    class EMP_TOPIC,CLIENT_TOPIC,DEPT_TOPIC,DLX mqStyle
    class EMP_CONSUMER,CLIENT_CONSUMER,DEPT_CONSUMER consumerStyle
    class SYNC_SERVICE,CONSISTENCY_CHECKER syncStyle
    class SYNC_EVENTS_TABLE,SYNC_STATUS_TABLE,EMP_REPLICAS,CLIENT_REPLICAS dataStyle
```

### **4. 物流订单业务流程图**

```mermaid
flowchart TD
    START([🚀 开始创建物流订单])

    subgraph VALIDATION["✅ 数据验证阶段"]
        CHECK_CLIENT["🔍 验证客户信息<br/>查询 client_replicas"]
        CHECK_EMPLOYEE["🔍 验证员工信息<br/>查询 employee_replicas"]
        CHECK_PORTS["🔍 验证港口信息<br/>查询 ports"]

        CHECK_CLIENT_RESULT{"客户存在?"}
        CHECK_EMPLOYEE_RESULT{"员工存在?"}
        CHECK_PORTS_RESULT{"港口存在?"}
    end

    subgraph ORDER_CREATION["📦 订单创建阶段"]
        GENERATE_ORDER_NO["🔢 生成订单号<br/>调用 GenerateOrderNo()"]
        CREATE_ORDER["📝 创建物流订单<br/>插入 logistics_orders"]
        SET_ORDER_STATUS["📊 设置订单状态<br/>status = 'draft'"]
    end

    subgraph BUSINESS_LOGIC["🚛 业务逻辑处理"]
        CALCULATE_COST["💰 计算预估费用<br/>基于路线和货物信息"]
        SET_TIMELINE["📅 设置时间计划<br/>预计发货/到达日期"]
        ASSIGN_RESOURCES["🎯 分配资源<br/>确认负责员工"]
    end

    subgraph RESULT["✨ 结果处理"]
        ORDER_SUCCESS["✅ 订单创建成功"]
        ORDER_FAILED["❌ 订单创建失败"]
        SEND_NOTIFICATION["📧 发送通知<br/>给相关人员"]
    end

    %% 主流程
    START --> CHECK_CLIENT
    CHECK_CLIENT --> CHECK_CLIENT_RESULT
    CHECK_CLIENT_RESULT -->|"存在"| CHECK_EMPLOYEE
    CHECK_CLIENT_RESULT -->|"不存在"| ORDER_FAILED

    CHECK_EMPLOYEE --> CHECK_EMPLOYEE_RESULT
    CHECK_EMPLOYEE_RESULT -->|"存在"| CHECK_PORTS
    CHECK_EMPLOYEE_RESULT -->|"不存在"| ORDER_FAILED

    CHECK_PORTS --> CHECK_PORTS_RESULT
    CHECK_PORTS_RESULT -->|"存在"| GENERATE_ORDER_NO
    CHECK_PORTS_RESULT -->|"不存在"| ORDER_FAILED

    GENERATE_ORDER_NO --> CREATE_ORDER
    CREATE_ORDER --> SET_ORDER_STATUS
    SET_ORDER_STATUS --> CALCULATE_COST
    CALCULATE_COST --> SET_TIMELINE
    SET_TIMELINE --> ASSIGN_RESOURCES
    ASSIGN_RESOURCES --> ORDER_SUCCESS
    ORDER_SUCCESS --> SEND_NOTIFICATION

    ORDER_FAILED --> SEND_NOTIFICATION

    %% 数据关联说明
    CHECK_CLIENT -.->|"source_id"| CMS_CLIENT_DATA[("🤝 client_replicas<br/>source_id = client_id")]
    CHECK_EMPLOYEE -.->|"source_id"| CMS_EMPLOYEE_DATA[("👥 employee_replicas<br/>source_id = employee_id")]
    CHECK_PORTS -.->|"port_id"| PORTS_DATA[("🏗️ ports<br/>origin/destination")]

    CREATE_ORDER -.->|"关联"| ORDER_DATA[("📦 logistics_orders<br/>client_source_id<br/>employee_source_id<br/>origin_port_id<br/>destination_port_id")]

    %% 样式定义
    classDef startStyle fill:#c8e6c9,stroke:#388e3c,stroke-width:3px
    classDef validationStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef creationStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef businessStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef resultStyle fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef dataStyle fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef decisionStyle fill:#fff8e1,stroke:#ffa000,stroke-width:2px

    class START startStyle
    class CHECK_CLIENT,CHECK_EMPLOYEE,CHECK_PORTS validationStyle
    class GENERATE_ORDER_NO,CREATE_ORDER,SET_ORDER_STATUS creationStyle
    class CALCULATE_COST,SET_TIMELINE,ASSIGN_RESOURCES businessStyle
    class ORDER_SUCCESS,ORDER_FAILED,SEND_NOTIFICATION resultStyle
    class CMS_CLIENT_DATA,CMS_EMPLOYEE_DATA,PORTS_DATA,ORDER_DATA dataStyle
    class CHECK_CLIENT_RESULT,CHECK_EMPLOYEE_RESULT,CHECK_PORTS_RESULT decisionStyle
```

### **5. 数据同步状态监控图**

```mermaid
flowchart LR
    subgraph MONITORING["📊 同步状态监控"]
        direction TB

        subgraph METRICS["📈 关键指标"]
            SYNC_RATE["🔄 同步成功率<br/>success_events / total_events"]
            SYNC_DELAY["⏱️ 同步延迟<br/>avg(processed_at - created_at)"]
            ERROR_RATE["❌ 错误率<br/>failed_events / total_events"]
            QUEUE_DEPTH["📋 队列深度<br/>pending_events_count"]
        end

        subgraph STATUS_CHECK["✅ 状态检查"]
            CONSISTENCY_CHECK["🔍 数据一致性检查<br/>定期对比源数据"]
            HEALTH_CHECK["💓 健康检查<br/>系统可用性监控"]
            ALERT_SYSTEM["🚨 告警系统<br/>异常情况通知"]
        end

        subgraph DASHBOARD["📊 监控面板"]
            GRAFANA["📈 Grafana 仪表板"]
            PROMETHEUS["📊 Prometheus 指标"]
            LOGS["📝 日志聚合<br/>(ELK Stack)"]
        end
    end

    subgraph DATA_SOURCES["💾 数据源"]
        SYNC_EVENTS_MONITOR[("📝 sync_events<br/>事件处理状态")]
        SYNC_STATUS_MONITOR[("📊 sync_status<br/>同步统计信息")]
        APP_LOGS["📋 应用日志<br/>详细执行记录"]
        MQ_METRICS["📨 消息队列指标<br/>队列深度/吞吐量"]
    end

    %% 数据流向
    SYNC_EVENTS_MONITOR --> SYNC_RATE
    SYNC_EVENTS_MONITOR --> SYNC_DELAY
    SYNC_EVENTS_MONITOR --> ERROR_RATE
    MQ_METRICS --> QUEUE_DEPTH

    SYNC_STATUS_MONITOR --> CONSISTENCY_CHECK
    APP_LOGS --> HEALTH_CHECK

    SYNC_RATE --> ALERT_SYSTEM
    ERROR_RATE --> ALERT_SYSTEM
    CONSISTENCY_CHECK --> ALERT_SYSTEM

    SYNC_RATE --> GRAFANA
    SYNC_DELAY --> GRAFANA
    ERROR_RATE --> GRAFANA
    QUEUE_DEPTH --> GRAFANA

    GRAFANA --> PROMETHEUS
    APP_LOGS --> LOGS

    %% 样式定义
    classDef metricsStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef statusStyle fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef dashboardStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef dataStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class SYNC_RATE,SYNC_DELAY,ERROR_RATE,QUEUE_DEPTH metricsStyle
    class CONSISTENCY_CHECK,HEALTH_CHECK,ALERT_SYSTEM statusStyle
    class GRAFANA,PROMETHEUS,LOGS dashboardStyle
    class SYNC_EVENTS_MONITOR,SYNC_STATUS_MONITOR,APP_LOGS,MQ_METRICS dataStyle
```

### **6. 部署架构图**

```mermaid
flowchart TB
    subgraph INTERNET["🌐 互联网"]
        USER["👤 用户"]
        ADMIN["👨‍💼 管理员"]
    end

    subgraph DMZ["🛡️ DMZ区域"]
        LB["🔄 负载均衡器<br/>(Nginx/HAProxy)"]
        WAF["🛡️ Web应用防火墙"]
    end

    subgraph APP_ZONE["🏢 应用区域"]
        subgraph CMS_CLUSTER["公司管理系统集群"]
            CMS1["📊 CMS-1<br/>Pod"]
            CMS2["📊 CMS-2<br/>Pod"]
        end

        subgraph LPS_CLUSTER["物流平台系统集群"]
            LPS1["🚛 LPS-1<br/>Pod"]
            LPS2["🚛 LPS-2<br/>Pod"]
        end

        subgraph MIDDLEWARE["中间件集群"]
            RABBIT_CLUSTER["📨 RabbitMQ<br/>集群"]
            REDIS_CLUSTER["⚡ Redis<br/>集群"]
        end
    end

    subgraph DATA_ZONE["💾 数据区域"]
        subgraph DB_CLUSTER["数据库集群"]
            MYSQL_MASTER["🗄️ MySQL<br/>主库"]
            MYSQL_SLAVE1["🗄️ MySQL<br/>从库1"]
            MYSQL_SLAVE2["🗄️ MySQL<br/>从库2"]
        end

        subgraph BACKUP["备份存储"]
            NAS["📦 NAS存储"]
            CLOUD_BACKUP["☁️ 云端备份"]
        end
    end

    subgraph MONITOR_ZONE["📊 监控区域"]
        PROMETHEUS["📈 Prometheus"]
        GRAFANA["📊 Grafana"]
        ALERTMANAGER["🚨 AlertManager"]
        ELK["📝 ELK Stack"]
    end

    %% 网络连接
    USER --> WAF
    ADMIN --> WAF
    WAF --> LB

    LB --> CMS1
    LB --> CMS2
    LB --> LPS1
    LB --> LPS2

    CMS_CLUSTER --> MYSQL_MASTER
    LPS_CLUSTER --> MYSQL_MASTER

    MYSQL_MASTER --> MYSQL_SLAVE1
    MYSQL_MASTER --> MYSQL_SLAVE2

    CMS_CLUSTER --> RABBIT_CLUSTER
    LPS_CLUSTER --> RABBIT_CLUSTER
    CMS_CLUSTER --> REDIS_CLUSTER
    LPS_CLUSTER --> REDIS_CLUSTER

    DB_CLUSTER --> NAS
    NAS --> CLOUD_BACKUP

    PROMETHEUS --> APP_ZONE
    PROMETHEUS --> DATA_ZONE
    GRAFANA --> PROMETHEUS
    ALERTMANAGER --> PROMETHEUS
    ELK --> APP_ZONE

    %% 样式定义
    classDef internetStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef dmzStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef appStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef monitorStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class USER,ADMIN internetStyle
    class LB,WAF dmzStyle
    class CMS1,CMS2,LPS1,LPS2,RABBIT_CLUSTER,REDIS_CLUSTER appStyle
    class MYSQL_MASTER,MYSQL_SLAVE1,MYSQL_SLAVE2,NAS,CLOUD_BACKUP dataStyle
    class PROMETHEUS,GRAFANA,ALERTMANAGER,ELK monitorStyle
```

---

## 📋 **架构设计说明**

### **🎯 核心设计原则**

1. **独立数据库架构**:
   - 公司管理系统 (`company_management_system`) 和物流平台系统 (`logistics_platform_system`) 完全独立
   - 避免直接数据库连接，通过事件驱动机制实现数据同步

2. **事件驱动数据同步**:
   - 使用消息队列 (RabbitMQ/Kafka) 实现异步数据同步
   - 保证最终一致性，支持故障恢复和重试机制

3. **主数据副本模式**:
   - `employee_replicas` 和 `client_replicas` 作为主数据的本地副本
   - 通过 `source_id` 字段关联原始数据，支持数据溯源

4. **渐进式扩展**:
   - 精简的表结构设计，便于后续根据业务需求扩展
   - 预留了运输单、运价等表的扩展空间

### **🔄 数据同步机制**

1. **事件发布**: 公司管理系统在数据变更时发布事件到消息队列
2. **事件消费**: 物流平台系统消费事件并更新本地副本数据
3. **状态跟踪**: 通过 `sync_events` 和 `sync_status` 表跟踪同步状态
4. **一致性检查**: 定期检查数据一致性，发现并修复不一致问题

### **🚛 业务流程设计**

1. **订单创建**: 基于本地副本数据验证客户和员工信息
2. **港口管理**: 独立的港口基础数据，支持物流路线规划
3. **状态管理**: 完整的订单状态流转，支持业务流程跟踪

### **📊 监控和运维**

1. **实时监控**: 同步成功率、延迟、错误率等关键指标
2. **告警机制**: 异常情况自动告警，支持快速响应
3. **可视化面板**: Grafana 仪表板提供直观的监控视图

### **🔒 安全设计**

1. **网络隔离**: 通过DMZ、应用区、数据区实现网络分层
2. **访问控制**: 基于角色的权限控制和API访问限制
3. **数据加密**: 敏感数据传输和存储加密
4. **审计日志**: 完整的操作审计和安全日志

### **📈 扩展性设计**

1. **水平扩展**: 支持应用服务的水平扩展
2. **数据库扩展**: 支持读写分离和分库分表
3. **缓存策略**: 多级缓存提升系统性能
4. **CDN加速**: 静态资源CDN加速

这个架构设计确保了系统的**高可用性**、**可扩展性**和**可维护性**，为物流平台的长期发展奠定了坚实的技术基础。

---

## 📚 **图表使用说明**

### **图表类型说明**

1. **erDiagram**: 实体关系图，展示数据库表结构和关系
2. **flowchart**: 流程图，展示业务流程和系统架构
3. **sequenceDiagram**: 时序图，展示系统间的交互流程

### **图表渲染方法**

这些Mermaid图表可以通过以下方式渲染：

1. **在线渲染**: 使用 [Mermaid Live Editor](https://mermaid.live/)
2. **IDE插件**: 安装Mermaid预览插件
3. **文档平台**: GitHub、GitLab等平台原生支持
4. **本地工具**: 使用mermaid-cli等命令行工具

### **图表维护建议**

1. **版本控制**: 图表代码纳入版本控制，跟踪变更历史
2. **定期更新**: 随着系统演进及时更新图表内容
3. **文档同步**: 确保图表与文档描述保持一致
4. **团队协作**: 建立图表更新的团队协作流程
