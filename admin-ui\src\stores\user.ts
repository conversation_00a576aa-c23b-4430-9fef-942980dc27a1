/**
 * 用户状态管理
 * 
 * @description 用户认证和权限状态管理
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/data_structures_final.md
 */

import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import type { UserInfo, LoginCredentials, LoginResponse } from '@/types/api'
import { authApi } from '@/api'
import { storage } from '@/utils'

// 用户状态接口
interface UserState {
  userInfo: UserInfo | null
  token: string | null
  refreshToken: string | null
  permissions: string[]
  roles: string[]
  loading: boolean
  error: string | null
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const state = reactive<UserState>({
    userInfo: null,
    token: storage.getLocal('admin_token'),
    refreshToken: storage.getLocal('admin_refresh_token'),
    permissions: [],
    roles: [],
    loading: false,
    error: null
  })

  // 计算属性
  const isAuthenticated = computed(() => !!state.token)
  const userName = computed(() => state.userInfo?.username || '')
  const userRoles = computed(() => state.userInfo?.roles || [])
  const userPermissions = computed(() => state.userInfo?.permissions || [])

  /**
   * 用户登录
   */
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      state.loading = true
      state.error = null

      const response: LoginResponse = await authApi.login(credentials)
      
      // 保存认证信息
      state.token = response.token
      state.refreshToken = response.refreshToken
      state.userInfo = response.userInfo
      state.permissions = response.userInfo.permissions
      state.roles = response.userInfo.roles

      // 持久化存储
      storage.setLocal('admin_token', response.token)
      storage.setLocal('admin_refresh_token', response.refreshToken)
      storage.setLocal('admin_user_info', response.userInfo)

    } catch (error: any) {
      state.error = error.message || '登录失败'
      throw error
    } finally {
      state.loading = false
    }
  }

  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    try {
      // 调用登出API
      if (state.token) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除状态
      clearUserData()
    }
  }

  /**
   * 刷新Token
   */
  const refreshToken = async (): Promise<void> => {
    if (!state.refreshToken) {
      throw new Error('没有刷新令牌')
    }

    try {
      const response: LoginResponse = await authApi.refreshToken(state.refreshToken)
      
      // 更新认证信息
      state.token = response.token
      state.refreshToken = response.refreshToken
      
      // 持久化存储
      storage.setLocal('admin_token', response.token)
      storage.setLocal('admin_refresh_token', response.refreshToken)

    } catch (error) {
      // 刷新失败，清除用户数据
      clearUserData()
      throw error
    }
  }

  /**
   * 获取当前用户信息
   */
  const getCurrentUser = async (): Promise<void> => {
    if (!state.token) {
      throw new Error('未登录')
    }

    try {
      state.loading = true
      const userInfo: UserInfo = await authApi.getCurrentUser()
      
      state.userInfo = userInfo
      state.permissions = userInfo.permissions
      state.roles = userInfo.roles

      // 更新本地存储
      storage.setLocal('admin_user_info', userInfo)

    } catch (error: any) {
      state.error = error.message || '获取用户信息失败'
      throw error
    } finally {
      state.loading = false
    }
  }

  /**
   * 检查是否有指定权限
   */
  const hasPermission = (permission: string): boolean => {
    return state.permissions.includes(permission)
  }

  /**
   * 检查是否有任一权限
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => state.permissions.includes(permission))
  }

  /**
   * 检查是否有所有权限
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => state.permissions.includes(permission))
  }

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: string): boolean => {
    return state.roles.includes(role)
  }

  /**
   * 检查是否有任一角色
   */
  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => state.roles.includes(role))
  }

  /**
   * 检查是否有所有角色
   */
  const hasAllRoles = (roles: string[]): boolean => {
    return roles.every(role => state.roles.includes(role))
  }

  /**
   * 清除用户数据
   */
  const clearUserData = (): void => {
    state.userInfo = null
    state.token = null
    state.refreshToken = null
    state.permissions = []
    state.roles = []
    state.error = null

    // 清除本地存储
    storage.removeLocal('admin_token')
    storage.removeLocal('admin_refresh_token')
    storage.removeLocal('admin_user_info')
  }

  /**
   * 初始化用户状态
   */
  const initUserState = (): void => {
    // 从本地存储恢复用户信息
    const savedUserInfo = storage.getLocal<UserInfo>('admin_user_info')
    if (savedUserInfo) {
      state.userInfo = savedUserInfo
      state.permissions = savedUserInfo.permissions
      state.roles = savedUserInfo.roles
    }
  }

  // 初始化
  initUserState()

  return {
    // 状态
    ...state,
    
    // 计算属性
    isAuthenticated,
    userName,
    userRoles,
    userPermissions,
    
    // 方法
    login,
    logout,
    refreshToken,
    getCurrentUser,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    clearUserData,
    initUserState
  }
})
