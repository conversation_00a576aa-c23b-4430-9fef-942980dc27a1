package com.example.cllcnplatformbackend.mapper;

import com.example.cllcnplatformbackend.entity.logistics.UserMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户映射Mapper接口（物流平台专用）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-08-01
 */
@Mapper
public interface UserMappingMapper {
    
    /**
     * 根据原始用户ID和用户类型查找用户映射
     */
    UserMapping findByOriginalUserIdAndUserType(@Param("originalUserId") Long originalUserId, 
                                                @Param("userType") UserMapping.UserType userType);
    
    /**
     * 根据平台用户ID查找用户映射
     */
    UserMapping findByPlatformUserId(@Param("platformUserId") String platformUserId);
    
    /**
     * 根据用户名查找用户映射
     */
    UserMapping findByUsername(@Param("username") String username);
    
    /**
     * 根据邮箱查找用户映射
     */
    UserMapping findByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查找用户映射
     */
    UserMapping findByPhone(@Param("phone") String phone);
    
    /**
     * 根据用户类型查找所有激活用户
     */
    List<UserMapping> findActiveByUserType(@Param("userType") UserMapping.UserType userType);
    
    /**
     * 根据部门ID查找员工用户映射
     */
    List<UserMapping> findEmployeesByDepartmentId(@Param("departmentId") Long departmentId);
    
    /**
     * 根据角色查找用户映射
     */
    List<UserMapping> findByRole(@Param("role") UserMapping.UserRole role);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(@Param("username") String username);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(@Param("email") String email);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(@Param("phone") String phone);
    
    /**
     * 插入用户映射
     */
    int insert(UserMapping userMapping);
    
    /**
     * 更新用户映射
     */
    int update(UserMapping userMapping);
    
    /**
     * 删除用户映射
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据原始用户ID和用户类型删除用户映射
     */
    int deleteByOriginalUserIdAndUserType(@Param("originalUserId") Long originalUserId, 
                                         @Param("userType") UserMapping.UserType userType);
}
