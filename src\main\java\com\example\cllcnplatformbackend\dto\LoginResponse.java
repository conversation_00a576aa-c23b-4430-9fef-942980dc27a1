package com.example.cllcnplatformbackend.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户登录响应DTO
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponse {

    private String accessToken;

    private String tokenType = "Bearer";

    private Long expiresIn;

    private UserInfo userInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {

        private String platformUserId;

        private String userType;

        private String realName;

        private String email;

        private String mobile;

        private String status;
    }

    /**
     * 创建登录成功响应
     */
    public static LoginResponse success(String accessToken, Long expiresIn, UserInfo userInfo) {
        return new LoginResponse(accessToken, "Bearer", expiresIn, userInfo);
    }
}
