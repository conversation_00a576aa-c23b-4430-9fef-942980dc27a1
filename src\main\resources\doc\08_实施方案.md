# CLLCN国际物流平台 - 实施方案

## 1. 方案总结

### 1.1 推荐架构：混合架构
基于深度分析，我们推荐采用**混合架构**方案：
- **用户认证共享**：直接使用公司管理系统的 `employee` 和 `client` 表
- **业务数据独立**：物流平台业务数据使用独立数据库
- **适度解耦**：既保证用户体验统一，又实现业务系统独立发展

### 1.2 核心优势
- ✅ **用户体验统一**：员工和客户使用同一套账号体系
- ✅ **系统适度解耦**：业务数据独立，便于独立发展和维护
- ✅ **开发效率高**：复用现有用户体系，减少重复开发
- ✅ **数据一致性好**：通过数据库视图实现实时数据同步
- ✅ **权限管理简单**：基于现有角色体系扩展权限控制

## 2. 详细实施步骤

### 2.1 第一阶段：数据库结构调整（预计2天）

#### 2.1.1 公司管理系统调整
```sql
-- 1. 为client表添加password字段
ALTER TABLE company_management_system.client 
ADD COLUMN password VARCHAR(255) COMMENT '登录密码(加密)' AFTER email;

-- 2. 添加登录相关索引
ALTER TABLE company_management_system.client 
ADD INDEX idx_client_email (email);

-- 3. 为现有客户设置默认密码（需要通知客户修改）
UPDATE company_management_system.client 
SET password = '$2a$10$N.kmcuVIUW6U/TtHW/Qz4OQjpvhZLRXWOZ8nGkSq9fS8.fvewsa8.' -- 默认密码：123456
WHERE password IS NULL;
```

#### 2.1.2 创建物流平台数据库
```sql
-- 1. 创建物流平台数据库
CREATE DATABASE IF NOT EXISTS `logistics_platform` 
DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 2. 创建用户映射表
USE logistics_platform;
-- （参考04_数据模型设计.md中的user_mapping表结构）

-- 3. 创建用户视图
-- （参考04_数据模型设计.md中的v_platform_users视图）
```

### 2.2 第二阶段：统一认证系统开发（预计3-5天）

#### 2.2.1 认证服务开发
```java
@Service
public class UnifiedAuthService {
    
    @Autowired
    private EmployeeRepository employeeRepository;
    
    @Autowired
    private ClientRepository clientRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    public AuthResult authenticate(String email, String password) {
        // 1. 先尝试员工表认证
        Employee employee = employeeRepository.findByEmailAndStatus(email, "Active");
        if (employee != null && passwordEncoder.matches(password, employee.getPassword())) {
            return AuthResult.success()
                .userId("E" + employee.getEmployeeId())
                .userType(UserType.fromRole(employee.getRole()))
                .realName(employee.getName())
                .email(employee.getEmail())
                .mobile(employee.getPhone())
                .departmentId(employee.getDepartmentId())
                .build();
        }
        
        // 2. 再尝试客户表认证
        Client client = clientRepository.findByEmailAndStatus(email, "审核通过");
        if (client != null && client.getPassword() != null 
            && passwordEncoder.matches(password, client.getPassword())) {
            return AuthResult.success()
                .userId("C" + client.getClientId())
                .userType(UserType.CLIENT)
                .realName(client.getName())
                .email(client.getEmail())
                .mobile(client.getPhone())
                .employeeId(client.getEmployeeId())
                .build();
        }
        
        return AuthResult.failure("用户名或密码错误");
    }
}
```

#### 2.2.2 JWT Token机制
```java
@Component
public class JwtTokenProvider {
    
    public String generateToken(AuthResult authResult) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", authResult.getUserId());
        claims.put("userType", authResult.getUserType().name());
        claims.put("realName", authResult.getRealName());
        claims.put("email", authResult.getEmail());
        
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(authResult.getUserId())
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + 7200000)) // 2小时
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }
}
```

#### 2.2.3 权限管理系统
```java
@Component
public class PermissionManager {
    
    public boolean hasPermission(String userId, String resource, String action) {
        UserType userType = getUserType(userId);
        
        switch (userType) {
            case ADMIN:
                return true; // 管理员拥有所有权限
                
            case EMPLOYEE:
            case MANAGER:
                return hasEmployeePermission(resource, action);
                
            case CLIENT:
                return hasClientPermission(userId, resource, action);
                
            default:
                return false;
        }
    }
    
    private boolean hasClientPermission(String userId, String resource, String action) {
        // 客户只能访问自己的数据
        if ("order".equals(resource)) {
            return "view".equals(action) || "create".equals(action);
        }
        if ("route".equals(resource) || "pricing".equals(resource)) {
            return "view".equals(action);
        }
        return false;
    }
}
```

### 2.3 第三阶段：物流业务表创建（预计2-3天）

#### 2.3.1 执行数据库脚本
```bash
# 1. 执行基础数据表创建脚本
mysql -u root -p logistics_platform < scripts/01_basic_tables.sql

# 2. 执行航线运价表创建脚本  
mysql -u root -p logistics_platform < scripts/02_route_pricing_tables.sql

# 3. 执行订单相关表创建脚本
mysql -u root -p logistics_platform < scripts/03_order_tables.sql

# 4. 执行个人中心表创建脚本
mysql -u root -p logistics_platform < scripts/04_personal_tables.sql

# 5. 初始化基础数据
mysql -u root -p logistics_platform < scripts/05_init_data.sql
```

#### 2.3.2 数据完整性验证
```sql
-- 验证外键约束
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM information_schema.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = 'logistics_platform' 
AND CONSTRAINT_TYPE = 'FOREIGN KEY';

-- 验证索引创建
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'logistics_platform'
ORDER BY TABLE_NAME, INDEX_NAME;
```

### 2.4 第四阶段：API开发和测试（预计5-7天）

#### 2.4.1 核心API开发
```java
// 1. 认证相关API
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request) {
        // 统一认证逻辑
    }
    
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refresh(@RequestBody RefreshRequest request) {
        // Token刷新逻辑
    }
}

// 2. 用户管理API
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @GetMapping("/profile")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<UserProfile> getProfile() {
        // 获取用户信息
    }
    
    @PutMapping("/password")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Void> changePassword(@RequestBody ChangePasswordRequest request) {
        // 修改密码
    }
}

// 3. 物流业务API
@RestController
@RequestMapping("/api/routes")
public class RouteController {
    
    @GetMapping("/search")
    public ResponseEntity<PageResult<RouteInfo>> searchRoutes(
        @RequestParam Long originPortId,
        @RequestParam Long destinationPortId,
        // 其他查询参数
    ) {
        // 航线搜索逻辑
    }
}
```

#### 2.4.2 权限控制实现
```java
@Aspect
@Component
public class PermissionAspect {
    
    @Around("@annotation(requirePermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, RequirePermission requirePermission) throws Throwable {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        String resource = requirePermission.resource();
        String action = requirePermission.action();
        
        if (!permissionManager.hasPermission(userId, resource, action)) {
            throw new AccessDeniedException("权限不足");
        }
        
        return joinPoint.proceed();
    }
}
```

## 3. 配置和部署

### 3.1 数据库连接配置
```yaml
# application.yml
spring:
  datasource:
    # 公司管理系统数据库
    company:
      url: *****************************************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
      driver-class-name: com.mysql.cj.jdbc.Driver
    
    # 物流平台数据库
    logistics:
      url: **********************************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
      driver-class-name: com.mysql.cj.jdbc.Driver

# JWT配置
jwt:
  secret: ${JWT_SECRET}
  expiration: 7200 # 2小时
  refresh-expiration: 604800 # 7天
```

### 3.2 多数据源配置
```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.logistics")
    public DataSource logisticsDataSource() {
        return DataSourceBuilder.create().build();
    }
    
    @Bean
    @ConfigurationProperties("spring.datasource.company")
    public DataSource companyDataSource() {
        return DataSourceBuilder.create().build();
    }
}
```

## 4. 测试验证

### 4.1 功能测试清单
- [ ] 员工登录测试（admin、manager、employee角色）
- [ ] 客户登录测试（需要先设置密码）
- [ ] 权限控制测试（不同角色访问不同资源）
- [ ] 航线查询测试
- [ ] 订舱功能测试
- [ ] 订单管理测试
- [ ] 跨数据库查询性能测试

### 4.2 性能测试
```bash
# 使用JMeter进行压力测试
# 1. 登录接口压力测试
# 2. 航线查询接口压力测试  
# 3. 订单创建接口压力测试
# 4. 数据库连接池监控
```

## 5. 上线部署

### 5.1 部署检查清单
- [ ] 数据库结构调整完成
- [ ] 客户密码初始化完成
- [ ] 应用配置文件检查
- [ ] 数据库连接测试
- [ ] API接口测试
- [ ] 权限控制验证
- [ ] 性能测试通过
- [ ] 日志配置完成
- [ ] 监控告警配置

### 5.2 回滚方案
```sql
-- 如果需要回滚client表的password字段
ALTER TABLE company_management_system.client DROP COLUMN password;
ALTER TABLE company_management_system.client DROP INDEX idx_client_email;

-- 删除物流平台数据库（谨慎操作）
-- DROP DATABASE logistics_platform;
```

## 6. 后续优化建议

### 6.1 性能优化
- 实施Redis缓存策略
- 数据库读写分离
- API接口缓存
- 静态资源CDN

### 6.2 功能扩展
- 移动端适配
- 第三方系统集成
- 数据分析和报表
- 国际化支持

### 6.3 运维监控
- 应用性能监控（APM）
- 数据库性能监控
- 业务指标监控
- 异常告警机制
