<!--
  运价查询页面 - 重构版本

  @description 物流平台核心功能页面，提供专业的运价查询功能
  <AUTHOR>
  @date 2025-07-21 10:10:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
  @note 使用shadcn-vue组件和模块化设计，实现现代化交互体验
-->

<template>
  <div class="min-h-screen bg-background">
    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-6 space-y-6">

      <!-- 搜索表单 -->
      <ShippingSearchForm
        v-model="queryForm"
        v-model:service-type="activeServiceType"
        :origin-ports="originPorts"
        :destination-ports="destinationPorts"
        :loading="loading"
        @submit="handleQuery"
      />

      <!-- 结果区域 -->
      <div v-if="queryResults.length > 0 || loading" class="space-y-6">
        <!-- 筛选栏 - 只在搜索结果时显示 -->
        <ShippingFilterBar
          v-if="hasSearched && !showingHotRates"
          v-model:filters="filterOptions"
          :total-results="totalResults"
          :available-carriers="availableCarriers"
          :sort-options="sortOptions"
          @filter-change="applyFilters"
        />

        <!-- 结果列表 -->
        <ShippingResultTable
          :results="queryResults"
          @booking="handleBooking"
        />

        <!-- 分页组件 - 放置在列表下方 -->
        <div
          v-if="totalResults > pageSize"
          class="flex justify-end mt-6"
        >
          <div class="bg-white shadow-lg rounded-lg p-4 border">
            <div class="flex items-center gap-6 text-sm">
              <!-- 每页条数选择 -->
              <div class="flex items-center gap-2">
                <span class="text-gray-700">每页</span>
                <select
                  v-model="pageSize"
                  @change="handlePageSizeChange"
                  class="px-2 py-1 bg-white text-gray-700 focus:outline-none cursor-pointer border border-gray-300 rounded"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span class="text-gray-700">条</span>
              </div>

              <!-- 分页信息 -->
              <div class="text-gray-700">
                共 {{ totalResults }} 条
              </div>

              <!-- 分页控制 -->
              <div class="flex items-center gap-1">
                <button
                  @click="goToPreviousPage"
                  :disabled="currentPage === 1"
                  :class="[
                    'px-3 py-1 transition-colors',
                    currentPage === 1
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:text-black'
                  ]"
                >
                  上一页
                </button>

                <div class="flex items-center gap-1 mx-2">
                  <template v-for="page in visiblePages" :key="page">
                    <button
                      v-if="page !== '...'"
                      @click="goToPage(page)"
                      :class="[
                        'px-3 py-1 rounded transition-colors',
                        page === currentPage
                          ? 'bg-black text-white'
                          : 'text-gray-600 hover:bg-gray-200 hover:text-black'
                      ]"
                    >
                      {{ page }}
                    </button>
                    <span v-else class="px-2 text-gray-400">...</span>
                  </template>
                </div>

                <button
                  @click="goToNextPage"
                  :disabled="currentPage === totalPages"
                  :class="[
                    'px-3 py-1 transition-colors',
                    currentPage === totalPages
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:text-black'
                  ]"
                >
                  下一页
                </button>
              </div>

              <!-- 跳转到指定页 -->
              <div class="flex items-center gap-2">
                <span class="text-gray-700">前往</span>
                <input
                  v-model="jumpToPage"
                  @keyup.enter="handleJumpToPage"
                  @blur="handleJumpToPage"
                  type="number"
                  :min="1"
                  :max="totalPages"
                  class="w-16 px-2 py-1 text-center border border-gray-300 rounded text-gray-700 focus:outline-none focus:border-gray-500"
                  placeholder="页"
                />
                <span class="text-gray-700">页</span>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- 空状态 - 只在搜索后无结果时显示 -->
      <ShippingEmptyState
        v-else-if="!loading && hasSearched && !showingHotRates"
        type="empty"
        @retry="clearSearch"
        @contact="handleContact"
      />

      <!-- 加载状态 -->
      <Card v-if="loading" class="w-full">
        <CardContent class="p-12 text-center">
          <div class="w-8 h-8 mx-auto mb-4 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p class="text-muted-foreground">正在查询运价...</p>
        </CardContent>
      </Card>
    </main>


  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

// UI组件导入
import { Card, CardContent } from '@/components/ui/card'
import {
  Pagination, PaginationContent, PaginationItem,
  PaginationNext, PaginationPrevious
} from '@/components/ui/pagination'

// 业务组件导入
import ShippingSearchForm from '@/components/shipping/ShippingSearchForm.vue'
import ShippingFilterBar from '@/components/shipping/ShippingFilterBar.vue'
import ShippingResultTable from '@/components/shipping/ShippingResultTable.vue'
import ShippingEmptyState from '@/components/shipping/ShippingEmptyState.vue'

// API导入
import { shippingApi } from '@/api/shipping'
import { toast } from 'vue-sonner'

// 响应式数据
const activeServiceType = ref('sea_lcl')
const loading = ref(false)
const hasSearched = ref(false)
const showingHotRates = ref(true) // 标识当前是否显示热门运价

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(5) // 每页显示5条数据
const totalResults = ref(0)
const jumpToPage = ref('')

// 查询表单数据
const queryForm = ref({
  origin: '',
  destination: '',
  cargoReadyDate: '',
  serviceType: 'sea_lcl'
})

// 筛选选项
const filterOptions = ref({
  carrier: '',
  directOnly: false,
  sortBy: ''
})



// 查询结果
const queryResults = ref([])

// 港口数据
const allPorts = ref([])

// 可用船司列表
const availableCarriers = ref(['COSCO', 'MSC', 'EVERGREEN', 'HAPAG-LLOYD', 'MAERSK'])

// 排序选项
const sortOptions = ref([
  { label: '20GP低价', value: 'price_20gp' },
  { label: '40GP低价', value: 'price_40gp' },
  { label: '40HQ低价', value: 'price_40hq' },
  { label: '时效优先', value: 'transit_time' }
])

// 计算属性
const originPorts = computed(() =>
  Array.isArray(allPorts.value) ? allPorts.value.filter(port => port.type === 'origin' || port.type === 'both') : []
)

const destinationPorts = computed(() =>
  Array.isArray(allPorts.value) ? allPorts.value.filter(port => port.type === 'destination' || port.type === 'both') : []
)

// 分页计算属性
const totalPages = computed(() => Math.ceil(totalResults.value / pageSize.value))

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 复杂分页逻辑
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) pages.push(i)
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) pages.push(i)
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) pages.push(i)
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
})

// 方法
// 存储所有热门运价数据
const allHotRates = ref([])

const loadHotRates = async (serviceType) => {
  try {
    loading.value = true
    const response = await shippingApi.getHotRates(serviceType)

    // 存储所有数据
    allHotRates.value = response.rates
    totalResults.value = response.rates.length

    // 对热门运价进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    queryResults.value = response.rates.slice(startIndex, endIndex)

    showingHotRates.value = true
    hasSearched.value = false // 重置搜索状态，表示这是热门推荐
  } catch (error) {
    console.error('获取热门运价失败:', error)
    queryResults.value = []
    totalResults.value = 0
    allHotRates.value = []
  } finally {
    loading.value = false
  }
}

// 监听服务类型变化
watch(activeServiceType, async (newServiceType) => {
  await loadHotRates(newServiceType)
}, { immediate: true })

// 生命周期
onMounted(async () => {
  try {
    const portsResponse = await shippingApi.getPorts()
    console.log('港口数据:', portsResponse)
    allPorts.value = portsResponse.ports || []
  } catch (error) {
    console.error('获取港口数据失败:', error)
    allPorts.value = []
  }
})

const handleQuery = async () => {
  if (!queryForm.value.origin || !queryForm.value.destination) {
    toast.error('请填写起运港和目的港')
    return
  }

  loading.value = true
  hasSearched.value = true
  showingHotRates.value = false // 切换到搜索模式

  // 如果是新查询，重置到第一页
  if (!hasSearched.value) {
    currentPage.value = 1
  }

  try {
    const response = await shippingApi.searchRates({
      origin: queryForm.value.origin,
      destination: queryForm.value.destination,
      serviceType: activeServiceType.value,
      carrier: filterOptions.value.carrier,
      directOnly: filterOptions.value.directOnly,
      sortBy: filterOptions.value.sortBy,
      page: currentPage.value,
      pageSize: pageSize.value
    })

    queryResults.value = response.rates
    totalResults.value = response.total
    toast.success(`查询成功，找到 ${response.total} 个方案`)
  } catch (error) {
    console.error('查询失败:', error)
    toast.error(error.message || '查询失败，请稍后重试')
    queryResults.value = []
    totalResults.value = 0
  } finally {
    loading.value = false
  }
}

const applyFilters = () => {
  if (hasSearched.value) {
    handleQuery()
  }
}



const clearSearch = () => {
  queryForm.value = {
    origin: '',
    destination: '',
    cargoReadyDate: '',
    serviceType: 'sea_lcl'
  }
  filterOptions.value = {
    carrier: '',
    directOnly: false,
    sortBy: ''
  }
  queryResults.value = []
  totalResults.value = 0
  hasSearched.value = false
}

const handleBooking = (result) => {
  toast.success(`正在为您跳转到 ${result.carrier} 的订舱页面...`)
  // 这里可以添加订舱逻辑
}

const handleContact = () => {
  toast.info('客服功能开发中...')
  // 这里可以添加联系客服的逻辑
}

const getServiceTypeName = (serviceType) => {
  const serviceMap = {
    'sea_lcl': '海运拼箱',
    'sea_fcl': '海运整箱',
    'air': '空运',
    'rail_lcl': '铁路拼箱'
  }
  return serviceMap[serviceType] || serviceType
}

// 分页方法
const handlePageChange = (page) => {
  currentPage.value = page
  if (hasSearched.value && !showingHotRates.value) {
    // 如果是搜索模式，重新查询
    handleQuery()
  } else if (showingHotRates.value && allHotRates.value.length > 0) {
    // 如果是热门推荐模式，直接从已有数据分页
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    queryResults.value = allHotRates.value.slice(startIndex, endIndex)
  }
}

const goToPage = (page) => {
  if (page !== '...' && page !== currentPage.value) {
    handlePageChange(page)
  }
}

const goToPreviousPage = () => {
  if (currentPage.value > 1) {
    handlePageChange(currentPage.value - 1)
  }
}

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    handlePageChange(currentPage.value + 1)
  }
}

const handleJumpToPage = () => {
  const page = parseInt(jumpToPage.value)
  if (page && page >= 1 && page <= totalPages.value && page !== currentPage.value) {
    handlePageChange(page)
    jumpToPage.value = ''
  }
}

const handlePageSizeChange = () => {
  // 重置到第一页
  currentPage.value = 1

  if (hasSearched.value && !showingHotRates.value) {
    // 如果是搜索模式，重新查询
    handleQuery()
  } else if (showingHotRates.value && allHotRates.value.length > 0) {
    // 如果是热门推荐模式，重新分页
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    queryResults.value = allHotRates.value.slice(startIndex, endIndex)
  }
}
</script>

<style scoped>
/* 使用Tailwind CSS，无需额外样式 */
</style>
