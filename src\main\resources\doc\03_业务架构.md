# CLLCN国际物流平台 - 业务架构

## 1. 业务流程概述

### 1.1 核心业务流程
CLLCN国际物流平台的核心业务流程围绕海运整箱业务展开，从用户注册到订单完成形成完整的业务闭环。

### 1.2 主要参与角色
- **货主企业**：需要海运服务的企业客户
- **平台运营**：负责平台运营和客户服务
- **船公司**：提供海运服务的承运人
- **港口**：货物装卸的节点
- **系统管理员**：负责系统维护和数据管理

## 2. 详细业务流程

### 2.1 用户注册与认证流程

```mermaid
graph TD
    A[用户访问平台] --> B{用户类型}
    B -->|普通用户| C[注册账号]
    B -->|公司员工| D[系统同步账号]
    C --> E[填写基础信息]
    E --> F[邮箱/手机验证]
    F --> G[账号激活]
    G --> H[企业认证]
    H --> I{认证审核}
    I -->|通过| J[获得完整权限]
    I -->|失败| K[重新提交认证]
    K --> H
    D --> L[直接获得权限]
```

**业务规则**：
- 普通用户必须通过企业认证才能使用订舱功能
- 公司员工账号由公司信息管理系统同步，无需认证
- 企业认证审核时间为1-3个工作日
- 认证失败可无限次重新提交

### 2.2 航线查询与运价展示流程

```mermaid
graph TD
    A[用户进入首页] --> B[选择起运港]
    B --> C[选择目的港]
    C --> D[设置筛选条件]
    D --> E[查询航线]
    E --> F[展示运价列表]
    F --> G[查看费用明细]
    G --> H[比较不同航线]
    H --> I{选择航线}
    I -->|继续查看| F
    I -->|确定选择| J[进入订舱流程]
```

**业务规则**：
- 支持模糊搜索港口名称
- 运价按照用户选择的优先级排序
- 费用明细包含所有相关费用项
- 特惠航线优先展示

### 2.3 订舱业务流程

```mermaid
graph TD
    A[选择航线] --> B[填写货物信息]
    B --> C[选择集装箱类型]
    C --> D[填写联系人信息]
    D --> E[上传相关附件]
    E --> F[确认订舱信息]
    F --> G[提交订舱申请]
    G --> H[系统生成订单]
    H --> I[发送确认邮件]
    I --> J[等待平台确认]
    J --> K{平台审核}
    K -->|通过| L[订单确认]
    K -->|拒绝| M[订单取消]
    L --> N[生成SO单据]
    N --> O[开始物流跟踪]
```

**业务规则**：
- 一个订单可包含多个集装箱
- 每个集装箱可装载多种货物
- 危险品需要特殊标识和处理
- 订单确认后不可修改，只能取消

### 2.4 订单管理流程

```mermaid
graph TD
    A[订单生成] --> B[待确认]
    B --> C{平台审核}
    C -->|通过| D[已确认]
    C -->|拒绝| E[已取消]
    D --> F[订舱成功]
    F --> G[装箱]
    G --> H[开船]
    H --> I[在途]
    I --> J[到港]
    J --> K[清关]
    K --> L[提货]
    L --> M[完成]
    
    B --> N[用户取消]
    N --> E
    D --> O[平台取消]
    O --> E
```

**状态说明**：
- **待确认**：订单已提交，等待平台审核
- **已确认**：平台审核通过，订舱成功
- **进行中**：货物运输过程中的各个阶段
- **已完成**：货物已提取，订单结束
- **已取消**：订单被取消，不再执行

## 3. 数据流转

### 3.1 运价数据流转
1. **数据来源**：船公司提供运价数据
2. **数据录入**：运营人员录入系统
3. **数据处理**：系统计算总费用
4. **数据展示**：用户查询时实时展示
5. **数据更新**：定期更新运价信息

### 3.2 订单数据流转
1. **订单创建**：用户提交订舱信息
2. **订单审核**：平台审核订单信息
3. **订单确认**：生成正式订单和SO单据
4. **状态更新**：物流过程中实时更新状态
5. **订单归档**：完成后归档保存

### 3.3 用户数据流转
1. **注册信息**：用户填写基础信息
2. **认证信息**：提交企业认证材料
3. **审核处理**：人工审核认证信息
4. **权限分配**：根据认证结果分配权限
5. **信息维护**：用户可更新个人信息

## 4. 业务规则

### 4.1 用户权限规则
- **游客**：只能浏览航线和运价，不能订舱
- **注册用户**：可以订舱，但需要企业认证
- **认证用户**：拥有完整功能权限
- **公司员工**：拥有客户服务权限
- **管理员**：拥有系统管理权限

### 4.2 订舱业务规则
- 同一航线同一时间只能订舱一次
- 危险品需要特殊申报和审批
- 超重货物需要额外费用
- 订单确认后24小时内可以取消

### 4.3 费用计算规则
- 海运费按箱型计算
- 附加费按实际情况收取
- 多币种费用需要汇率转换
- 特惠航线享受折扣价格

### 4.4 数据同步规则
- 公司员工信息每日同步一次
- 运价信息实时更新
- 订单状态变更立即通知
- 系统日志实时记录

## 5. 异常处理

### 5.1 业务异常
- **订舱失败**：舱位不足、信息不完整
- **认证失败**：材料不齐全、信息不真实
- **支付失败**：余额不足、支付超时
- **物流异常**：延误、货损、港口罢工

### 5.2 异常处理策略
- **自动重试**：系统异常自动重试3次
- **人工介入**：复杂问题转人工处理
- **用户通知**：及时通知用户异常情况
- **补偿机制**：提供相应的补偿方案

## 6. 集成接口

### 6.1 外部系统集成
- **公司信息管理系统**：员工信息同步
- **船公司系统**：运价和舱位信息
- **港口系统**：港口状态和费用信息
- **支付系统**：在线支付功能
- **物流跟踪系统**：实时物流信息

### 6.2 集成方式
- **API接口**：RESTful API进行数据交换
- **消息队列**：异步处理大量数据
- **定时任务**：定期同步数据
- **Webhook**：实时推送状态变更

## 7. 业务监控

### 7.1 关键指标
- **用户注册转化率**：注册用户/访问用户
- **企业认证通过率**：认证通过/认证申请
- **订单转化率**：订单数量/查询次数
- **客户满意度**：用户评分和反馈

### 7.2 监控方式
- **实时监控**：关键业务指标实时监控
- **定期报告**：每日、每周、每月业务报告
- **异常告警**：业务异常自动告警
- **趋势分析**：业务趋势分析和预测
