<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.cllcnplatformbackend.mapper.ClientMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.cllcnplatformbackend.entity.company.Client">
        <id column="client_id" property="clientId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="contact_person" property="contactPerson" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        client_id, name, email, password, phone, company_name, contact_person, 
        address, status, remark, create_time, update_time
    </sql>

    <!-- 不包含密码的列 -->
    <sql id="Column_List_No_Password">
        client_id, name, email, phone, company_name, contact_person, 
        address, status, remark, create_time, update_time
    </sql>

    <!-- 根据邮箱查找客户 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM client
        WHERE email = #{email}
    </select>

    <!-- 根据手机号查找客户 -->
    <select id="findByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM client
        WHERE phone = #{phone}
    </select>

    <!-- 根据邮箱和状态列表查找客户（用于认证） -->
    <select id="findByEmailAndStatusIn" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM client
        WHERE email = #{email} AND status IN
        <foreach item="status" collection="statusList" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <!-- 根据手机号和状态列表查找客户（用于认证） -->
    <select id="findByPhoneAndStatusIn" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM client
        WHERE phone = #{phone} AND status IN
        <foreach item="status" collection="statusList" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <!-- 根据客户ID和状态列表查找客户（用于认证） -->
    <select id="findByClientIdAndStatusIn" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM client
        WHERE client_id = #{clientId} AND status IN
        <foreach item="status" collection="statusList" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <!-- 根据客户ID查找客户 -->
    <select id="findByClientId" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM client
        WHERE client_id = #{clientId}
    </select>

    <!-- 根据公司名称查找客户 -->
    <select id="findByCompanyNameContaining" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM client
        WHERE company_name LIKE CONCAT('%', #{companyName}, '%')
    </select>

    <!-- 查找所有激活状态的客户 -->
    <select id="findAllActive" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM client
        WHERE status IN ('审核通过', '已合作')
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查找客户 -->
    <select id="findByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM client
        WHERE status = #{status}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM client
        WHERE email = #{email}
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM client
        WHERE phone = #{phone}
    </select>

    <!-- 插入客户 -->
    <insert id="insert" parameterType="com.example.cllcnplatformbackend.entity.company.Client" 
            useGeneratedKeys="true" keyProperty="clientId">
        INSERT INTO client (
            name, email, password, phone, company_name, contact_person,
            address, status, remark, create_time, update_time
        ) VALUES (
            #{name}, #{email}, #{password}, #{phone}, #{companyName}, #{contactPerson},
            #{address}, #{status}, #{remark}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新客户 -->
    <update id="update" parameterType="com.example.cllcnplatformbackend.entity.company.Client">
        UPDATE client
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="email != null">email = #{email},</if>
            <if test="password != null">password = #{password},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="address != null">address = #{address},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE client_id = #{clientId}
    </update>

    <!-- 更新客户密码 -->
    <update id="updatePassword">
        UPDATE client
        SET password = #{password}, update_time = NOW()
        WHERE client_id = #{clientId}
    </update>

    <!-- 删除客户 -->
    <delete id="deleteById">
        DELETE FROM client WHERE client_id = #{clientId}
    </delete>

    <!-- 统计所有客户数量（用于数据库连接验证） -->
    <select id="countAllClients" resultType="int">
        SELECT COUNT(*) FROM client
    </select>

    <!-- 根据手机号模糊查询客户（用于调试验证） -->
    <select id="findByPhoneLike" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM client
        WHERE phone LIKE #{phone}
        LIMIT 10
    </select>

</mapper>
