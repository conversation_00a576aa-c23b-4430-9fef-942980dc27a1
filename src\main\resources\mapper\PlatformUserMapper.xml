<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.cllcnplatformbackend.mapper.PlatformUserMapper">

    <!-- 平台用户结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.cllcnplatformbackend.entity.PlatformUser">
        <result column="platform_user_id" property="platformUserId" jdbcType="VARCHAR"/>
        <result column="source_table" property="sourceTable" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="BIGINT"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="department_id" property="departmentId" jdbcType="BIGINT"/>
        <result column="employee_id" property="employeeId" jdbcType="BIGINT"/>
        <result column="original_role" property="originalRole" jdbcType="VARCHAR"/>
        <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列（不包含密码） -->
    <sql id="Base_Column_List">
        platform_user_id, source_table, source_id, user_type, real_name, 
        email, mobile, status, department_id, employee_id, original_role, last_sync_time
    </sql>

    <!-- 根据邮箱查找用户（包含密码） -->
    <select id="findByEmailWithPassword" resultMap="BaseResultMap">
        SELECT 
            v.platform_user_id, v.source_table, v.source_id, v.user_type, v.real_name,
            v.email, v.mobile, v.status, v.department_id, v.employee_id, v.original_role, v.last_sync_time,
            CASE 
                WHEN v.source_table = 'employee' THEN e.password
                WHEN v.source_table = 'client' THEN c.password
            END as password
        FROM v_platform_users v
        LEFT JOIN company_management_system.employee e ON v.source_table = 'employee' AND v.source_id = e.employee_id
        LEFT JOIN company_management_system.client c ON v.source_table = 'client' AND v.source_id = c.client_id
        WHERE v.email = #{email}
        AND (
            (v.source_table = 'employee' AND e.password IS NOT NULL) OR
            (v.source_table = 'client' AND c.password IS NOT NULL)
        )
        LIMIT 1
    </select>

    <!-- 根据手机号查找用户（包含密码） -->
    <select id="findByMobileWithPassword" resultMap="BaseResultMap">
        SELECT 
            v.platform_user_id, v.source_table, v.source_id, v.user_type, v.real_name,
            v.email, v.mobile, v.status, v.department_id, v.employee_id, v.original_role, v.last_sync_time,
            CASE 
                WHEN v.source_table = 'employee' THEN e.password
                WHEN v.source_table = 'client' THEN c.password
            END as password
        FROM v_platform_users v
        LEFT JOIN company_management_system.employee e ON v.source_table = 'employee' AND v.source_id = e.employee_id
        LEFT JOIN company_management_system.client c ON v.source_table = 'client' AND v.source_id = c.client_id
        WHERE v.mobile = #{mobile}
        AND (
            (v.source_table = 'employee' AND e.password IS NOT NULL) OR
            (v.source_table = 'client' AND c.password IS NOT NULL)
        )
        LIMIT 1
    </select>

    <!-- 根据平台用户ID查找用户 -->
    <select id="findByPlatformUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM v_platform_users
        WHERE platform_user_id = #{platformUserId}
    </select>

    <!-- 根据平台用户ID查找用户（包含密码） -->
    <select id="findByPlatformUserIdWithPassword" resultMap="BaseResultMap">
        SELECT 
            v.platform_user_id, v.source_table, v.source_id, v.user_type, v.real_name,
            v.email, v.mobile, v.status, v.department_id, v.employee_id, v.original_role, v.last_sync_time,
            CASE 
                WHEN v.source_table = 'employee' THEN e.password
                WHEN v.source_table = 'client' THEN c.password
            END as password
        FROM v_platform_users v
        LEFT JOIN company_management_system.employee e ON v.source_table = 'employee' AND v.source_id = e.employee_id
        LEFT JOIN company_management_system.client c ON v.source_table = 'client' AND v.source_id = c.client_id
        WHERE v.platform_user_id = #{platformUserId}
    </select>

</mapper>
