package com.example.cllcnplatformbackend.service.impl;

import com.example.cllcnplatformbackend.dto.LoginRequest;
import com.example.cllcnplatformbackend.dto.LoginResponse;
import com.example.cllcnplatformbackend.entity.company.Client;
import com.example.cllcnplatformbackend.entity.company.Employee;
import com.example.cllcnplatformbackend.mapper.ClientMapper;
import com.example.cllcnplatformbackend.mapper.EmployeeMapper;
import com.example.cllcnplatformbackend.service.AuthService;
import com.example.cllcnplatformbackend.service.LoginAttemptService;
import com.example.cllcnplatformbackend.utils.JwtUtil;
import com.example.cllcnplatformbackend.utils.Md5Util;
import com.example.cllcnplatformbackend.utils.PasswordEncoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import io.jsonwebtoken.Claims;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证服务实现类
 * 实现跨系统的用户认证功能
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final EmployeeMapper employeeMapper;
    private final ClientMapper clientMapper;
    private final JwtUtil jwtUtil;
    private final LoginAttemptService loginAttemptService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        String account = loginRequest.getAccount().trim();
        String password = loginRequest.getPassword().trim();

        log.info("用户登录尝试 - 账号: {}", account);

        try {
            // 1. 检查账户是否被锁定（临时禁用用于调试）
            log.debug("检查账户锁定状态 - 账号: {} (锁定检查已禁用)", account);
            // 临时注释掉锁定检查，用于调试登录功能
            /*
            if (loginAttemptService.isAccountLocked(account)) {
                long remainingTime = loginAttemptService.getLockRemainingTime(account);
                String errorMsg = String.format("账户已被锁定，剩余时间: %d 秒", remainingTime);
                log.warn("账户锁定登录尝试 - 账号: {}, 剩余锁定时间: {} 秒", account, remainingTime);
                throw new RuntimeException(errorMsg);
            }
            */
            log.debug("跳过锁定检查，继续登录流程 - 账号: {}", account);

            // 2. 尝试认证用户
            Employee employee = null;
            Client client = null;
            boolean loginSuccess = false;

            // 首先尝试员工登录（邮箱）
            employee = employeeMapper.findByEmailAndStatus(account, Employee.Status.ACTIVE);
            if (employee != null && employee.getPassword() != null) {
                if (verifyAndUpgradeEmployeePassword(employee, password)) {
                    loginSuccess = true;
                }
            }

            // 尝试员工登录（手机号）
            if (!loginSuccess && employee == null) {
                employee = employeeMapper.findByPhoneAndStatus(account, Employee.Status.ACTIVE);
                if (employee != null && employee.getPassword() != null) {
                    if (verifyAndUpgradeEmployeePassword(employee, password)) {
                        loginSuccess = true;
                    }
                }
            }

            // 尝试客户登录（邮箱）- 不检查客户状态
            if (!loginSuccess && employee == null) {
                log.debug("员工登录失败，开始通过邮箱查找客户 - 邮箱: {} (不限制状态)", account);
                client = clientMapper.findByEmail(account);
                if (client != null) {
                    log.info("通过邮箱找到客户 - 客户ID: {}, 公司名称: {}, 联系人: {}, 状态: {}, 密码是否存在: {}",
                        client.getClientId(), client.getCompanyName(), client.getContactPerson(),
                        client.getStatus(), client.getPassword() != null ? "是" : "否");
                    if (client.getPassword() != null) {
                        log.debug("开始验证客户密码 - 客户ID: {}", client.getClientId());
                        if (verifyAndUpgradeClientPassword(client, password)) {
                            log.info("客户密码验证成功 - 客户ID: {}", client.getClientId());
                            loginSuccess = true;
                        } else {
                            log.warn("客户密码验证失败 - 客户ID: {}", client.getClientId());
                        }
                    } else {
                        log.warn("客户密码为空 - 客户ID: {}", client.getClientId());
                    }
                } else {
                    log.debug("通过邮箱未找到客户 - 邮箱: {}", account);
                }
            }

            // 尝试客户登录（手机号）
            if (!loginSuccess && client == null && employee == null) {
                log.debug("开始通过手机号查找客户 - 手机号: {} (不限制状态)", account);

                // 添加数据库连接验证 - 查询所有客户数量
                try {
                    int totalClients = clientMapper.countAllClients();
                    log.info("数据库连接验证 - 客户表总记录数: {}", totalClients);
                } catch (Exception e) {
                    log.error("数据库连接验证失败: {}", e.getMessage());
                }

                client = clientMapper.findByPhone(account);
                if (client != null) {
                    log.info("通过手机号找到客户 - 客户ID: {}, 公司名称: {}, 联系人: {}, 状态: {}, 密码是否存在: {}",
                        client.getClientId(), client.getCompanyName(), client.getContactPerson(),
                        client.getStatus(), client.getPassword() != null ? "是" : "否");
                    if (client.getPassword() != null) {
                        log.debug("开始验证客户密码 - 客户ID: {}", client.getClientId());
                        if (verifyAndUpgradeClientPassword(client, password)) {
                            log.info("客户密码验证成功 - 客户ID: {}", client.getClientId());
                            loginSuccess = true;
                        } else {
                            log.warn("客户密码验证失败 - 客户ID: {}", client.getClientId());
                        }
                    } else {
                        log.warn("客户密码为空 - 客户ID: {}", client.getClientId());
                    }
                } else {
                    log.debug("通过手机号未找到客户 - 手机号: {}", account);

                    // 添加模糊查询验证 - 查看是否有类似的手机号
                    try {
                        java.util.List<com.example.cllcnplatformbackend.entity.company.Client> similarClients =
                            clientMapper.findByPhoneLike("%" + account + "%");
                        log.info("模糊查询验证 - 包含'{}'的手机号客户数量: {}", account, similarClients.size());
                        for (com.example.cllcnplatformbackend.entity.company.Client c : similarClients) {
                            log.info("相似客户 - ID: {}, 手机号: {}, 状态: {}", c.getClientId(), c.getPhone(), c.getStatus());
                        }
                    } catch (Exception e) {
                        log.error("模糊查询验证失败: {}", e.getMessage());
                    }
                }
            }

            // 3. 处理登录结果
            if (loginSuccess) {
                // 登录成功，清除失败记录
                loginAttemptService.clearFailedAttempts(account);

                if (employee != null) {
                    log.info("员工登录成功 - ID: {}, 姓名: {}", employee.getEmployeeId(), employee.getName());
                    return createLoginResponse(employee);
                } else if (client != null) {
                    log.info("客户登录成功 - ID: {}, 姓名: {}", client.getClientId(), client.getName());
                    return createLoginResponse(client);
                }
            } else {
                // 登录失败，记录失败次数（临时禁用）
                // loginAttemptService.recordFailedAttempt(account);
                log.warn("登录失败 - 账号: {}", account);
                throw new RuntimeException("账号或密码错误");
            }

        } catch (Exception e) {
            if (e.getMessage().contains("账户已被锁定")) {
                throw e; // 重新抛出锁定异常
            }

            log.error("登录过程中发生系统错误 - 账号: {}", account, e);
            // 不暴露具体的系统错误，统一返回账号或密码错误
            throw new RuntimeException("账号或密码错误");
        }

        // 这里不应该到达
        throw new RuntimeException("登录处理异常");
    }

    @Override
    public LoginResponse adminLogin(LoginRequest loginRequest) {
        String account = loginRequest.getAccount().trim();
        String password = loginRequest.getPassword().trim();

        log.info("管理后台登录尝试 - 账号: {}", account);

        try {
            // 1. 检查账户是否被锁定（临时禁用用于调试）
            log.debug("管理后台检查账户锁定状态 - 账号: {} (锁定检查已禁用)", account);
            // 临时注释掉锁定检查，用于调试登录功能
            /*
            if (loginAttemptService.isAccountLocked(account)) {
                long remainingTime = loginAttemptService.getLockRemainingTime(account);
                String errorMsg = String.format("账户已被锁定，剩余时间: %d 秒", remainingTime);
                log.warn("管理后台账户锁定登录尝试 - 账号: {}, 剩余锁定时间: {} 秒", account, remainingTime);
                throw new RuntimeException(errorMsg);
            }
            */

            // 2. 查找员工用户（只查找员工，不查找客户）
            Employee employee = null;
            boolean loginSuccess = false;

            // 尝试通过邮箱查找员工
            log.debug("开始通过邮箱查找员工 - 邮箱: {}, 状态: ACTIVE", account);
            employee = employeeMapper.findByEmailAndStatus(account, Employee.Status.ACTIVE);
            if (employee != null) {
                log.info("通过邮箱找到员工 - 员工ID: {}, 姓名: {}, 角色: {}, 密码是否存在: {}",
                    employee.getEmployeeId(), employee.getName(), employee.getRole(),
                    employee.getPassword() != null ? "是" : "否");
                if (employee.getPassword() != null) {
                    log.debug("开始验证员工密码 - 员工ID: {}", employee.getEmployeeId());
                    if (verifyAndUpgradeEmployeePassword(employee, password)) {
                        log.info("员工密码验证成功 - 员工ID: {}", employee.getEmployeeId());
                        loginSuccess = true;
                    } else {
                        log.warn("员工密码验证失败 - 员工ID: {}", employee.getEmployeeId());
                    }
                } else {
                    log.warn("员工密码为空 - 员工ID: {}", employee.getEmployeeId());
                }
            } else {
                log.debug("通过邮箱未找到员工 - 邮箱: {}", account);
            }

            // 尝试通过手机号查找员工
            if (!loginSuccess && employee == null) {
                log.debug("开始通过手机号查找员工 - 手机号: {}, 状态: ACTIVE", account);
                employee = employeeMapper.findByPhoneAndStatus(account, Employee.Status.ACTIVE);
                if (employee != null) {
                    log.info("通过手机号找到员工 - 员工ID: {}, 姓名: {}, 角色: {}, 密码是否存在: {}",
                        employee.getEmployeeId(), employee.getName(), employee.getRole(),
                        employee.getPassword() != null ? "是" : "否");
                    if (employee.getPassword() != null) {
                        log.debug("开始验证员工密码 - 员工ID: {}", employee.getEmployeeId());
                        if (verifyAndUpgradeEmployeePassword(employee, password)) {
                            log.info("员工密码验证成功 - 员工ID: {}", employee.getEmployeeId());
                            loginSuccess = true;
                        } else {
                            log.warn("员工密码验证失败 - 员工ID: {}", employee.getEmployeeId());
                        }
                    } else {
                        log.warn("员工密码为空 - 员工ID: {}", employee.getEmployeeId());
                    }
                } else {
                    log.debug("通过手机号未找到员工 - 手机号: {}", account);
                }
            }

            // 3. 验证登录结果和admin权限
            if (loginSuccess && employee != null) {
                // 检查是否为admin角色
                if (!Employee.Role.ADMIN.equals(employee.getRole())) {
                    log.warn("非管理员尝试登录管理后台 - 员工ID: {}, 角色: {}", employee.getEmployeeId(), employee.getRole());
                    // loginAttemptService.recordFailedAttempt(account);
                    throw new RuntimeException("权限不足，只有管理员才能登录管理后台");
                }

                // 登录成功，清除失败记录
                loginAttemptService.clearFailedAttempts(account);

                log.info("管理员登录成功 - ID: {}, 姓名: {}, 角色: {}",
                    employee.getEmployeeId(), employee.getName(), employee.getRole());
                return createAdminLoginResponse(employee);

            } else {
                // 登录失败，记录失败次数（临时禁用）
                // loginAttemptService.recordFailedAttempt(account);
                log.warn("管理后台登录失败 - 账号: {}", account);
                throw new RuntimeException("账号或密码错误");
            }

        } catch (Exception e) {
            if (e.getMessage().contains("账户已被锁定") || e.getMessage().contains("权限不足")) {
                throw e; // 重新抛出特定异常
            }

            log.error("管理后台登录过程中发生系统错误 - 账号: {}", account, e);
            throw new RuntimeException("系统错误，请稍后重试");
        }
    }

    @Override
    public LoginResponse.UserInfo getUserInfo(String platformUserId) {
        if (platformUserId.startsWith("E")) {
            // 员工用户
            Long employeeId = Long.parseLong(platformUserId.substring(1));
            Employee employee = employeeMapper.findByEmployeeIdAndStatus(employeeId, Employee.Status.ACTIVE);
            if (employee != null) {
                return createUserInfo(employee);
            }
        } else if (platformUserId.startsWith("C")) {
            // 客户用户 - 不检查状态
            Long clientId = Long.parseLong(platformUserId.substring(1));
            Client client = clientMapper.findByClientId(clientId);
            if (client != null) {
                return createUserInfo(client);
            }
        }
        
        throw new RuntimeException("用户不存在或已被禁用");
    }

    @Override
    public LoginResponse.UserInfo validateTokenAndGetUser(String token) {
        try {
            Claims claims = jwtUtil.parseToken(token);
            String platformUserId = (String) claims.get("platformUserId");
            return getUserInfo(platformUserId);
        } catch (Exception e) {
            log.error("Token验证失败", e);
            throw new RuntimeException("Token无效或已过期");
        }
    }

    @Override
    public String refreshToken(String refreshToken) {
        try {
            Claims claims = jwtUtil.parseToken(refreshToken);
            String platformUserId = (String) claims.get("platformUserId");
            
            // 验证用户仍然有效
            getUserInfo(platformUserId);
            
            // 生成新的访问令牌
            Map<String, Object> newClaims = new HashMap<>();
            newClaims.put("platformUserId", platformUserId);
            newClaims.put("userType", claims.get("userType"));
            newClaims.put("realName", claims.get("realName"));
            
            return jwtUtil.generateToken(newClaims);
        } catch (Exception e) {
            log.error("刷新Token失败", e);
            throw new RuntimeException("刷新Token失败");
        }
    }

    /**
     * 为员工创建登录响应
     */
    private LoginResponse createLoginResponse(Employee employee) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("platformUserId", "E" + employee.getEmployeeId());
        claims.put("userType", mapEmployeeRole(employee.getRole().toString()));
        claims.put("realName", employee.getName());
        claims.put("email", employee.getEmail());
        claims.put("mobile", employee.getPhone());

        String token = jwtUtil.generateToken(claims);
        Long expiresIn = JwtUtil.getExpirationTime();

        LoginResponse.UserInfo userInfo = createUserInfo(employee);
        
        return LoginResponse.success(token, expiresIn, userInfo);
    }

    /**
     * 为客户创建登录响应
     */
    private LoginResponse createLoginResponse(Client client) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("platformUserId", "C" + client.getClientId());
        claims.put("userType", "client");
        claims.put("realName", client.getName());
        claims.put("email", client.getEmail());
        claims.put("mobile", client.getPhone());

        String token = jwtUtil.generateToken(claims);
        Long expiresIn = JwtUtil.getExpirationTime();

        LoginResponse.UserInfo userInfo = createUserInfo(client);
        
        return LoginResponse.success(token, expiresIn, userInfo);
    }

    /**
     * 创建员工用户信息
     */
    private LoginResponse.UserInfo createUserInfo(Employee employee) {
        return new LoginResponse.UserInfo(
            "E" + employee.getEmployeeId(),
            mapEmployeeRole(employee.getRole().toString()),
            employee.getName(),
            employee.getEmail(),
            employee.getPhone(),
            "active"
        );
    }

    /**
     * 创建客户用户信息
     */
    private LoginResponse.UserInfo createUserInfo(Client client) {
        return new LoginResponse.UserInfo(
            "C" + client.getClientId(),
            "client",
            client.getName(),
            client.getEmail(),
            client.getPhone(),
            mapClientStatus(client.getStatus())
        );
    }

    /**
     * 映射员工角色
     */
    private String mapEmployeeRole(String role) {
        if (role == null) return "employee";
        switch (role.toUpperCase()) {
            case "ADMIN":
                return "admin";
            case "MANAGER":
                return "manager";
            default:
                return "employee";
        }
    }

    /**
     * 映射客户状态
     */
    private String mapClientStatus(String status) {
        if ("审核通过".equals(status) || "已合作".equals(status)) {
            return "active";
        }
        return "inactive";
    }

    /**
     * 验证员工密码并处理密码升级
     * @param employee 员工对象
     * @param rawPassword 原始密码
     * @return 密码是否匹配
     */
    private boolean verifyAndUpgradeEmployeePassword(Employee employee, String rawPassword) {
        if (employee == null || employee.getPassword() == null) {
            log.debug("员工密码验证失败 - 员工对象或密码为空");
            return false;
        }

        log.debug("开始密码匹配验证 - 员工ID: {}, 存储密码长度: {}, 输入密码长度: {}",
            employee.getEmployeeId(), employee.getPassword().length(), rawPassword.length());

        boolean matches = passwordEncoder.matches(rawPassword, employee.getPassword());

        log.debug("密码匹配结果 - 员工ID: {}, 匹配结果: {}", employee.getEmployeeId(), matches);

        if (matches && passwordEncoder.needsUpgrade(employee.getPassword())) {
            log.info("员工密码需要升级 - 员工ID: {}", employee.getEmployeeId());
            // 密码验证成功且需要升级，异步升级密码
            upgradeEmployeePassword(employee, rawPassword);
        }

        return matches;
    }

    /**
     * 验证客户密码并处理密码升级
     * @param client 客户对象
     * @param rawPassword 原始密码
     * @return 密码是否匹配
     */
    private boolean verifyAndUpgradeClientPassword(Client client, String rawPassword) {
        if (client == null || client.getPassword() == null) {
            log.debug("客户密码验证失败 - 客户对象或密码为空");
            return false;
        }

        log.debug("开始密码匹配验证 - 客户ID: {}, 存储密码长度: {}, 输入密码长度: {}",
            client.getClientId(), client.getPassword().length(), rawPassword.length());



        boolean matches = passwordEncoder.matches(rawPassword, client.getPassword());

        log.debug("密码匹配结果 - 客户ID: {}, 匹配结果: {}", client.getClientId(), matches);

        if (matches && passwordEncoder.needsUpgrade(client.getPassword())) {
            log.info("客户密码需要升级 - 客户ID: {}", client.getClientId());
            // 密码验证成功且需要升级，异步升级密码
            upgradeClientPassword(client, rawPassword);
        }

        return matches;
    }

    /**
     * 升级员工密码加密算法
     * @param employee 员工对象
     * @param rawPassword 原始密码
     */
    private void upgradeEmployeePassword(Employee employee, String rawPassword) {
        try {
            String newEncodedPassword = passwordEncoder.encode(rawPassword);
            employee.setPassword(newEncodedPassword);
            employeeMapper.updatePassword(employee.getEmployeeId(), newEncodedPassword);
            log.info("员工密码加密算法已升级 - ID: {}", employee.getEmployeeId());
        } catch (Exception e) {
            log.error("升级员工密码加密算法失败 - ID: {}", employee.getEmployeeId(), e);
        }
    }

    /**
     * 升级客户密码加密算法
     * @param client 客户对象
     * @param rawPassword 原始密码
     */
    private void upgradeClientPassword(Client client, String rawPassword) {
        try {
            String newEncodedPassword = passwordEncoder.encode(rawPassword);
            client.setPassword(newEncodedPassword);
            clientMapper.updatePassword(client.getClientId(), newEncodedPassword);
            log.info("客户密码加密算法已升级 - ID: {}", client.getClientId());
        } catch (Exception e) {
            log.error("升级客户密码加密算法失败 - ID: {}", client.getClientId(), e);
        }
    }

    /**
     * 为管理员创建登录响应
     * @param employee 员工对象
     * @return 管理员登录响应
     */
    private LoginResponse createAdminLoginResponse(Employee employee) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("platformUserId", "E" + employee.getEmployeeId());
        claims.put("userType", "admin");
        claims.put("realName", employee.getName());
        claims.put("email", employee.getEmail());
        claims.put("mobile", employee.getPhone());

        String token = jwtUtil.generateToken(claims);
        Long expiresIn = JwtUtil.getExpirationTime();

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
            "E" + employee.getEmployeeId(),
            "admin",
            employee.getName(),
            employee.getEmail(),
            employee.getPhone(),
            "active"
        );

        return LoginResponse.success(token, expiresIn, userInfo);
    }
}
