{"name": "cllcn-platform-user", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3002", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview --port 3002", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.6.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.10", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-vue": "^8.6.0", "lucide-vue-next": "^0.525.0", "mitt": "^3.0.1", "pinia": "^3.0.3", "radix-vue": "^1.9.17", "reka-ui": "^2.4.1", "tailwind-merge": "^3.3.1", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue-sonner": "^2.0.1", "zod": "^3.25.76"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.4", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}