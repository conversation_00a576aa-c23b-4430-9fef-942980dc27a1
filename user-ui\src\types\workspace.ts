/**
 * 工作台相关类型定义
 * 
 * @description 工作台功能模块的TypeScript类型定义
 * <AUTHOR>
 * @date 2025-07-25 15:30:00 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */

/**
 * 工作台菜单项接口
 * 支持多级嵌套菜单结构
 */
export interface WorkspaceMenuItem {
  /** 菜单标题 */
  title: string
  /** 路由路径 */
  href: string
  /** 图标名称（来自lucide-vue-next） */
  icon: string
  /** 子菜单项（可选） */
  children?: WorkspaceMenuItem[]
  /** 是否需要权限验证 */
  requiresAuth?: boolean
  /** 菜单描述（可选） */
  description?: string
}

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  IN_TRANSIT = 'in_transit',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

/**
 * 订单信息接口
 */
export interface Order {
  id: string
  orderNumber: string
  status: OrderStatus
  origin: string
  destination: string
  createTime: string
  estimatedDelivery: string
  totalAmount: number
  currency: string
}

/**
 * 账单状态枚举
 */
export enum BillStatus {
  UNPAID = 'unpaid',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled'
}

/**
 * 账单信息接口
 */
export interface Bill {
  id: string
  billNumber: string
  status: BillStatus
  amount: number
  currency: string
  dueDate: string
  createTime: string
  orderId: string
}

/**
 * 发票状态枚举
 */
export enum InvoiceStatus {
  PENDING = 'pending',
  ISSUED = 'issued',
  SENT = 'sent',
  CANCELLED = 'cancelled'
}

/**
 * 发票信息接口
 */
export interface Invoice {
  id: string
  invoiceNumber: string
  status: InvoiceStatus
  amount: number
  currency: string
  issueDate: string
  billId: string
  downloadUrl?: string
}

/**
 * 消息类型枚举
 */
export enum MessageType {
  SYSTEM = 'system',
  ORDER = 'order',
  BILL = 'bill',
  INVOICE = 'invoice',
  PROMOTION = 'promotion'
}

/**
 * 消息信息接口
 */
export interface Message {
  id: string
  type: MessageType
  title: string
  content: string
  isRead: boolean
  createTime: string
  relatedId?: string
}

/**
 * 联系人信息接口
 */
export interface Contact {
  id: string
  name: string
  email: string
  phone: string
  company: string
  position: string
  createTime: string
}

/**
 * 优惠券状态枚举
 */
export enum CouponStatus {
  AVAILABLE = 'available',
  USED = 'used',
  EXPIRED = 'expired'
}

/**
 * 优惠券信息接口
 */
export interface Coupon {
  id: string
  name: string
  type: string
  discount: number
  status: CouponStatus
  expiryDate: string
  minAmount?: number
  description: string
}
