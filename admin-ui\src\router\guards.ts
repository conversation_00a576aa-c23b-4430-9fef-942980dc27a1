/**
 * 路由守卫
 * 
 * @description 路由权限控制和导航守卫
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */

import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import type { PermissionCheckResult } from './types'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElLoading } from 'element-plus'
import { storage, token } from '@/utils'

/**
 * 路由守卫类
 */
export class RouteGuards {
  /**
   * 前置守卫 - 权限验证
   */
  static async beforeEach(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ): Promise<void> {
    try {
      // 设置页面标题
      if (to.meta?.title) {
        document.title = `${to.meta.title} - CLLCN管理后台`
      }

      // 显示页面加载状态
      const loadingInstance = RouteGuards.showPageLoading()

      // 检查是否需要认证
      if (to.meta?.requiresAuth !== false) {
        const authResult = await RouteGuards.checkAuthentication(to)

        if (!authResult.isAuthenticated) {
          RouteGuards.hidePageLoading(loadingInstance)

          if (authResult.reason === 'token_expired') {
            ElMessage.warning('登录已过期，请重新登录')
          } else if (authResult.reason === 'no_token') {
            ElMessage.warning('请先登录')
          }

          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }

        // 检查权限
        const permissionResult = RouteGuards.checkPermissions(to)
        if (!permissionResult.hasPermission) {
          RouteGuards.hidePageLoading(loadingInstance)

          // 更友好的权限错误提示
          const missingItems = [
            ...(permissionResult.missingPermissions || []),
            ...(permissionResult.missingRoles || [])
          ]

          ElMessage.error(`访问被拒绝：缺少必要权限 (${missingItems.join(', ')})`)

          if (permissionResult.redirectTo) {
            next(permissionResult.redirectTo)
          } else {
            next('/403')
          }
          return
        }
      }

      // 处理外部链接
      if (to.meta?.external) {
        RouteGuards.hidePageLoading(loadingInstance)
        window.open(to.path, to.meta.target || '_blank')
        next(false)
        return
      }

      RouteGuards.hidePageLoading(loadingInstance)
      next()
    } catch (error) {
      console.error('路由守卫错误:', error)
      ElMessage.error('页面加载失败，请稍后重试')
      next(false)
    }
  }

  /**
   * 后置守卫 - 页面加载完成处理
   */
  static afterEach(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized
  ): void {
    // 更新面包屑
    RouteGuards.updateBreadcrumbs(to)
    
    // 添加到标签页
    RouteGuards.addToTabs(to)
    
    // 页面加载完成，隐藏loading
    const loadingElement = document.getElementById('app-loading')
    if (loadingElement) {
      loadingElement.style.display = 'none'
    }
  }

  /**
   * 检查路由权限
   */
  static checkPermissions(route: RouteLocationNormalized): PermissionCheckResult {
    const userStore = useUserStore()
    const { permissions: userPermissions, roles: userRoles } = userStore
    
    const requiredPermissions = route.meta?.permissions || []
    const requiredRoles = route.meta?.roles || []

    // 检查权限
    const missingPermissions = requiredPermissions.filter(
      permission => !userPermissions.includes(permission)
    )

    // 检查角色
    const missingRoles = requiredRoles.filter(
      role => !userRoles.includes(role)
    )

    const hasPermission = missingPermissions.length === 0 && missingRoles.length === 0

    return {
      hasPermission,
      missingPermissions: missingPermissions.length > 0 ? missingPermissions : undefined,
      missingRoles: missingRoles.length > 0 ? missingRoles : undefined,
      redirectTo: !hasPermission ? '/403' : undefined
    }
  }

  /**
   * 更新面包屑
   */
  private static updateBreadcrumbs(route: RouteLocationNormalized): void {
    // 这里可以实现面包屑更新逻辑
    // 可以通过store或者事件总线来通知面包屑组件更新
    console.log('更新面包屑:', route.path)
  }

  /**
   * 添加到标签页
   */
  private static addToTabs(route: RouteLocationNormalized): void {
    // 这里可以实现标签页添加逻辑
    // 可以通过store来管理标签页状态
    if (!route.meta?.hideInMenu && route.meta?.title) {
      console.log('添加标签页:', route.meta.title)
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(permission: string): boolean {
    const userStore = useUserStore()
    return userStore.permissions.includes(permission)
  }

  /**
   * 检查用户是否有指定角色
   */
  static hasRole(role: string): boolean {
    const userStore = useUserStore()
    return userStore.roles.includes(role)
  }

  /**
   * 检查用户是否有任一权限
   */
  static hasAnyPermission(permissions: string[]): boolean {
    const userStore = useUserStore()
    return permissions.some(permission => userStore.permissions.includes(permission))
  }

  /**
   * 检查用户是否有任一角色
   */
  static hasAnyRole(roles: string[]): boolean {
    const userStore = useUserStore()
    return roles.some(role => userStore.roles.includes(role))
  }

  /**
   * 检查用户认证状态
   */
  static async checkAuthentication(route: RouteLocationNormalized): Promise<{
    isAuthenticated: boolean
    reason?: 'no_token' | 'token_expired' | 'token_invalid' | 'user_not_found'
  }> {
    const userStore = useUserStore()

    // 检查是否有token
    const userToken = storage.getLocal('admin_token')
    if (!userToken) {
      return { isAuthenticated: false, reason: 'no_token' }
    }

    // 检查token是否过期
    if (token.isTokenExpired(userToken)) {
      // 尝试刷新token
      try {
        await userStore.refreshToken()
        return { isAuthenticated: true }
      } catch (error) {
        console.error('Token刷新失败:', error)
        // 清除过期的认证信息
        storage.removeLocal('admin_token')
        storage.removeLocal('admin_refresh_token')
        storage.removeLocal('admin_user_info')
        return { isAuthenticated: false, reason: 'token_expired' }
      }
    }

    // 检查用户信息是否存在
    if (!userStore.userInfo) {
      try {
        // 尝试获取用户信息
        await userStore.getCurrentUser()
        return { isAuthenticated: true }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return { isAuthenticated: false, reason: 'user_not_found' }
      }
    }

    return { isAuthenticated: true }
  }

  /**
   * 显示页面加载状态
   */
  static showPageLoading() {
    return ElLoading.service({
      lock: true,
      text: '页面加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }

  /**
   * 隐藏页面加载状态
   */
  static hidePageLoading(loadingInstance?: any): void {
    if (loadingInstance) {
      loadingInstance.close()
    }
  }

  /**
   * 检查路由是否为公开路由
   */
  static isPublicRoute(route: RouteLocationNormalized): boolean {
    const publicRoutes = ['/login', '/404', '/403']
    return publicRoutes.includes(route.path) || route.meta?.requiresAuth === false
  }

  /**
   * 获取用户友好的权限名称
   */
  static getPermissionDisplayName(permission: string): string {
    const permissionMap: Record<string, string> = {
      'system:read': '系统查看',
      'system:write': '系统管理',
      'user:read': '用户查看',
      'user:write': '用户管理',
      'order:read': '订单查看',
      'order:write': '订单管理',
      'finance:read': '财务查看',
      'finance:write': '财务管理',
      'report:read': '报表查看',
      'report:write': '报表管理'
    }

    return permissionMap[permission] || permission
  }
}
