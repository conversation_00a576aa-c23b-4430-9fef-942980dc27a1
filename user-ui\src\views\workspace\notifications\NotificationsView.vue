<!--
  消息通知页面
  
  @description 消息通知页面，支持消息筛选、搜索和状态管理
  <AUTHOR>
  @date 2025-07-25 16:14:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="notifications-page">
    <!-- 页面标题和操作区 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-foreground">消息通知</h1>
        <p class="text-muted-foreground mt-1">查看和管理您的消息通知</p>
      </div>
      <div class="flex space-x-2 mt-4 sm:mt-0">
        <Button variant="outline" @click="markAllAsRead">
          <CheckCircle class="h-4 w-4 mr-2" />
          全部已读
        </Button>
        <Button variant="outline" @click="clearAllMessages">
          <Trash2 class="h-4 w-4 mr-2" />
          清空消息
        </Button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Mail class="h-6 w-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">总消息</p>
              <p class="text-2xl font-bold">{{ totalMessages }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
              <AlertCircle class="h-6 w-6 text-red-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">未读消息</p>
              <p class="text-2xl font-bold">{{ unreadMessages }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <CheckCircle class="h-6 w-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">已读消息</p>
              <p class="text-2xl font-bold">{{ readMessages }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <Star class="h-6 w-6 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">重要消息</p>
              <p class="text-2xl font-bold">{{ importantMessages }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 搜索和筛选区 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 搜索框 -->
          <div class="md:col-span-2">
            <Label for="search">搜索消息</Label>
            <div class="relative mt-1">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="搜索消息标题或内容..."
                class="pl-10"
              />
            </div>
          </div>

          <!-- 类型筛选 -->
          <div>
            <Label for="type">消息类型</Label>
            <Select v-model="selectedType">
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="选择类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部类型</SelectItem>
                <SelectItem value="order">订单通知</SelectItem>
                <SelectItem value="bill">账单通知</SelectItem>
                <SelectItem value="system">系统通知</SelectItem>
                <SelectItem value="promotion">营销推广</SelectItem>
                <SelectItem value="invoice">发票通知</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 状态筛选 -->
          <div>
            <Label for="status">阅读状态</Label>
            <Select v-model="selectedStatus">
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="unread">未读</SelectItem>
                <SelectItem value="read">已读</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 消息列表 -->
    <Card>
      <CardContent class="p-0">
        <!-- 桌面端列表 -->
        <div class="hidden md:block">
          <div class="space-y-1">
            <div
              v-for="message in paginatedMessages"
              :key="message.id"
              class="flex items-start space-x-4 p-4 border-b hover:bg-accent/50 transition-colors cursor-pointer"
              :class="{ 'bg-accent/20': !message.isRead }"
              @click="toggleReadStatus(message)"
            >
              <!-- 状态指示器 -->
              <div class="flex-shrink-0 mt-2">
                <div
                  class="w-3 h-3 rounded-full"
                  :class="message.isRead ? 'bg-muted-foreground' : 'bg-primary'"
                ></div>
              </div>

              <!-- 消息图标 -->
              <div class="flex-shrink-0 mt-1">
                <div class="p-2 rounded-lg" :class="getMessageIconBg(message.type)">
                  <component :is="getMessageIcon(message.type)" class="h-5 w-5" :class="getMessageIconColor(message.type)" />
                </div>
              </div>

              <!-- 消息内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between mb-1">
                  <h3 class="text-sm font-medium text-foreground truncate">{{ message.title }}</h3>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getMessageTypeVariant(message.type)">
                      {{ getMessageTypeText(message.type) }}
                    </Badge>
                    <span class="text-xs text-muted-foreground">{{ formatDate(message.createTime) }}</span>
                  </div>
                </div>
                <p class="text-sm text-muted-foreground line-clamp-2">{{ message.content }}</p>
              </div>

              <!-- 操作按钮 -->
              <div class="flex-shrink-0">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" @click.stop>
                      <MoreHorizontal class="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem @click.stop="toggleReadStatus(message)">
                      {{ message.isRead ? '标为未读' : '标为已读' }}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      v-if="message.relatedId"
                      @click.stop="viewRelated(message)"
                    >
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem @click.stop="deleteMessage(message.id)" class="text-destructive">
                      删除消息
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>

        <!-- 移动端卡片列表 -->
        <div class="md:hidden p-4 space-y-4">
          <Card 
            v-for="message in paginatedMessages" 
            :key="message.id" 
            class="p-4 cursor-pointer transition-colors"
            :class="{ 'border-primary/50 bg-accent/20': !message.isRead }"
            @click="toggleReadStatus(message)"
          >
            <div class="flex items-start space-x-3">
              <!-- 状态和图标 -->
              <div class="flex-shrink-0">
                <div class="relative">
                  <div class="p-2 rounded-lg" :class="getMessageIconBg(message.type)">
                    <component :is="getMessageIcon(message.type)" class="h-4 w-4" :class="getMessageIconColor(message.type)" />
                  </div>
                  <div
                    v-if="!message.isRead"
                    class="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"
                  ></div>
                </div>
              </div>

              <!-- 消息内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between mb-1">
                  <Badge :variant="getMessageTypeVariant(message.type)" class="text-xs">
                    {{ getMessageTypeText(message.type) }}
                  </Badge>
                  <span class="text-xs text-muted-foreground">{{ formatDate(message.createTime) }}</span>
                </div>
                <h3 class="text-sm font-medium text-foreground mb-1">{{ message.title }}</h3>
                <p class="text-sm text-muted-foreground line-clamp-2">{{ message.content }}</p>
                
                <!-- 移动端操作按钮 -->
                <div class="flex space-x-2 mt-3">
                  <Button variant="outline" size="sm" @click.stop="toggleReadStatus(message)">
                    {{ message.isRead ? '标为未读' : '标为已读' }}
                  </Button>
                  <Button 
                    v-if="message.relatedId"
                    variant="outline" 
                    size="sm"
                    @click.stop="viewRelated(message)"
                  >
                    查看详情
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>

        <!-- 分页 -->
        <div class="p-4 border-t">
          <div class="flex items-center justify-between">
            <div class="text-sm text-muted-foreground">
              显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredMessages.length) }} 
              共 {{ filteredMessages.length }} 条消息
            </div>
            <div class="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === 1"
                @click="currentPage--"
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 空状态 -->
    <div v-if="filteredMessages.length === 0" class="text-center py-12">
      <Mail class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <h3 class="text-lg font-medium text-foreground mb-2">暂无消息</h3>
      <p class="text-muted-foreground">
        {{ searchQuery || selectedType || selectedStatus ? '没有找到匹配的消息' : '您还没有收到任何消息' }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { 
  Search, 
  CheckCircle, 
  Trash2, 
  Mail, 
  AlertCircle, 
  Star, 
  MoreHorizontal,
  Package,
  CreditCard,
  Settings,
  Gift,
  FileText
} from 'lucide-vue-next'
import { mockMessages } from '@/data/mockData'
import { MessageType } from '@/types/workspace'
import type { Message } from '@/types/workspace'

// 响应式数据
const searchQuery = ref('')
const selectedType = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const messages = ref([...mockMessages])

// 计算属性
const filteredMessages = computed(() => {
  let filtered = messages.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(message => 
      message.title.toLowerCase().includes(query) ||
      message.content.toLowerCase().includes(query)
    )
  }

  // 类型筛选
  if (selectedType.value) {
    filtered = filtered.filter(message => message.type === selectedType.value)
  }

  // 状态筛选
  if (selectedStatus.value) {
    const isRead = selectedStatus.value === 'read'
    filtered = filtered.filter(message => message.isRead === isRead)
  }

  return filtered.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
})

const totalPages = computed(() => Math.ceil(filteredMessages.value.length / pageSize.value))

const paginatedMessages = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredMessages.value.slice(start, end)
})

// 统计数据
const totalMessages = computed(() => messages.value.length)
const unreadMessages = computed(() => messages.value.filter(m => !m.isRead).length)
const readMessages = computed(() => messages.value.filter(m => m.isRead).length)
const importantMessages = computed(() => messages.value.filter(m => m.type === MessageType.SYSTEM || m.type === MessageType.ORDER).length)

// 方法
const getMessageIcon = (type: MessageType) => {
  switch (type) {
    case MessageType.ORDER: return Package
    case MessageType.BILL: return CreditCard
    case MessageType.SYSTEM: return Settings
    case MessageType.PROMOTION: return Gift
    case MessageType.INVOICE: return FileText
    default: return Mail
  }
}

const getMessageIconBg = (type: MessageType) => {
  switch (type) {
    case MessageType.ORDER: return 'bg-blue-100'
    case MessageType.BILL: return 'bg-green-100'
    case MessageType.SYSTEM: return 'bg-yellow-100'
    case MessageType.PROMOTION: return 'bg-purple-100'
    case MessageType.INVOICE: return 'bg-orange-100'
    default: return 'bg-gray-100'
  }
}

const getMessageIconColor = (type: MessageType) => {
  switch (type) {
    case MessageType.ORDER: return 'text-blue-600'
    case MessageType.BILL: return 'text-green-600'
    case MessageType.SYSTEM: return 'text-yellow-600'
    case MessageType.PROMOTION: return 'text-purple-600'
    case MessageType.INVOICE: return 'text-orange-600'
    default: return 'text-gray-600'
  }
}

const getMessageTypeVariant = (type: MessageType) => {
  switch (type) {
    case MessageType.ORDER: return 'default'
    case MessageType.BILL: return 'secondary'
    case MessageType.SYSTEM: return 'outline'
    case MessageType.PROMOTION: return 'default'
    case MessageType.INVOICE: return 'default'
    default: return 'secondary'
  }
}

const getMessageTypeText = (type: MessageType) => {
  switch (type) {
    case MessageType.ORDER: return '订单'
    case MessageType.BILL: return '账单'
    case MessageType.SYSTEM: return '系统'
    case MessageType.PROMOTION: return '推广'
    case MessageType.INVOICE: return '发票'
    default: return '其他'
  }
}

const formatDate = (dateString: string) => {
  const now = new Date()
  const date = new Date(dateString)
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffInHours < 1) {
    return '刚刚'
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}小时前`
  } else if (diffInHours < 48) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}

const toggleReadStatus = (message: Message) => {
  const index = messages.value.findIndex(m => m.id === message.id)
  if (index > -1) {
    messages.value[index].isRead = !messages.value[index].isRead
  }
}

const deleteMessage = (messageId: string) => {
  const index = messages.value.findIndex(m => m.id === messageId)
  if (index > -1) {
    messages.value.splice(index, 1)
  }
}

const markAllAsRead = () => {
  messages.value.forEach(message => {
    message.isRead = true
  })
}

const clearAllMessages = () => {
  if (confirm('确定要清空所有消息吗？此操作不可恢复。')) {
    messages.value = []
  }
}

const viewRelated = (message: Message) => {
  console.log('查看相关内容:', message.relatedId, message.type)
  // 实际实现中这里会跳转到相关页面
}
</script>

<style scoped>
.notifications-page {
  /* 页面样式 */
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
