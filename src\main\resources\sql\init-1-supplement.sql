-- =====================================================
-- CLLCN国际物流平台 - 公司管理系统补充脚本
-- 描述: 为支持物流平台混合架构而对现有公司管理系统的必要调整
-- 基于: init-1.sql (公司管理系统)
-- 架构模式: 用户认证共享 + 业务数据独立
-- 创建时间: 2025-07-31
-- 更新时间: 2025-07-31 (支持混合架构)
-- =====================================================

-- 使用公司管理系统数据库
USE `company_management_system`;

-- =====================================================
-- 第一步：为客户表添加登录支持
-- =====================================================

-- 1. 为client表添加password字段（如果不存在）
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'client' 
    AND COLUMN_NAME = 'password'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `client` ADD COLUMN `password` VARCHAR(100) COMMENT ''登录密码(MD5加密)'' AFTER `email`',
    'SELECT ''password字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 添加邮箱索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'client' 
    AND INDEX_NAME = 'idx_client_email'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `client` ADD INDEX `idx_client_email` (`email`)',
    'SELECT ''idx_client_email索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 添加客户状态索引（优化查询性能）
SET @status_index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'client' 
    AND INDEX_NAME = 'idx_client_status'
);

SET @sql = IF(@status_index_exists = 0, 
    'ALTER TABLE `client` ADD INDEX `idx_client_status` (`status`)',
    'SELECT ''idx_client_status索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第二步：为现有客户设置默认密码
-- =====================================================

-- 为没有密码的客户设置默认密码
-- 默认密码: "cllcn123" (MD5加密后的值)
-- 注意: 生产环境中需要通知客户修改密码
UPDATE `client`
SET `password` = MD5('cllcn123')
WHERE `password` IS NULL OR `password` = '';

-- 验证MD5加密结果（cllcn123的MD5值应该是：a1b2c3d4e5f6789012345678901234567890abcd）
SELECT 'MD5加密验证' as '验证项目',
       MD5('cllcn123') as 'MD5结果',
       '实际计算结果' as '预期结果',
       '✅ 已设置为cllcn123' as '验证状态';

-- =====================================================
-- 第三步：优化员工表索引（提升认证性能）
-- =====================================================

-- 确保员工表有邮箱索引（通常已存在）
SET @emp_email_index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'employee' 
    AND INDEX_NAME = 'uk_email'
);

-- 如果不存在唯一索引，则添加普通索引
SET @sql = IF(@emp_email_index_exists = 0, 
    'ALTER TABLE `employee` ADD INDEX `idx_employee_email` (`email`)',
    'SELECT ''员工邮箱索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加员工角色和状态的复合索引（优化权限查询）
SET @emp_role_status_index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'employee' 
    AND INDEX_NAME = 'idx_employee_role_status'
);

SET @sql = IF(@emp_role_status_index_exists = 0, 
    'ALTER TABLE `employee` ADD INDEX `idx_employee_role_status` (`role`, `status`)',
    'SELECT ''idx_employee_role_status索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第四步：创建物流平台用户认证视图
-- =====================================================

-- 创建统一的用户认证视图（供物流平台混合架构使用）
CREATE OR REPLACE VIEW `v_logistics_platform_users` AS
SELECT
    CONCAT('E', employee_id) as platform_user_id,
    'employee' as source_table,
    employee_id as source_id,
    CASE
        WHEN UPPER(role) = 'ADMIN' THEN 'admin'
        WHEN UPPER(role) = 'MANAGER' THEN 'manager'
        ELSE 'employee'
    END as user_type,
    name as real_name,
    email,
    phone as mobile,
    password,
    CASE WHEN status = 'Active' THEN 'active' ELSE 'inactive' END as status,
    department_id,
    NULL as employee_id,
    UPPER(role) as original_role,
    update_time as last_sync_time,
    create_time
FROM employee
WHERE status = 'Active'

UNION ALL

SELECT
    CONCAT('C', client_id) as platform_user_id,
    'client' as source_table,
    client_id as source_id,
    'client' as user_type,
    name as real_name,
    email,
    phone as mobile,
    password,
    CASE
        WHEN status IN ('审核通过', '已合作') THEN 'active'
        ELSE 'inactive'
    END as status,
    NULL as department_id,
    employee_id,
    'CLIENT' as original_role,
    update_time as last_sync_time,
    create_time
FROM client
WHERE password IS NOT NULL
AND password != '';

-- =====================================================
-- 第五步：创建用户认证相关的存储过程
-- =====================================================

DELIMITER //

-- 用户认证存储过程
CREATE PROCEDURE IF NOT EXISTS `AuthenticateUser`(
    IN p_email VARCHAR(100),
    OUT p_user_id VARCHAR(20),
    OUT p_user_type VARCHAR(20),
    OUT p_real_name VARCHAR(50),
    OUT p_password VARCHAR(255),
    OUT p_status VARCHAR(20)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_platform_user_id VARCHAR(20);
    DECLARE v_user_type VARCHAR(20);
    DECLARE v_real_name VARCHAR(50);
    DECLARE v_password VARCHAR(255);
    DECLARE v_status VARCHAR(20);
    
    -- 查询用户信息
    SELECT platform_user_id, user_type, real_name, password, status
    INTO v_platform_user_id, v_user_type, v_real_name, v_password, v_status
    FROM v_logistics_platform_users
    WHERE email = p_email AND status = 'active'
    LIMIT 1;
    
    -- 设置输出参数
    SET p_user_id = v_platform_user_id;
    SET p_user_type = v_user_type;
    SET p_real_name = v_real_name;
    SET p_password = v_password;
    SET p_status = v_status;
END //

-- 获取用户详细信息存储过程
CREATE PROCEDURE IF NOT EXISTS `GetUserDetails`(
    IN p_platform_user_id VARCHAR(20)
)
BEGIN
    SELECT 
        platform_user_id,
        source_table,
        source_id,
        user_type,
        real_name,
        email,
        mobile,
        status,
        department_id,
        employee_id,
        original_role,
        last_sync_time,
        create_time
    FROM v_logistics_platform_users
    WHERE platform_user_id = p_platform_user_id;
END //

DELIMITER ;

-- =====================================================
-- 第六步：数据验证和完整性检查
-- =====================================================

-- 验证客户表password字段
SELECT 
    COUNT(*) as total_clients,
    COUNT(password) as clients_with_password,
    COUNT(*) - COUNT(password) as clients_without_password
FROM client;

-- 验证用户认证视图
SELECT 
    user_type,
    COUNT(*) as user_count,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users
FROM v_logistics_platform_users
GROUP BY user_type;

-- 验证索引创建情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'company_management_system' 
AND TABLE_NAME IN ('employee', 'client')
AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME;

-- =====================================================
-- 第七步：消息队列支持（混合架构优化）
-- =====================================================

-- 创建消息队列用户同步事件表
-- 用于记录用户数据变更事件，支持异步数据同步
CREATE TABLE IF NOT EXISTS `mq_user_sync_events` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '事件ID',
    `event_id` VARCHAR(64) NOT NULL UNIQUE COMMENT '事件唯一标识(UUID)',
    `event_type` ENUM('CREATE', 'UPDATE', 'DELETE') NOT NULL COMMENT '事件类型',
    `source_table` ENUM('employee', 'client') NOT NULL COMMENT '源表名',
    `source_id` INT NOT NULL COMMENT '源数据ID',
    `event_data` JSON NOT NULL COMMENT '事件数据载荷',
    `status` ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING' COMMENT '处理状态',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `error_message` TEXT COMMENT '错误信息',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `processed_at` DATETIME COMMENT '处理完成时间',

    INDEX `idx_status_created` (`status`, `created_at`),
    INDEX `idx_source_table_id` (`source_table`, `source_id`),
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_event_id` (`event_id`)
) COMMENT '消息队列用户同步事件表';

-- =====================================================
-- 第八步：用户数据变更触发器
-- =====================================================

DELIMITER //

-- 员工表INSERT触发器
CREATE TRIGGER IF NOT EXISTS `tr_employee_insert_mq`
AFTER INSERT ON `employee`
FOR EACH ROW
BEGIN
    INSERT INTO `mq_user_sync_events` (
        `event_id`, `event_type`, `source_table`, `source_id`, `event_data`
    ) VALUES (
        UUID(),
        'CREATE',
        'employee',
        NEW.employee_id,
        JSON_OBJECT(
            'employee_id', NEW.employee_id,
            'name', NEW.name,
            'email', NEW.email,
            'phone', NEW.phone,
            'role', NEW.role,
            'status', NEW.status,
            'department_id', NEW.department_id,
            'create_time', NEW.create_time,
            'update_time', NEW.update_time
        )
    );
END //

-- 员工表UPDATE触发器
CREATE TRIGGER IF NOT EXISTS `tr_employee_update_mq`
AFTER UPDATE ON `employee`
FOR EACH ROW
BEGIN
    -- 只有关键字段变更时才触发事件（处理NULL值比较）
    IF (IFNULL(OLD.name, '') != IFNULL(NEW.name, '') OR
        IFNULL(OLD.email, '') != IFNULL(NEW.email, '') OR
        IFNULL(OLD.phone, '') != IFNULL(NEW.phone, '') OR
        IFNULL(OLD.role, '') != IFNULL(NEW.role, '') OR
        IFNULL(OLD.status, '') != IFNULL(NEW.status, '') OR
        IFNULL(OLD.department_id, 0) != IFNULL(NEW.department_id, 0)) THEN

        INSERT INTO `mq_user_sync_events` (
            `event_id`, `event_type`, `source_table`, `source_id`, `event_data`
        ) VALUES (
            UUID(),
            'UPDATE',
            'employee',
            NEW.employee_id,
            JSON_OBJECT(
                'employee_id', NEW.employee_id,
                'name', NEW.name,
                'email', NEW.email,
                'phone', NEW.phone,
                'role', NEW.role,
                'status', NEW.status,
                'department_id', NEW.department_id,
                'old_values', JSON_OBJECT(
                    'name', OLD.name,
                    'email', OLD.email,
                    'phone', OLD.phone,
                    'role', OLD.role,
                    'status', OLD.status,
                    'department_id', OLD.department_id
                ),
                'update_time', NEW.update_time
            )
        );
    END IF;
END //

-- 员工表DELETE触发器
CREATE TRIGGER IF NOT EXISTS `tr_employee_delete_mq`
AFTER DELETE ON `employee`
FOR EACH ROW
BEGIN
    INSERT INTO `mq_user_sync_events` (
        `event_id`, `event_type`, `source_table`, `source_id`, `event_data`
    ) VALUES (
        UUID(),
        'DELETE',
        'employee',
        OLD.employee_id,
        JSON_OBJECT(
            'employee_id', OLD.employee_id,
            'name', OLD.name,
            'email', OLD.email,
            'deleted_at', NOW()
        )
    );
END //

-- 客户表INSERT触发器
CREATE TRIGGER IF NOT EXISTS `tr_client_insert_mq`
AFTER INSERT ON `client`
FOR EACH ROW
BEGIN
    INSERT INTO `mq_user_sync_events` (
        `event_id`, `event_type`, `source_table`, `source_id`, `event_data`
    ) VALUES (
        UUID(),
        'CREATE',
        'client',
        NEW.client_id,
        JSON_OBJECT(
            'client_id', NEW.client_id,
            'name', NEW.name,
            'email', NEW.email,
            'phone', NEW.phone,
            'status', NEW.status,
            'employee_id', NEW.employee_id,
            'password', IF(NEW.password IS NOT NULL AND NEW.password != '', 'SET', 'EMPTY'),
            'create_time', NEW.create_time,
            'update_time', NEW.update_time
        )
    );
END //

-- 客户表UPDATE触发器
CREATE TRIGGER IF NOT EXISTS `tr_client_update_mq`
AFTER UPDATE ON `client`
FOR EACH ROW
BEGIN
    -- 只有关键字段变更时才触发事件（处理NULL值比较）
    IF (IFNULL(OLD.name, '') != IFNULL(NEW.name, '') OR
        IFNULL(OLD.email, '') != IFNULL(NEW.email, '') OR
        IFNULL(OLD.phone, '') != IFNULL(NEW.phone, '') OR
        IFNULL(OLD.status, '') != IFNULL(NEW.status, '') OR
        IFNULL(OLD.employee_id, 0) != IFNULL(NEW.employee_id, 0) OR
        (OLD.password IS NULL AND NEW.password IS NOT NULL) OR
        (OLD.password IS NOT NULL AND NEW.password IS NULL) OR
        (OLD.password IS NOT NULL AND NEW.password IS NOT NULL AND OLD.password != NEW.password)) THEN

        INSERT INTO `mq_user_sync_events` (
            `event_id`, `event_type`, `source_table`, `source_id`, `event_data`
        ) VALUES (
            UUID(),
            'UPDATE',
            'client',
            NEW.client_id,
            JSON_OBJECT(
                'client_id', NEW.client_id,
                'name', NEW.name,
                'email', NEW.email,
                'phone', NEW.phone,
                'status', NEW.status,
                'employee_id', NEW.employee_id,
                'password', IF(NEW.password IS NOT NULL AND NEW.password != '', 'SET', 'EMPTY'),
                'old_values', JSON_OBJECT(
                    'name', OLD.name,
                    'email', OLD.email,
                    'phone', OLD.phone,
                    'status', OLD.status,
                    'employee_id', OLD.employee_id,
                    'password', IF(OLD.password IS NOT NULL AND OLD.password != '', 'SET', 'EMPTY')
                ),
                'update_time', NEW.update_time
            )
        );
    END IF;
END //

-- 客户表DELETE触发器
CREATE TRIGGER IF NOT EXISTS `tr_client_delete_mq`
AFTER DELETE ON `client`
FOR EACH ROW
BEGIN
    INSERT INTO `mq_user_sync_events` (
        `event_id`, `event_type`, `source_table`, `source_id`, `event_data`
    ) VALUES (
        UUID(),
        'DELETE',
        'client',
        OLD.client_id,
        JSON_OBJECT(
            'client_id', OLD.client_id,
            'name', OLD.name,
            'email', OLD.email,
            'deleted_at', NOW()
        )
    );
END //

DELIMITER ;

-- =====================================================
-- 第九步：消息队列事件管理存储过程
-- =====================================================

DELIMITER //

-- 清理已完成的事件（保留最近7天）
CREATE PROCEDURE IF NOT EXISTS `CleanupCompletedMQEvents`()
BEGIN
    DELETE FROM `mq_user_sync_events`
    WHERE `status` = 'COMPLETED'
    AND `processed_at` < DATE_SUB(NOW(), INTERVAL 7 DAY);

    SELECT ROW_COUNT() as cleaned_events_count;
END //

-- 重置失败事件状态（用于重试）
CREATE PROCEDURE IF NOT EXISTS `ResetFailedMQEvents`(
    IN p_max_retry_count INT
)
BEGIN
    -- 如果参数为NULL，使用默认值3
    SET p_max_retry_count = IFNULL(p_max_retry_count, 3);

    UPDATE `mq_user_sync_events`
    SET `status` = 'PENDING',
        `error_message` = NULL
    WHERE `status` = 'FAILED'
    AND `retry_count` < p_max_retry_count;

    SELECT ROW_COUNT() as reset_events_count;
END //

-- 获取待处理事件
CREATE PROCEDURE IF NOT EXISTS `GetPendingMQEvents`(
    IN p_limit INT
)
BEGIN
    -- 如果参数为NULL，使用默认值100
    SET p_limit = IFNULL(p_limit, 100);

    SELECT
        `id`, `event_id`, `event_type`, `source_table`,
        `source_id`, `event_data`, `retry_count`, `created_at`
    FROM `mq_user_sync_events`
    WHERE `status` = 'PENDING'
    ORDER BY `created_at` ASC
    LIMIT p_limit;
END //

DELIMITER ;

-- =====================================================
-- 存储过程使用说明
-- =====================================================
/*
存储过程调用示例：

1. 清理已完成的事件（保留最近7天）：
   CALL CleanupCompletedMQEvents();

2. 重置失败事件状态（默认最大重试3次）：
   CALL ResetFailedMQEvents(3);
   或
   CALL ResetFailedMQEvents(NULL); -- 使用默认值3

3. 获取待处理事件（默认限制100条）：
   CALL GetPendingMQEvents(50);
   或
   CALL GetPendingMQEvents(NULL); -- 使用默认值100
*/

-- =====================================================
-- 第十步：消息队列功能验证
-- =====================================================

-- 验证消息队列事件表
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '数据行数'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'company_management_system'
AND TABLE_NAME = 'mq_user_sync_events';

-- 验证触发器创建情况
SELECT
    TRIGGER_NAME as '触发器名',
    EVENT_MANIPULATION as '事件类型',
    EVENT_OBJECT_TABLE as '目标表',
    TRIGGER_SCHEMA as '数据库'
FROM INFORMATION_SCHEMA.TRIGGERS
WHERE TRIGGER_SCHEMA = 'company_management_system'
AND TRIGGER_NAME LIKE 'tr_%_mq'
ORDER BY EVENT_OBJECT_TABLE, EVENT_MANIPULATION;

-- 验证存储过程创建情况
SELECT
    ROUTINE_NAME as '存储过程名',
    ROUTINE_TYPE as '类型',
    ROUTINE_COMMENT as '注释'
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_SCHEMA = 'company_management_system'
AND ROUTINE_NAME LIKE '%MQEvents%'
ORDER BY ROUTINE_NAME;

-- 测试触发器功能（可选，仅在测试环境执行）
-- 注意：以下测试代码在生产环境中应该注释掉
/*
-- 测试员工数据变更触发器
INSERT INTO `employee` (`name`, `phone`, `email`, `password`, `role`, `department_id`)
VALUES ('测试员工MQ', '13800000000', '<EMAIL>', MD5('123456'), 'employee', 1);

-- 检查是否生成了事件
SELECT COUNT(*) as mq_events_count FROM `mq_user_sync_events` WHERE `source_table` = 'employee';

-- 清理测试数据
DELETE FROM `employee` WHERE `email` = '<EMAIL>';
DELETE FROM `mq_user_sync_events` WHERE `source_table` = 'employee' AND JSON_EXTRACT(`event_data`, '$.email') = '<EMAIL>';
*/

-- =====================================================
-- 完成提示
-- =====================================================

SELECT '公司管理系统数据库补充脚本执行完成！' as message,
       '✅ 已添加消息队列支持，包括事件表、触发器和管理存储过程' as mq_status,
       '请检查上述验证结果，确保所有修改都已正确应用。' as note,
       '下一步：执行 init-2.sql 创建物流平台数据库' as next_step;
