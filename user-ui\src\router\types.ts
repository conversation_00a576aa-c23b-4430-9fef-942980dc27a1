/**
 * 路由相关类型定义
 * 
 * @description 路由管理系统的类型定义
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/data_structures_final.md
 * @note 与admin-ui共享相同架构，但独立实现
 */

import type { RouteRecordRaw, RouteLocationNormalized } from 'vue-router'

// 路由元信息
export interface RouteMeta {
  title?: string                    // 页面标题
  icon?: string                    // 菜单图标
  requiresAuth?: boolean           // 是否需要认证
  permissions?: string[]           // 所需权限
  roles?: string[]                // 所需角色
  keepAlive?: boolean             // 是否缓存
  hideInMenu?: boolean            // 是否在菜单中隐藏
  hideInBreadcrumb?: boolean      // 是否在面包屑中隐藏
  activeMenu?: string             // 激活的菜单项
  noCache?: boolean               // 不缓存
  affix?: boolean                 // 固定标签
  badge?: string | number         // 徽章
  order?: number                  // 排序
  external?: boolean              // 外部链接
  target?: '_blank' | '_self'     // 链接打开方式
  showInTabbar?: boolean          // 是否在底部导航显示
  tabbarIcon?: string             // 底部导航图标
}

// 扩展的路由配置
export interface RouteConfig extends Omit<RouteRecordRaw, 'meta' | 'children'> {
  meta?: RouteMeta
  children?: RouteConfig[]
}

// 菜单项结构
export interface MenuItem {
  id: string
  title: string
  path?: string
  icon?: string
  children?: MenuItem[]
  permissions?: string[]
  roles?: string[]
  order?: number
  external?: boolean
  target?: '_blank' | '_self'
  badge?: string | number
  hidden?: boolean
}

// 面包屑项
export interface BreadcrumbItem {
  title: string
  path?: string
  icon?: string
  disabled?: boolean
}

// 标签页项
export interface TabItem {
  name: string
  title: string
  path: string
  icon?: string
  closable?: boolean
  affix?: boolean
}

// 底部导航项
export interface TabbarItem {
  name: string
  title: string
  path: string
  icon: string
  activeIcon?: string
  badge?: string | number
  dot?: boolean
}

// 路由守卫配置
export interface RouteGuardConfig {
  beforeEach?: (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized
  ) => boolean | string | void
  afterEach?: (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized
  ) => void
}

// 权限检查结果
export interface PermissionCheckResult {
  hasPermission: boolean
  missingPermissions?: string[]
  missingRoles?: string[]
  redirectTo?: string
}

// 路由缓存配置
export interface RouteCacheConfig {
  include?: string[]
  exclude?: string[]
  max?: number
}

// 动态路由配置
export interface DynamicRouteConfig {
  routes: RouteConfig[]
  permissions: string[]
  roles: string[]
}
