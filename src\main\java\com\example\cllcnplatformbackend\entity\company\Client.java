package com.example.cllcnplatformbackend.entity.company;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 客户实体类（来自公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Client {

    private Long clientId;
    
    /**
     * 客户姓名
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码（加密）- 新增字段
     */
    private String password;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 地址
     */
    private String address;

    /**
     * 状态：审核通过, 待审核, 审核拒绝, 已合作
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 判断是否为激活状态（审核通过或已合作）
     */
    public boolean isActive() {
        return "审核通过".equals(this.status) || "已合作".equals(this.status);
    }
    
    /**
     * 判断是否有密码（可以登录）
     */
    public boolean hasPassword() {
        return password != null && !password.trim().isEmpty();
    }
    
    /**
     * 获取标准化状态
     * 用于与Employee状态统一
     */
    public String getStandardStatus() {
        return isActive() ? "ACTIVE" : "INACTIVE";
    }
}
