package com.example.cllcnplatformbackend.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 密码加密工具类
 * 支持bcrypt加密和MD5兼容验证，实现密码加密算法的平滑升级
 * 
 * <AUTHOR> Platform
 * @since 2025-08-02
 */
@Slf4j
@Component
public class PasswordEncoder {
    
    private final BCryptPasswordEncoder bcryptEncoder;
    
    public PasswordEncoder() {
        // 使用默认强度(10)的bcrypt编码器
        this.bcryptEncoder = new BCryptPasswordEncoder();
    }
    
    /**
     * 使用bcrypt算法加密密码
     * @param rawPassword 原始密码
     * @return bcrypt加密后的密码
     */
    public String encode(String rawPassword) {
        if (rawPassword == null || rawPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        
        String encoded = bcryptEncoder.encode(rawPassword.trim());
        log.debug("密码已使用bcrypt算法加密");
        return encoded;
    }
    
    /**
     * 验证密码是否匹配
     * 支持bcrypt和MD5两种格式的密码验证，实现向后兼容
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 存储的加密密码
     * @return 密码是否匹配
     */
    public boolean matches(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            return false;
        }
        
        String trimmedRawPassword = rawPassword.trim();
        
        // 首先尝试bcrypt验证
        if (isBcryptHash(encodedPassword)) {
            boolean matches = bcryptEncoder.matches(trimmedRawPassword, encodedPassword);
            log.debug("使用bcrypt算法验证密码: {}", matches ? "成功" : "失败");
            return matches;
        }
        
        // 如果不是bcrypt格式，尝试MD5验证（向后兼容）
        if (isMd5Hash(encodedPassword)) {
            String md5Hash = Md5Util.getMD5(trimmedRawPassword);
            boolean matches = md5Hash.equals(encodedPassword);
            log.debug("使用MD5算法验证密码: {}", matches ? "成功" : "失败");
            return matches;
        }
        
        log.warn("未知的密码加密格式: {}", encodedPassword.substring(0, Math.min(10, encodedPassword.length())) + "...");
        return false;
    }
    
    /**
     * 检查密码是否需要升级加密算法
     * @param encodedPassword 存储的加密密码
     * @return 是否需要升级
     */
    public boolean needsUpgrade(String encodedPassword) {
        if (encodedPassword == null) {
            return true;
        }
        
        // 如果不是bcrypt格式，则需要升级
        return !isBcryptHash(encodedPassword);
    }
    
    /**
     * 判断是否为bcrypt哈希格式
     * bcrypt哈希格式：$2a$10$... 或 $2b$10$... 等
     * @param hash 哈希字符串
     * @return 是否为bcrypt格式
     */
    private boolean isBcryptHash(String hash) {
        if (hash == null || hash.length() < 7) {
            return false;
        }
        
        // bcrypt哈希以$2开头，格式为$2a$10$...或$2b$10$...等
        return hash.startsWith("$2a$") || hash.startsWith("$2b$") || 
               hash.startsWith("$2x$") || hash.startsWith("$2y$");
    }
    
    /**
     * 判断是否为MD5哈希格式
     * MD5哈希格式：32位十六进制字符串
     * @param hash 哈希字符串
     * @return 是否为MD5格式
     */
    private boolean isMd5Hash(String hash) {
        if (hash == null || hash.length() != 32) {
            return false;
        }
        
        // 检查是否为32位十六进制字符串
        return hash.matches("^[a-fA-F0-9]{32}$");
    }
    
    /**
     * 获取密码加密算法类型
     * @param encodedPassword 加密后的密码
     * @return 加密算法类型
     */
    public String getAlgorithmType(String encodedPassword) {
        if (encodedPassword == null) {
            return "UNKNOWN";
        }
        
        if (isBcryptHash(encodedPassword)) {
            return "BCRYPT";
        } else if (isMd5Hash(encodedPassword)) {
            return "MD5";
        } else {
            return "UNKNOWN";
        }
    }
    
    /**
     * 密码强度检查
     * @param rawPassword 原始密码
     * @return 密码是否符合强度要求
     */
    public boolean isPasswordStrong(String rawPassword) {
        if (rawPassword == null || rawPassword.trim().length() < 6) {
            return false;
        }
        
        String password = rawPassword.trim();
        
        // 基本强度要求：至少6位，包含字母和数字
        boolean hasLetter = password.matches(".*[a-zA-Z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        
        return hasLetter && hasDigit;
    }
    
    /**
     * 生成密码强度建议
     * @param rawPassword 原始密码
     * @return 密码强度建议
     */
    public String getPasswordStrengthAdvice(String rawPassword) {
        if (rawPassword == null || rawPassword.trim().isEmpty()) {
            return "密码不能为空";
        }
        
        String password = rawPassword.trim();
        
        if (password.length() < 6) {
            return "密码长度至少需要6位";
        }
        
        if (!password.matches(".*[a-zA-Z].*")) {
            return "密码需要包含字母";
        }
        
        if (!password.matches(".*\\d.*")) {
            return "密码需要包含数字";
        }
        
        if (password.length() >= 8 && password.matches(".*[!@#$%^&*(),.?\":{}|<>].*")) {
            return "密码强度：强";
        } else if (password.length() >= 6) {
            return "密码强度：中等";
        } else {
            return "密码强度：弱";
        }
    }
}
