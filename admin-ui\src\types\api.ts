/**
 * API相关类型定义
 * 
 * @description 统一的API接口类型定义，确保类型安全
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/data_structures_final.md
 */

import type { AxiosRequestConfig, AxiosResponse } from 'axios'

// 统一API响应格式
export interface ApiResponse<T = any> {
  code: number          // 状态码：200成功，其他失败
  data: T              // 响应数据
  message: string      // 响应消息
  timestamp: number    // 时间戳
}

// 分页响应结构
export interface PaginationResponse<T> {
  list: T[]           // 数据列表
  total: number       // 总数量
  page: number        // 当前页码
  pageSize: number    // 每页大小
  totalPages: number  // 总页数
}

// 分页请求参数
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: Record<string, any>
}

// HTTP客户端配置
export interface HttpConfig {
  baseURL: string
  timeout: number
  withCredentials: boolean
  headers?: Record<string, string>
}

// 请求拦截器配置
export interface RequestInterceptorConfig {
  onFulfilled?: (config: AxiosRequestConfig) => AxiosRequestConfig | Promise<AxiosRequestConfig>
  onRejected?: (error: any) => any
}

// 响应拦截器配置
export interface ResponseInterceptorConfig {
  onFulfilled?: (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>
  onRejected?: (error: any) => any
}

// 错误类型
export type ErrorType = 
  | 'network' 
  | 'validation' 
  | 'authentication' 
  | 'authorization' 
  | 'business' 
  | 'system'

// 错误信息
export interface ErrorInfo {
  type: ErrorType
  code: string | number
  message: string
  details?: any
  timestamp: number
  stack?: string
  context?: Record<string, any>
}

// 用户认证相关类型
export interface UserInfo {
  id: string
  username: string
  email: string
  phone?: string
  avatar?: string
  realName?: string
  roles: string[]
  permissions: string[]
  status: 'active' | 'inactive' | 'banned'
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
}

// 登录凭据
export interface LoginCredentials {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  token: string
  refreshToken: string
  expiresIn: number
  userInfo: UserInfo
}

// JWT Token结构
export interface JWTPayload {
  sub: string        // 用户ID
  username: string
  roles: string[]
  iat: number       // 签发时间
  exp: number       // 过期时间
}

// 请求重试配置
export interface RetryConfig {
  retries: number
  retryDelay: number
  retryCondition?: (error: any) => boolean
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求选项
export interface RequestOptions extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  retry?: RetryConfig
}
