package com.example.cllcnplatformbackend.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * MyBatis多数据源配置
 * 支持混合架构：公司管理系统（用户认证）+ 物流平台（业务数据）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-08-01
 */
@Configuration
public class MyBatisDataSourceConfig {

    /**
     * 公司管理系统数据源（主数据源）
     */
    @Primary
    @Bean(name = "companyDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.company")
    public DataSource companyDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    /**
     * 物流平台数据源
     */
    @Bean(name = "logisticsDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.logistics")
    public DataSource logisticsDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    /**
     * 公司管理系统SqlSessionFactory
     */
    @Primary
    @Bean(name = "companySqlSessionFactory")
    public SqlSessionFactory companySqlSessionFactory(@Qualifier("companyDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 设置MyBatis配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setDefaultEnumTypeHandler(org.apache.ibatis.type.EnumTypeHandler.class);
        sessionFactory.setConfiguration(configuration);
        
        // 设置mapper文件位置
        sessionFactory.setMapperLocations(
            new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*Mapper.xml")
        );
        
        // 设置类型别名包
        sessionFactory.setTypeAliasesPackage("com.example.cllcnplatformbackend.entity.company");
        
        return sessionFactory.getObject();
    }

    /**
     * 物流平台SqlSessionFactory
     */
    @Bean(name = "logisticsSqlSessionFactory")
    public SqlSessionFactory logisticsSqlSessionFactory(@Qualifier("logisticsDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 设置MyBatis配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setDefaultEnumTypeHandler(org.apache.ibatis.type.EnumTypeHandler.class);
        sessionFactory.setConfiguration(configuration);
        
        // 设置mapper文件位置
        sessionFactory.setMapperLocations(
            new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*Mapper.xml")
        );
        
        // 设置类型别名包
        sessionFactory.setTypeAliasesPackage("com.example.cllcnplatformbackend.entity.logistics");
        
        return sessionFactory.getObject();
    }

    /**
     * 公司管理系统事务管理器
     */
    @Primary
    @Bean(name = "companyTransactionManager")
    public DataSourceTransactionManager companyTransactionManager(@Qualifier("companyDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 物流平台事务管理器
     */
    @Bean(name = "logisticsTransactionManager")
    public DataSourceTransactionManager logisticsTransactionManager(@Qualifier("logisticsDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 公司管理系统Mapper扫描配置
     */
    @Configuration
    @MapperScan(
        basePackages = "com.example.cllcnplatformbackend.mapper",
        sqlSessionFactoryRef = "companySqlSessionFactory"
    )
    static class CompanyMapperConfig {
    }
}
