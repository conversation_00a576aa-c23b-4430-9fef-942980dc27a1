/**
 * 全局消息提示工具
 * 
 * @description 基于vue-sonner的统一消息提示封装
 * <AUTHOR>
 * @date 2025-08-04 16:30:00 +08:00
 * @reference shadcn-vue sonner组件
 */

import { toast as sonnerToast } from 'vue-sonner'

export interface ToastOptions {
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  cancel?: {
    label: string
    onClick?: () => void
  }
}

/**
 * 统一的消息提示工具
 */
export const toast = {
  /**
   * 成功提示
   */
  success: (message: string, options?: ToastOptions) => {
    return sonnerToast.success(message, {
      duration: options?.duration || 3000,
      description: options?.description,
      action: options?.action,
      cancel: options?.cancel
    })
  },

  /**
   * 错误提示
   */
  error: (message: string, options?: ToastOptions) => {
    return sonnerToast.error(message, {
      duration: options?.duration || 4000,
      description: options?.description,
      action: options?.action,
      cancel: options?.cancel
    })
  },

  /**
   * 警告提示
   */
  warning: (message: string, options?: ToastOptions) => {
    return sonnerToast.warning(message, {
      duration: options?.duration || 4000,
      description: options?.description,
      action: options?.action,
      cancel: options?.cancel
    })
  },

  /**
   * 信息提示
   */
  info: (message: string, options?: ToastOptions) => {
    return sonnerToast.info(message, {
      duration: options?.duration || 3000,
      description: options?.description,
      action: options?.action,
      cancel: options?.cancel
    })
  },

  /**
   * 加载提示
   */
  loading: (message: string, options?: Omit<ToastOptions, 'action'>) => {
    return sonnerToast.loading(message, {
      duration: options?.duration || Infinity,
      description: options?.description
    })
  },

  /**
   * 自定义提示
   */
  custom: (message: string, options?: ToastOptions) => {
    return sonnerToast(message, {
      duration: options?.duration || 3000,
      description: options?.description,
      action: options?.action,
      cancel: options?.cancel
    })
  },

  /**
   * 关闭所有提示
   */
  dismiss: (toastId?: string | number) => {
    return sonnerToast.dismiss(toastId)
  },

  /**
   * Promise提示 - 自动处理loading/success/error状态
   */
  promise: <T>(
    promise: Promise<T>,
    {
      loading: loadingMessage,
      success: successMessage,
      error: errorMessage
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading: loadingMessage,
      success: successMessage,
      error: errorMessage
    })
  }
}

/**
 * 预设的业务场景提示
 */
export const businessToast = {
  /**
   * 登录成功
   */
  loginSuccess: (redirectPath?: string) => {
    return toast.success('登录成功！', {
      description: '正在跳转到工作台...',
      duration: 2000,
      action: redirectPath ? {
        label: '立即查看',
        onClick: () => {
          // 这里需要在调用处传入router实例
          console.log('跳转到:', redirectPath)
        }
      } : undefined
    })
  },

  /**
   * 登录失败
   */
  loginError: (errorType: 'credentials' | 'locked' | 'network' | 'unknown', message?: string) => {
    const errorConfig = {
      credentials: {
        title: '登录失败',
        description: '账号或密码错误，请检查后重试'
      },
      locked: {
        title: '账户已被锁定',
        description: message || '请联系管理员解锁账户',
        action: {
          label: '联系客服',
          onClick: () => console.log('联系客服')
        }
      },
      network: {
        title: '网络连接失败',
        description: '请检查网络连接后重试'
      },
      unknown: {
        title: '登录失败',
        description: message || '未知错误，请稍后重试'
      }
    }

    const config = errorConfig[errorType]
    return toast.error(config.title, {
      description: config.description,
      duration: 4000,
      action: 'action' in config ? config.action : undefined
    })
  },

  /**
   * 操作成功
   */
  operationSuccess: (operation: string, description?: string) => {
    return toast.success(`${operation}成功`, {
      description,
      duration: 2000
    })
  },

  /**
   * 操作失败
   */
  operationError: (operation: string, error?: string) => {
    return toast.error(`${operation}失败`, {
      description: error || '请稍后重试',
      duration: 3000
    })
  },

  /**
   * 数据保存成功
   */
  saveSuccess: (dataType: string = '数据') => {
    return toast.success('保存成功', {
      description: `${dataType}已成功保存`,
      duration: 2000
    })
  },

  /**
   * 数据删除确认
   */
  deleteConfirm: (dataType: string, onConfirm: () => void) => {
    return toast.warning(`确认删除${dataType}？`, {
      description: '此操作不可撤销',
      duration: 5000,
      action: {
        label: '确认删除',
        onClick: onConfirm
      },
      cancel: {
        label: '取消'
      }
    })
  }
}

export default toast
