<!--
  卡券中心页面
  
  @description 优惠券和积分管理
  <AUTHOR>
  @date 2025-07-25 16:05:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="coupons-page">
    <!-- 页面头部 -->
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-foreground">卡券中心</h2>
      <p class="text-muted-foreground mt-1">管理您的优惠券和积分</p>
    </div>

    <!-- 积分和统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <Star class="h-6 w-6 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">积分余额</p>
              <p class="text-2xl font-bold">{{ userPoints }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <Ticket class="h-6 w-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">可用优惠券</p>
              <p class="text-2xl font-bold">{{ availableCoupons }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <CheckCircle class="h-6 w-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">已使用</p>
              <p class="text-2xl font-bold">{{ usedCoupons }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
              <Clock class="h-6 w-6 text-red-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">已过期</p>
              <p class="text-2xl font-bold">{{ expiredCoupons }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 筛选和操作 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex items-center space-x-4">
            <Select v-model="selectedStatus">
              <SelectTrigger class="w-48">
                <SelectValue placeholder="筛选状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="available">可使用</SelectItem>
                <SelectItem value="used">已使用</SelectItem>
                <SelectItem value="expired">已过期</SelectItem>
              </SelectContent>
            </Select>
            <Select v-model="selectedType">
              <SelectTrigger class="w-48">
                <SelectValue placeholder="筛选类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部类型</SelectItem>
                <SelectItem value="满减券">满减券</SelectItem>
                <SelectItem value="折扣券">折扣券</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="flex space-x-2">
            <Button variant="outline">
              <Gift class="h-4 w-4 mr-2" />
              兑换优惠券
            </Button>
            <Button>
              <Plus class="h-4 w-4 mr-2" />
              领取优惠券
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 优惠券列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <Card 
        v-for="coupon in filteredCoupons" 
        :key="coupon.id" 
        class="relative overflow-hidden"
        :class="getCouponCardClass(coupon.status)"
      >
        <!-- 状态标签 -->
        <div class="absolute top-2 right-2">
          <Badge :variant="getCouponStatusVariant(coupon.status)">
            {{ getCouponStatusText(coupon.status) }}
          </Badge>
        </div>

        <CardContent class="p-4">
          <!-- 优惠券头部 -->
          <div class="mb-4">
            <h3 class="font-semibold text-foreground mb-1">{{ coupon.name }}</h3>
            <p class="text-sm text-muted-foreground">{{ coupon.description }}</p>
          </div>

          <!-- 优惠金额/折扣 -->
          <div class="mb-4 text-center">
            <div class="text-3xl font-bold text-primary">
              <span v-if="coupon.type === '满减券'">¥{{ coupon.discount }}</span>
              <span v-else>{{ (coupon.discount * 10).toFixed(1) }}折</span>
            </div>
            <p class="text-sm text-muted-foreground">
              满{{ formatCurrency(coupon.minAmount) }}可用
            </p>
          </div>

          <!-- 有效期 -->
          <div class="mb-4 text-center">
            <p class="text-sm text-muted-foreground">
              有效期至: {{ formatDate(coupon.expiryDate) }}
            </p>
          </div>

          <!-- 操作按钮 -->
          <div class="flex space-x-2">
            <Button 
              v-if="coupon.status === 'available'"
              class="flex-1"
              @click="useCoupon(coupon)"
            >
              立即使用
            </Button>
            <Button 
              v-else-if="coupon.status === 'used'"
              variant="outline" 
              class="flex-1" 
              disabled
            >
              已使用
            </Button>
            <Button 
              v-else
              variant="outline" 
              class="flex-1" 
              disabled
            >
              已过期
            </Button>
            <Button variant="ghost" size="sm" @click="shareCoupon(coupon)">
              <Share class="h-4 w-4" />
            </Button>
          </div>
        </CardContent>

        <!-- 装饰性边框 -->
        <div 
          v-if="coupon.status === 'available'"
          class="absolute inset-0 border-2 border-dashed border-primary/20 rounded-lg pointer-events-none"
        ></div>
      </Card>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredCoupons.length === 0" class="text-center py-12">
      <Ticket class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <h3 class="text-lg font-medium text-foreground mb-2">暂无优惠券</h3>
      <p class="text-muted-foreground mb-4">
        {{ selectedStatus || selectedType ? '没有找到匹配的优惠券' : '您还没有任何优惠券' }}
      </p>
      <Button>
        <Gift class="h-4 w-4 mr-2" />
        去领取优惠券
      </Button>
    </div>

    <!-- 积分兑换对话框 -->
    <Dialog v-model:open="showExchangeDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>积分兑换</DialogTitle>
          <DialogDescription>
            使用积分兑换优惠券
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div class="text-center">
            <p class="text-sm text-muted-foreground">当前积分余额</p>
            <p class="text-2xl font-bold text-primary">{{ userPoints }}</p>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between items-center p-3 border rounded-lg">
              <div>
                <p class="font-medium">满100减10优惠券</p>
                <p class="text-sm text-muted-foreground">需要500积分</p>
              </div>
              <Button size="sm" :disabled="userPoints < 500">
                兑换
              </Button>
            </div>
            <div class="flex justify-between items-center p-3 border rounded-lg">
              <div>
                <p class="font-medium">9折优惠券</p>
                <p class="text-sm text-muted-foreground">需要800积分</p>
              </div>
              <Button size="sm" :disabled="userPoints < 800">
                兑换
              </Button>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showExchangeDialog = false">
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { 
  Star, 
  Ticket, 
  CheckCircle, 
  Clock, 
  Gift, 
  Plus, 
  Share 
} from 'lucide-vue-next'
import { mockCoupons } from '@/data/mockData'
import { CouponStatus } from '@/types/workspace'
import type { Coupon } from '@/types/workspace'

// 响应式数据
const selectedStatus = ref('')
const selectedType = ref('')
const showExchangeDialog = ref(false)
const userPoints = ref(2580)
const coupons = ref([...mockCoupons])

// 计算属性
const filteredCoupons = computed(() => {
  let filtered = coupons.value

  if (selectedStatus.value) {
    filtered = filtered.filter(coupon => coupon.status === selectedStatus.value)
  }

  if (selectedType.value) {
    filtered = filtered.filter(coupon => coupon.type === selectedType.value)
  }

  return filtered
})

const availableCoupons = computed(() => 
  coupons.value.filter(coupon => coupon.status === CouponStatus.AVAILABLE).length
)

const usedCoupons = computed(() => 
  coupons.value.filter(coupon => coupon.status === CouponStatus.USED).length
)

const expiredCoupons = computed(() => 
  coupons.value.filter(coupon => coupon.status === CouponStatus.EXPIRED).length
)

// 方法
const getCouponStatusVariant = (status: CouponStatus) => {
  switch (status) {
    case CouponStatus.AVAILABLE: return 'default'
    case CouponStatus.USED: return 'secondary'
    case CouponStatus.EXPIRED: return 'destructive'
    default: return 'secondary'
  }
}

const getCouponStatusText = (status: CouponStatus) => {
  switch (status) {
    case CouponStatus.AVAILABLE: return '可使用'
    case CouponStatus.USED: return '已使用'
    case CouponStatus.EXPIRED: return '已过期'
    default: return '未知'
  }
}

const getCouponCardClass = (status: CouponStatus) => {
  switch (status) {
    case CouponStatus.AVAILABLE: return 'border-primary/20 hover:shadow-md transition-shadow'
    case CouponStatus.USED: return 'opacity-60'
    case CouponStatus.EXPIRED: return 'opacity-40'
    default: return ''
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const useCoupon = (coupon: Coupon) => {
  console.log('使用优惠券:', coupon.name)
  // 实际实现中这里会跳转到下单页面并应用优惠券
}

const shareCoupon = (coupon: Coupon) => {
  console.log('分享优惠券:', coupon.name)
  // 实际实现中这里会打开分享对话框
}
</script>

<style scoped>
.coupons-page {
  /* 页面样式 */
}
</style>
