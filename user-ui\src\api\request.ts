import axios from 'axios'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import { toast } from 'vue-sonner'
import router from '@/router'
import { storage, token } from '@/utils'

// 刷新token的状态管理
let isRefreshing = false
let failedQueue: Array<{
  resolve: (value?: any) => void
  reject: (reason?: any) => void
}> = []

// 处理队列中的请求
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // 使用/api前缀，通过Vite代理转发到后端
  timeout: 10000,
  // 企业级配置
  withCredentials: false, // 不需要携带cookie，使用JWT
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  async (config) => {
    // 从localStorage获取token
    const userToken = storage.getLocal('user_token')

    if (userToken) {
      // 检查token是否即将过期
      if (token.isTokenExpiringSoon(userToken)) {
        console.log('Token即将过期，尝试刷新...')

        // 如果不是刷新token的请求，且token即将过期，先刷新token
        if (config.url !== '/auth/refresh') {
          try {
            await refreshTokenIfNeeded()
            // 刷新后重新获取token
            const newToken = storage.getLocal('user_token')
            if (newToken) {
              config.headers['Authorization'] = `Bearer ${newToken}`
            }
          } catch (error) {
            console.error('Token刷新失败:', error)
            // 刷新失败，清除token并跳转登录
            clearAuthData()
            return Promise.reject(new Error('Token刷新失败'))
          }
        } else {
          config.headers['Authorization'] = `Bearer ${userToken}`
        }
      } else {
        config.headers['Authorization'] = `Bearer ${userToken}`
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data

    // 如果返回的状态码不是200，说明接口有问题
    if (res.code !== 200) {
      // 401: 未登录或token过期
      if (res.code === 401) {
        return handleUnauthorized(response.config)
      }

      toast.error(res.message || '系统错误')
      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res
    }
  },
  async (error) => {
    const originalRequest = error.config

    // 处理401错误
    if (error.response && error.response.status === 401) {
      return handleUnauthorized(originalRequest)
    }

    // 显示错误信息
    let errorMessage = '系统错误'
    if (error.response) {
      errorMessage = error.response.data?.message || `请求失败 (${error.response.status})`
    } else if (error.request) {
      errorMessage = '无法连接到服务器，请检查网络'
    }

    toast.error(errorMessage)
    return Promise.reject(error)
  }
)

// 处理401未授权错误
const handleUnauthorized = async (originalRequest: AxiosRequestConfig): Promise<any> => {
  // 如果是刷新token的请求失败，直接跳转登录
  if (originalRequest.url === '/auth/refresh') {
    clearAuthData()
    return Promise.reject(new Error('刷新token失败'))
  }

  // 如果已经在刷新token，将请求加入队列
  if (isRefreshing) {
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject })
    }).then(() => {
      // 重新发送原始请求
      const newToken = storage.getLocal('user_token')
      if (newToken && originalRequest.headers) {
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`
      }
      return service(originalRequest)
    })
  }

  // 开始刷新token
  isRefreshing = true

  try {
    await refreshTokenIfNeeded()
    const newToken = storage.getLocal('user_token')

    if (newToken) {
      // 处理队列中的请求
      processQueue(null, newToken)

      // 重新发送原始请求
      if (originalRequest.headers) {
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`
      }
      return service(originalRequest)
    } else {
      throw new Error('刷新token后未获取到新token')
    }
  } catch (error) {
    // 刷新失败，处理队列并清除认证数据
    processQueue(error, null)
    clearAuthData()
    return Promise.reject(error)
  } finally {
    isRefreshing = false
  }
}

// 刷新token
const refreshTokenIfNeeded = async (): Promise<void> => {
  const refreshToken = storage.getLocal('user_refresh_token')

  if (!refreshToken) {
    throw new Error('没有可用的刷新令牌')
  }

  try {
    const response = await axios.post('/api/auth/refresh', {
      refreshToken: refreshToken
    })

    const refreshData = response.data.data

    // 更新token
    storage.setLocal('user_token', refreshData.accessToken)
    console.log('Token自动刷新成功')

  } catch (error) {
    console.error('Token自动刷新失败:', error)
    throw error
  }
}

// 清除认证数据
const clearAuthData = (): void => {
  storage.removeLocal('user_token')
  storage.removeLocal('user_refresh_token')
  storage.removeLocal('user_info')

  toast.error('登录已过期，请重新登录')

  // 避免在登录页面重复跳转
  if (router.currentRoute.value.path !== '/login') {
    router.push('/login')
  }
}

export default service
