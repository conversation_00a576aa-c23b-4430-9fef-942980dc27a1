<!--
  应用根组件

  @description CLLCN国际物流平台用户端根组件
  <AUTHOR>
  @date 2025-07-17 10:30:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<script setup lang="ts">
import { RouterView, useRouter } from 'vue-router'
import { Toaster } from '@/components/ui/sonner'
import LoginDialog from '@/components/auth/LoginDialog.vue'
import RegisterDialog from '@/components/auth/RegisterDialog.vue'
import { useUIStore } from '@/stores/ui'

const router = useRouter()

// 初始化UI store
const uiStore = useUIStore()

// 登录成功处理
const handleLoginSuccess = (userData: any) => {
  console.log('登录成功:', userData)
  uiStore.closeLoginModal()

  // 检查是否需要重定向
  if (uiStore.loginRedirectPath && uiStore.loginRedirectPath !== '/') {
    router.push(uiStore.loginRedirectPath)
  } else {
    // 默认跳转到工作台
    router.push('/workspace')
  }
}

// 注册成功处理
const handleRegisterSuccess = (userData: any) => {
  console.log('注册成功:', userData)
  uiStore.closeRegisterModal()
  // 注册成功后跳转到工作台
  router.push('/workspace')
}
</script>

<template>
  <div id="app">
    <RouterView />
    <Toaster />

    <!-- 全局弹窗 -->
    <LoginDialog
      v-model:open="uiStore.showLoginModal"
      @switch-to-register="uiStore.switchToRegister"
      @login-success="handleLoginSuccess"
    />
    <RegisterDialog
      v-model:open="uiStore.showRegisterModal"
      @switch-to-login="uiStore.switchToLogin"
      @register-success="handleRegisterSuccess"
    />
  </div>
</template>

<style>
/* 全局样式已在 main.css 中定义 */
</style>
