<!--
  运价查询搜索表单组件
  
  @description 提供运价查询的搜索表单功能
  <AUTHOR>
  @date 2025-07-21 09:50:00 +08:00
-->

<template>
  <Card class="w-full">
    <CardContent class="p-6">
      <!-- 服务类型Tabs -->
      <div class="mb-6">
        <Tabs v-model="localServiceType" class="w-full">
          <TabsList class="grid w-full grid-cols-4 h-12 bg-muted">
            <TabsTrigger
              value="sea_lcl"
              class="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Package class="w-4 h-4 mr-2" />
              海运拼箱
            </TabsTrigger>
            <TabsTrigger
              value="sea_fcl"
              class="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Ship class="w-4 h-4 mr-2" />
              海运整箱
            </TabsTrigger>
            <TabsTrigger
              value="air"
              class="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Plane class="w-4 h-4 mr-2" />
              空运
            </TabsTrigger>
            <TabsTrigger
              value="rail_lcl"
              class="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              <Train class="w-4 h-4 mr-2" />
              铁路拼箱
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      <!-- 搜索表单 -->
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 起运港 -->
          <div class="space-y-2">
            <Label for="origin" class="text-sm font-medium flex items-center">
              <MapPin class="w-4 h-4 mr-1 text-blue-600" />
              起运港
            </Label>
            <Select v-model="localForm.origin">
              <SelectTrigger id="origin" class="w-full h-11">
                <SelectValue placeholder="请选择起运港" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="port in displayOriginPorts"
                  :key="port.code"
                  :value="port.name"
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <span class="font-medium">{{ port.name }}</span>
                      <span class="ml-2 text-xs text-muted-foreground">{{ port.country }}</span>
                    </div>
                    <span v-if="port.popular" class="text-xs text-orange-500 font-medium">热门</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 目的港 -->
          <div class="space-y-2">
            <Label for="destination" class="text-sm font-medium flex items-center">
              <Navigation class="w-4 h-4 mr-1 text-green-600" />
              目的港
            </Label>
            <Select v-model="localForm.destination">
              <SelectTrigger id="destination" class="w-full h-11">
                <SelectValue placeholder="请选择目的港" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="port in displayDestinationPorts"
                  :key="port.code"
                  :value="port.name"
                >
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <span class="font-medium">{{ port.name }}</span>
                      <span class="ml-2 text-xs text-muted-foreground">{{ port.country }}</span>
                    </div>
                    <span v-if="port.popular" class="text-xs text-orange-500 font-medium">热门</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 货好时间 -->
          <div class="space-y-2">
            <Label for="cargoDate" class="text-sm font-medium flex items-center">
              <Calendar class="w-4 h-4 mr-1 text-orange-600" />
              货好时间
            </Label>
            <DatePicker
              v-model="localForm.cargoReadyDate"
              placeholder="请选择货好时间"
              class="w-full h-11"
            />
          </div>

          <!-- 查询按钮 -->
          <div class="flex items-end">
            <Button
              type="submit"
              class="w-1/2 h-11 bg-gray-900 hover:bg-gray-800 text-white font-medium"
              :disabled="loading || !canSubmit"
            >
              <Search class="w-4 h-4 mr-2" />
              {{ loading ? '查询中...' : '查运价' }}
            </Button>
          </div>
        </div>

      </form>
    </CardContent>
  </Card>
</template>

<script setup>
import { computed, watch } from 'vue'
import {
  Search, MapPin, Navigation, Calendar,
  Ship, Package, Plane, Train
} from 'lucide-vue-next'

// UI组件
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePicker } from '@/components/ui/date-picker'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      origin: '',
      destination: '',
      cargoReadyDate: '',
      serviceType: 'sea_lcl'
    })
  },
  serviceType: {
    type: String,
    default: 'sea_lcl'
  },
  originPorts: {
    type: Array,
    default: () => []
  },
  destinationPorts: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'update:serviceType', 'submit'])

// 本地状态
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const localServiceType = computed({
  get: () => props.serviceType,
  set: (value) => emit('update:serviceType', value)
})

// 计算属性
const canSubmit = computed(() => {
  return localForm.value.origin && localForm.value.destination
})

// 显示推荐的热门港口（优先显示热门港口，限制10个）
const displayOriginPorts = computed(() => {
  const popularPorts = props.originPorts.filter(port => port.popular)
  const otherPorts = props.originPorts.filter(port => !port.popular)
  return [...popularPorts, ...otherPorts].slice(0, 10)
})

const displayDestinationPorts = computed(() => {
  const popularPorts = props.destinationPorts.filter(port => port.popular)
  const otherPorts = props.destinationPorts.filter(port => !port.popular)
  return [...popularPorts, ...otherPorts].slice(0, 10)
})

// 方法
const handleSubmit = () => {
  if (canSubmit.value) {
    emit('submit')
  }
}

// 监听服务类型变化，更新表单
watch(() => props.serviceType, (newType) => {
  localForm.value = {
    ...localForm.value,
    serviceType: newType
  }
})
</script>
