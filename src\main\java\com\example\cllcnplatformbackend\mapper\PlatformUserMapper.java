package com.example.cllcnplatformbackend.mapper;

import com.example.cllcnplatformbackend.entity.PlatformUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 平台统一用户Mapper
 * 使用 v_platform_users 视图进行统一用户查询
 * 
 * <AUTHOR>
 * @date 2025-08-02 12:10:00 +08:00
 * @reference 基于 project_document/architecture/data_structures_final.md
 */
@Mapper
public interface PlatformUserMapper {

    /**
     * 根据邮箱查找用户（包含密码）
     * @param email 邮箱
     * @return 平台用户信息
     */
    PlatformUser findByEmailWithPassword(@Param("email") String email);

    /**
     * 根据手机号查找用户（包含密码）
     * @param mobile 手机号
     * @return 平台用户信息
     */
    PlatformUser findByMobileWithPassword(@Param("mobile") String mobile);

    /**
     * 根据平台用户ID查找用户
     * @param platformUserId 平台用户ID
     * @return 平台用户信息
     */
    PlatformUser findByPlatformUserId(@Param("platformUserId") String platformUserId);

    /**
     * 根据平台用户ID查找用户（包含密码）
     * @param platformUserId 平台用户ID
     * @return 平台用户信息
     */
    PlatformUser findByPlatformUserIdWithPassword(@Param("platformUserId") String platformUserId);
}
