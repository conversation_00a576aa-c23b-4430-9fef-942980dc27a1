package com.example.cllcnplatformbackend.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * ThreadLocal工具类
 * 用于在当前线程中存储和获取用户信息
 *
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
public class ThreadLocalUtil {

    /**
     * 存储用户信息的ThreadLocal，支持key-value存储
     */
    private static final ThreadLocal<Map<String, Object>> THREAD_LOCAL = new ThreadLocal<>();
    
    /**
     * 设置当前线程的键值对
     *
     * @param key 键
     * @param value 值
     */
    public static void set(String key, Object value) {
        Map<String, Object> map = THREAD_LOCAL.get();
        if (map == null) {
            map = new HashMap<>();
            THREAD_LOCAL.set(map);
        }
        map.put(key, value);
    }

    /**
     * 获取当前线程的指定键的值
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        Map<String, Object> map = THREAD_LOCAL.get();
        return map != null ? map.get(key) : null;
    }

    /**
     * 获取当前线程的指定键的值（指定类型）
     *
     * @param key 键
     * @param clazz 类型
     * @param <T> 泛型类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key, Class<T> clazz) {
        Object value = get(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 移除当前线程的指定键
     *
     * @param key 键
     */
    public static void remove(String key) {
        Map<String, Object> map = THREAD_LOCAL.get();
        if (map != null) {
            map.remove(key);
        }
    }

    /**
     * 清除当前线程的所有数据
     */
    public static void clear() {
        THREAD_LOCAL.remove();
    }

    /**
     * 判断当前线程是否有指定键的值
     *
     * @param key 键
     * @return 是否有值
     */
    public static boolean hasValue(String key) {
        Map<String, Object> map = THREAD_LOCAL.get();
        return map != null && map.containsKey(key);
    }

    /**
     * 判断当前线程是否有任何数据
     *
     * @return 是否有数据
     */
    public static boolean hasAnyValue() {
        Map<String, Object> map = THREAD_LOCAL.get();
        return map != null && !map.isEmpty();
    }
}
