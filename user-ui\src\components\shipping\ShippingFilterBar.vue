<!--
  运价查询筛选栏组件
  
  @description 提供运价结果的筛选和排序功能
  <AUTHOR>
  @date 2025-07-21 09:55:00 +08:00
-->

<template>
  <Card class="w-full">
    <CardContent class="p-4">
      <div class="flex flex-wrap items-center gap-4">
        <!-- 结果统计 -->
        <div class="flex items-center space-x-2">
          <Package class="w-4 h-4 text-muted-foreground" />
          <span class="text-sm font-medium">
            共 <span class="text-primary font-semibold">{{ totalResults }}</span> 条结果
          </span>
        </div>
        
        <!-- 分隔线 -->
        <Separator orientation="vertical" class="h-6" />
        
        <!-- 船司筛选 -->
        <div class="flex items-center space-x-2">
          <Label class="text-sm font-medium whitespace-nowrap">船司:</Label>
          <Select v-model="localFilters.carrier" @update:model-value="handleFilterChange">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="全部" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem
                v-for="carrier in availableCarriers"
                :key="carrier"
                :value="carrier"
              >
                {{ carrier }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <!-- 只看直达 -->
        <div class="flex items-center space-x-2">
          <Checkbox 
            id="directOnly" 
            v-model:checked="localFilters.directOnly"
            @update:checked="handleFilterChange"
          />
          <Label for="directOnly" class="text-sm font-medium cursor-pointer">
            <Route class="w-4 h-4 inline mr-1" />
            只看直达
          </Label>
        </div>
        
        <!-- 分隔线 -->
        <Separator orientation="vertical" class="h-6" />
        
        <!-- 价格排序 -->
        <div class="flex items-center space-x-2">
          <Label class="text-sm font-medium whitespace-nowrap">排序:</Label>
          <div class="flex flex-wrap gap-2">
            <Button
              v-for="sortOption in sortOptions"
              :key="sortOption.value"
              :variant="localFilters.sortBy === sortOption.value ? 'default' : 'outline'"
              size="sm"
              @click="setSortBy(sortOption.value)"
              class="text-xs h-8 px-3"
            >
              <TrendingUp class="w-3 h-3 mr-1" />
              {{ sortOption.label }}
            </Button>
          </div>
        </div>
        
        <!-- 清除筛选 -->
        <div class="ml-auto">
          <Button 
            variant="ghost" 
            size="sm"
            @click="clearFilters"
            class="text-xs"
          >
            <RotateCcw class="w-3 h-3 mr-1" />
            清除筛选
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup>
import { computed } from 'vue'
import { 
  Package, Route, TrendingUp, RotateCcw
} from 'lucide-vue-next'

// UI组件
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'

// Props
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({
      carrier: '',
      directOnly: false,
      sortBy: ''
    })
  },
  totalResults: {
    type: Number,
    default: 0
  },
  availableCarriers: {
    type: Array,
    default: () => ['COSCO', 'MSC', 'EVERGREEN', 'HAPAG-LLOYD', 'MAERSK']
  },
  sortOptions: {
    type: Array,
    default: () => [
      { label: '20GP低价', value: 'price_20gp' },
      { label: '40GP低价', value: 'price_40gp' },
      { label: '40HQ低价', value: 'price_40hq' },
      { label: '时效优先', value: 'transit_time' }
    ]
  }
})

// Emits
const emit = defineEmits(['update:filters', 'filterChange'])

// 本地状态
const localFilters = computed({
  get: () => props.filters,
  set: (value) => emit('update:filters', value)
})

// 方法
const handleFilterChange = () => {
  // 将"all"转换为空字符串
  const filters = { ...localFilters.value }
  if (filters.carrier === 'all') {
    filters.carrier = ''
  }
  emit('update:filters', filters)
  emit('filterChange')
}

const setSortBy = (sortBy) => {
  const newFilters = {
    ...localFilters.value,
    sortBy: localFilters.value.sortBy === sortBy ? '' : sortBy
  }
  localFilters.value = newFilters
  emit('filterChange')
}

const clearFilters = () => {
  localFilters.value = {
    carrier: '',
    directOnly: false,
    sortBy: ''
  }
  emit('filterChange')
}
</script>
