package com.example.cllcnplatformbackend.config;

import com.example.cllcnplatformbackend.interceptor.JwtAuthInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {
    
    private final JwtAuthInterceptor jwtAuthInterceptor;
    
    @Value("${app.upload.path}")
    private String uploadPath;
    
    @Value("${app.upload.url-prefix}")
    private String urlPrefix;
    
    /**
     * 添加拦截器
     * 优化后的精确路径控制，仅保留必要的白名单路径
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtAuthInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                        "/auth/login",          // 客户端登录接口
                        "/auth/admin/login",    // 管理后台登录接口
                        "/auth/register",       // 注册接口
                        "/auth/unlock",         // 临时解锁接口（调试用）
                        "/favicon.ico",         // 网站图标
                        "/error",              // 错误页面
                        urlPrefix + "/**"       // 静态资源文件访问路径
                );
    }
    
    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 文件上传路径映射
        registry.addResourceHandler(urlPrefix + "/**")
                .addResourceLocations("file:" + uploadPath + "/");
    }
    
    /**
     * 配置跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
