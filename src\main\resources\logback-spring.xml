<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 引入Spring Boot默认配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 定义日志文件路径 -->
    <property name="LOG_FILE" value="${LOG_FILE:-logs/cllcn-logistics-dev.log}"/>
    
    <!-- 开发环境配置 -->
    <springProfile name="!prod">
        <!-- 控制台输出 - 简洁模式 -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-30.30logger{29}){cyan} %clr(:){faint} %m%n%wEx</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        
        <!-- 文件输出 -->
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_FILE}</file>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>50MB</maxFileSize>
                <maxHistory>7</maxHistory>
                <totalSizeCap>500MB</totalSizeCap>
            </rollingPolicy>
        </appender>
        
        <!-- 根日志配置 -->
        <root level="WARN">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </root>
        
        <!-- 应用日志 -->
        <logger name="com.example.cllcnplatformbackend" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </logger>
        
        <!-- 框架日志优化 -->
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.springframework.boot" level="WARN"/>
        <logger name="org.springframework.security" level="ERROR"/>
        <logger name="org.springframework.web" level="ERROR"/>
        <logger name="org.springframework.data" level="ERROR"/>
        
        <!-- Hibernate日志优化 -->
        <logger name="org.hibernate" level="ERROR"/>
        <logger name="org.hibernate.SQL" level="OFF"/>
        <logger name="org.hibernate.type" level="OFF"/>
        <logger name="org.hibernate.tool" level="OFF"/>
        <logger name="org.hibernate.orm" level="ERROR"/>
        
        <!-- 数据库连接池日志优化 -->
        <logger name="com.zaxxer.hikari" level="WARN"/>
        
        <!-- Tomcat日志优化 -->
        <logger name="org.apache.catalina" level="ERROR"/>
        <logger name="org.apache.coyote" level="ERROR"/>
        <logger name="org.apache.tomcat" level="ERROR"/>
    </springProfile>
    
    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <!-- 生产环境只输出到文件 -->
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_FILE}</file>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>5GB</totalSizeCap>
            </rollingPolicy>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
        
        <logger name="com.example.cllcnplatformbackend" level="INFO"/>
    </springProfile>
</configuration>
