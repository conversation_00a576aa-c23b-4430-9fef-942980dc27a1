-- 在创建数据库之前，先删除数据库
DROP DATABASE IF EXISTS `company_management_system`;

-- 创建公司管理系统数据库
CREATE DATABASE IF NOT EXISTS `company_management_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用公司管理系统数据库
USE `company_management_system`;

-- 创建部门表（已优化为多负责人架构）
CREATE TABLE IF NOT EXISTS `department`
(
    `department_id`          INT         NOT NULL AUTO_INCREMENT,                                        -- 部门ID
    `department_name`        VARCHAR(50) NOT NULL,                                                       -- 部门名称
    `department_description` VARCHAR(255),                                                               -- 部门描述
    `parent_department_id`   INT,                                                                        -- 上级部门ID
    `status`                 VARCHAR(10) NOT NULL DEFAULT 'Active',                                      -- 状态（Active/Inactive）
    `create_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`department_id`),
    FOREIGN KEY (`parent_department_id`) REFERENCES `department` (`department_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
-- 部门表

-- 创建职位表
CREATE TABLE IF NOT EXISTS `position`
(
    `position_id`          INT         NOT NULL AUTO_INCREMENT,                                        -- 职位ID
    `position_name`        VARCHAR(50) NOT NULL,                                                       -- 职位名称
    `position_description` VARCHAR(255),                                                               -- 职位描述
    `department_id`        INT,                                                                        -- 所属部门ID
    `status`               VARCHAR(10) NOT NULL DEFAULT 'Active',                                      -- 状态（Active/Inactive）
    `create_time`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`          DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`position_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
-- 职位表

-- 创建员工表
CREATE TABLE IF NOT EXISTS `employee`
(
    `employee_id`     INT          NOT NULL AUTO_INCREMENT,                                        -- 员工ID
    `name`            VARCHAR(50)  NOT NULL,                                                       -- 姓名
    `email`           VARCHAR(100),                                                                -- 邮箱（登录账号）
    `password`        VARCHAR(100) NOT NULL,                                                       -- 密码（MD5加密）
    `entry_date`      DATE         NOT NULL,                                                       -- 入职时间
    `exit_date`       DATE         NULL,                                                           -- 离职时间
    `id_card`         VARCHAR(18)  NOT NULL,                                                       -- 身份证号
    `department_id`   INT,                                                                         -- 部门ID
    `position_id`     INT,                                                                         -- 职位ID
    `logistics_route` VARCHAR(100),                                                                -- 所属物流航线
    `status`          VARCHAR(10)  NOT NULL DEFAULT 'Active',                                      -- 工作状态
    `role`            VARCHAR(20)  NOT NULL DEFAULT 'employee',                                    -- 角色（admin/manager/employee）
    `create_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`employee_id`),
    UNIQUE KEY `uk_email` (`email`),
    FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`),
    FOREIGN KEY (`position_id`) REFERENCES `position` (`position_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- =====================================================
-- 部门多负责人关联表（支持一个部门配置多个负责人）
-- 创建时间: 2025-07-14 09:53:52 +08:00
-- =====================================================
CREATE TABLE IF NOT EXISTS `department_leader`
(
    `id`            INT      NOT NULL AUTO_INCREMENT COMMENT '关联记录ID',
    `department_id` INT      NOT NULL COMMENT '部门ID',
    `employee_id`   INT      NOT NULL COMMENT '员工ID（负责人）',
    `leader_role`   VARCHAR(50)       DEFAULT 'PRIMARY' COMMENT '负责人角色：PRIMARY-主要负责人，DEPUTY-副负责人，ASSISTANT-协助负责人',
    `start_date`    DATE     NOT NULL DEFAULT (CURDATE()) COMMENT '任职开始日期',
    `end_date`      DATE              DEFAULT NULL COMMENT '任职结束日期（NULL表示当前在职）',
    `is_active`     BOOLEAN  NOT NULL DEFAULT TRUE COMMENT '是否激活状态',
    `remark`        VARCHAR(255)      DEFAULT '' COMMENT '备注信息',
    `create_time`   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_department_id` (`department_id`),
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_leader_role` (`leader_role`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_date_range` (`start_date`, `end_date`),
    INDEX `idx_dept_employee` (`department_id`, `employee_id`),
    INDEX `idx_dept_active` (`department_id`, `is_active`),
    INDEX `idx_employee_active` (`employee_id`, `is_active`),

    -- 防止重复添加同一员工为同一部门的活跃负责人
    UNIQUE KEY `uk_dept_employee_active` (`department_id`, `employee_id`, `is_active`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT = '部门负责人关联表';

-- 创建兼容性视图（便于现有代码的平滑过渡）
CREATE OR REPLACE VIEW `department_with_primary_leader` AS
SELECT d.department_id,
       d.department_name,
       d.department_description,
       d.parent_department_id,
       d.status,
       d.create_time,
       d.update_time,
       dl.employee_id as primary_leader_id,
       e.name         as primary_leader_name
FROM department d
         LEFT JOIN department_leader dl ON d.department_id = dl.department_id
    AND dl.leader_role = 'PRIMARY'
    AND dl.is_active = TRUE
         LEFT JOIN employee e ON dl.employee_id = e.employee_id;

-- 创建常用查询存储过程（MySQL兼容版本）
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `GetDepartmentLeaders`(
    IN dept_id INT,
    IN include_inactive TINYINT
)
BEGIN
    -- 如果include_inactive为NULL，默认设为0（FALSE）
    IF include_inactive IS NULL THEN
        SET include_inactive = 0;
    END IF;

    SELECT dl.id,
           dl.department_id,
           d.department_name,
           dl.employee_id,
           e.name  as leader_name,
           e.email as leader_email,
           dl.leader_role,
           dl.start_date,
           dl.end_date,
           dl.is_active,
           dl.remark,
           dl.create_time,
           dl.update_time
    FROM department_leader dl
             INNER JOIN department d ON dl.department_id = d.department_id
             INNER JOIN employee e ON dl.employee_id = e.employee_id
    WHERE dl.department_id = dept_id
      AND (include_inactive = 1 OR dl.is_active = TRUE)
    ORDER BY CASE dl.leader_role
                 WHEN 'PRIMARY' THEN 1
                 WHEN 'DEPUTY' THEN 2
                 WHEN 'ASSISTANT' THEN 3
                 ELSE 4
                 END,
             dl.start_date DESC;
END //

CREATE PROCEDURE IF NOT EXISTS `GetEmployeeDepartments`(
    IN emp_id INT,
    IN include_inactive TINYINT
)
BEGIN
    -- 如果include_inactive为NULL，默认设为0（FALSE）
    IF include_inactive IS NULL THEN
        SET include_inactive = 0;
    END IF;

    SELECT dl.id,
           dl.department_id,
           d.department_name,
           d.department_description,
           dl.leader_role,
           dl.start_date,
           dl.end_date,
           dl.is_active,
           dl.remark
    FROM department_leader dl
             INNER JOIN department d ON dl.department_id = d.department_id
    WHERE dl.employee_id = emp_id
      AND (include_inactive = 1 OR dl.is_active = TRUE)
    ORDER BY dl.start_date DESC;
END //
DELIMITER ;

-- 创建业绩表
CREATE TABLE IF NOT EXISTS `performance`
(
    `id`                    INT            NOT NULL AUTO_INCREMENT, -- 业绩记录ID
    `employee_id`           INT            NOT NULL,                -- 员工ID
    `date`                  VARCHAR(7)     NOT NULL,                -- 日期（年月）
    `estimated_performance` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 预估业绩
    `actual_performance`    DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 实际业绩
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 创建工资表
CREATE TABLE IF NOT EXISTS `salary`
(
    `id`                       INT            NOT NULL AUTO_INCREMENT, -- 工资记录ID
    `employee_id`              INT            NOT NULL,                -- 员工ID
    `date`                     VARCHAR(7)     NOT NULL,                -- 日期（年月）
    `basic_salary`             DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 基本工资
    `performance_bonus`        DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 奖金
    `full_attendance_bonus`    DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 全勤奖金
    -- 业务操作奖金
    `business_operation_bonus` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 业务操作奖金
    -- 实得金额
    `sum_salary`               DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 实得金额
    -- 请假金额
    `leave_deduction`          DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 请假扣款
    -- 扣借款
    `deduction`                DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 扣款
    -- 迟到缺卡
    `late_deduction`           DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 迟到缺卡扣款
    -- 社保个人部分
    `social_security_personal` DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 社保个人部分
    -- 公积金
    `provident_fund`           DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 公积金
    -- 代扣代缴个税
    `tax`                      DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 个税
    -- 水电费
    `water_electricity_fee`    DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 水电费
    -- 实发工资
    `actual_salary`            DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 实发工资
    -- 报销
    `reimbursement`            DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 报销
    -- 私账
    `private_account`          DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 私账
    -- 合计
    `total_salary`             DECIMAL(12, 2) NOT NULL DEFAULT 0,      -- 合计
    -- 备注
    `remark`                   VARCHAR(255)   NOT NULL DEFAULT '',     -- 备注
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 客户表
CREATE TABLE IF NOT EXISTS `client`
(
    `client_id`   INT          NOT NULL AUTO_INCREMENT,                                        -- 客户ID
    `name`        VARCHAR(50)  NOT NULL,                                                       -- 客户名称
    `employee_id` INT,                                                                         -- 员工ID
    `email`       VARCHAR(100) NOT NULL,                                                       -- 客户邮箱
    `phone`       VARCHAR(20)  NOT NULL,                                                       -- 客户电话
    `create_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`client_id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 客户表添加客户分类（海运、散货、空运、快递等）与合作状态（未审核，审核中、审核通过，已拒绝）
ALTER TABLE `client`
    ADD COLUMN `category` VARCHAR(20) NOT NULL DEFAULT '海运' AFTER `employee_id`,
#     审核状态
    ADD COLUMN `status`   VARCHAR(20) NOT NULL DEFAULT '未审核' AFTER `category`;

alter table `client`
#     客户合作状态（报价中，已合作）
    ADD COLUMN `client_status` VARCHAR(20) NOT NULL DEFAULT '报价中' AFTER `status`;


-- 创建职位与部门的关联表
-- 使用反引号（`）来转义可能与关键字冲突的表名和列名
# use company_management_system;
CREATE TABLE IF NOT EXISTS `position_department`
(
    `position_id`   INT NOT NULL,
    `department_id` INT NOT NULL,
    `create_time`   DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`position_id`, `department_id`),
    -- 确保引用的表名 `position` 和 `department` 也被反引号括起来
    CONSTRAINT `fk_position_department_position` FOREIGN KEY (`position_id`) REFERENCES `position` (`position_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_position_department_department` FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE CASCADE
);
#
# -- 迁移现有职位的部门关系到新表
# -- 同样，在 INSERT 语句中也使用反引号
# INSERT INTO `position_department` (`position_id`, `department_id`, `create_time`)
# SELECT `position_id`, `department_id`, NOW()
# FROM `position`
# WHERE `department_id` IS NOT NULL;
# #
# # ALTER TABLE `position`
# #     DROP FOREIGN KEY `position_ibfk_1`;
# # 然后删除列
# ALTER TABLE `position`
#     DROP COLUMN `department_id`;

-- 创建备用金表
CREATE TABLE IF NOT EXISTS `petty_cash`
(
    `id`          INT            NOT NULL AUTO_INCREMENT,                                        -- 备用金ID
    `employee_id` INT            NOT NULL,                                                       -- 员工ID
    `purpose`     VARCHAR(255)   NOT NULL,                                                       -- 用途
    `amount`      DECIMAL(12, 2) NOT NULL,                                                       -- 金额
    `status`      VARCHAR(20)    NOT NULL DEFAULT '审核中',                                      -- 状态（审核中、已审核、已拒绝）
    `create_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- 客户表添加操作时间与国籍字段
ALTER TABLE `client`
    ADD COLUMN `operation_time` DATETIME AFTER `status`,
    ADD COLUMN `nationality`    VARCHAR(20) NOT NULL AFTER `operation_time`;

-- 修改客户表中电话的长度为100
ALTER TABLE `client`
    MODIFY COLUMN `phone` VARCHAR(100) NOT NULL;

-- 添加员工表可访问菜单ID字段，在role字段后面
ALTER TABLE `employee`
    ADD COLUMN `accessible_menu_ids` JSON AFTER `role`;

-- 客户表添加联系人与备注字段
ALTER TABLE `client`
    ADD COLUMN `contact_person` VARCHAR(50)  DEFAULT '' AFTER `name`, -- 联系人
    ADD COLUMN `remark`         VARCHAR(255) DEFAULT '' AFTER `phone`;
-- 备注

-- 客户表新增拒绝备注字段
ALTER TABLE `client`
    ADD COLUMN `reject_remark` VARCHAR(255) DEFAULT '' AFTER `remark`;

-- 员工表添加手机号字段在name字段后面
ALTER TABLE `employee`
    ADD COLUMN `phone` VARCHAR(20) AFTER `name`;


-- 删除员工表的status字段
ALTER TABLE `employee`
    DROP COLUMN `status`;
-- 重新添加status字段
ALTER TABLE `employee`
    ADD COLUMN `status` VARCHAR(10) NOT NULL DEFAULT 'Active' AFTER `logistics_route`;

-- 备用金表添加date年月字段
ALTER TABLE `petty_cash`
    ADD COLUMN `date` VARCHAR(7) NOT NULL AFTER `employee_id`;

-- 创建部门日常开销表
CREATE TABLE IF NOT EXISTS `department_expense`
(
    `id`            INT            NOT NULL AUTO_INCREMENT,                                        -- 开销ID
    `department_id` INT            NOT NULL,                                                       -- 部门ID
    `expense_date`  DATE           NOT NULL,                                                       -- 开销日期 (格式 YYYY-MM-DD)
    `item_name`     VARCHAR(255)   NOT NULL,                                                       -- 项目名称
    `amount`        DECIMAL(12, 2) NOT NULL DEFAULT 0.00,                                          -- 金额
    `remark`        VARCHAR(255)   NOT NULL DEFAULT '',                                            -- 备注
    `create_time`   DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`   DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`id`),
    FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='部门日常开销表';

-- 创建员工其他费用表
CREATE TABLE IF NOT EXISTS `employee_other_expense`
(
    `id`           INT            NOT NULL AUTO_INCREMENT,                                        -- 费用ID
    `employee_id`  INT            NOT NULL,                                                       -- 员工ID
    `expense_date` DATE           NOT NULL,                                                       -- 费用日期 (格式 YYYY-MM-DD)
    `item_name`    VARCHAR(255)   NOT NULL,                                                       -- 项目名称
    `amount`       DECIMAL(12, 2) NOT NULL DEFAULT 0.00,                                          -- 金额
    `remark`       VARCHAR(255)   NOT NULL DEFAULT '',                                            -- 备注
    `create_time`  DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,                             -- 创建时间
    `update_time`  DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (`id`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='员工其他费用表';

-- 创建通知规则定义表
CREATE TABLE IF NOT EXISTS `notification_rules`
(
    `rule_id`                       INT AUTO_INCREMENT PRIMARY KEY,
    `rule_name`                     VARCHAR(100) NOT NULL UNIQUE COMMENT '规则名称，例如: LOW_PERFORMANCE_LAST_MONTH',
    `description`                   VARCHAR(255) COMMENT '规则描述',
    `target_position_name`          VARCHAR(50) COMMENT '目标职位名称，例如 "业务员", 为空则可能适用于所有职位或通过其他逻辑判断',
    `condition_type`                VARCHAR(50)  NOT NULL COMMENT '条件类型，用于后端逻辑识别，例如: PERFORMANCE_VS_SALARY, NEW_CLIENTS_LAST_MONTH, NEW_CLIENTS_THIS_MONTH_AFTER_15TH',
    `message_template_cn`           VARCHAR(500) NOT NULL COMMENT '中文通知消息模板，可使用占位符如 {userName}, {actualPerformance}, {calculatedSalary}',
    `default_show_again_after_days` INT          NOT NULL COMMENT '默认再次提示间隔天数 (例如 7天, 3天)',
    `priority`                      INT      DEFAULT 10 COMMENT '优先级，数字越小越高',
    `is_active`                     BOOLEAN  DEFAULT TRUE COMMENT '规则是否激活',
    `create_time`                   DATETIME DEFAULT CURRENT_TIMESTAMP,
    `update_time`                   DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='通知规则定义表';

-- 插入通知规则示例数据
INSERT INTO `notification_rules` (`rule_name`, `description`, `target_position_name`, `condition_type`,
                                  `message_template_cn`, `default_show_again_after_days`, `is_active`)
VALUES ('LOW_PERFORMANCE_LAST_MONTH', '上月业绩低于上月应得工资', '业务员', 'PERFORMANCE_VS_SALARY',
        '您好{userName}，上个月自己工资都没有挣回来，请注意，继续努力哦！',
        30, TRUE),
       ('NO_NEW_CLIENTS_LAST_MONTH', '上月无新审批通过客户', '业务员', 'NEW_CLIENTS_LAST_MONTH',
        '您好{userName}，您上个月没有新客户，请努力寻找新客户哦！', 30, TRUE),
       ('NO_NEW_CLIENTS_THIS_MONTH_MID', '本月已过15号仍无新客户', '业务员', 'NEW_CLIENTS_THIS_MONTH_AFTER_15TH',
        '您好{userName}，这个月时间已过半，已经过去15天啦，请抓紧开发新客户哦！', 3, TRUE);

-- 创建员工通知记录表
CREATE TABLE IF NOT EXISTS `employee_notifications`
(
    `notification_id`       BIGINT AUTO_INCREMENT PRIMARY KEY,
    `employee_id`           INT           NOT NULL COMMENT '员工ID',
    `rule_id`               INT           NULL COMMENT '关联的通知规则ID (如果使用 notification_rules 表)',
    `notification_type`     VARCHAR(100)  NOT NULL COMMENT '通知类型 (如果未使用rules表，则直接存储类型key)',
    `title_cn`              VARCHAR(100)  NOT NULL DEFAULT '系统提醒' COMMENT '中文通知标题',
    `message_cn`            VARCHAR(1000) NOT NULL COMMENT '中文通知内容',
    `generated_at`          DATETIME               DEFAULT CURRENT_TIMESTAMP COMMENT '通知生成时间',
    `last_shown_at`         DATETIME      NULL COMMENT '上次弹窗显示时间',
    `dismissed_at`          DATETIME      NULL COMMENT '用户点击"我知道了"的时间',
    `show_again_after_days` INT COMMENT '本次我知道了之后，下次提示的间隔天数（可以基于规则默认值，或特定情况调整）',
    `next_prompt_date`      DATE          NULL COMMENT '根据dismissed_at和show_again_after_days计算得出的下次应提示日期',
    `is_read_in_bell`       BOOLEAN                DEFAULT FALSE COMMENT '在铃铛中是否已读',
    `status`                VARCHAR(20)            DEFAULT 'ACTIVE' COMMENT '状态: ACTIVE, DISMISSED, ARCHIVED',
    `create_time`           DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,
    FOREIGN KEY (`rule_id`) REFERENCES `notification_rules` (`rule_id`) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='员工通知记录表';


INSERT INTO `notification_rules`
(`rule_name`, `description`, `target_position_name`, `condition_type`, `message_template_cn`,
 `default_show_again_after_days`, `priority`, `is_active`)
VALUES ('SYSTEM_WELCOME_MESSAGE', '新用户欢迎通知', NULL, 'ALWAYS_TRIGGER',
        '尊敬的 {userName}，欢迎您加入我们的大家庭！希望您在这里工作愉快。', 3650, 100, TRUE);

-- 创建销售日报表
CREATE TABLE IF NOT EXISTS `sales_daily_report`
(
    `id`                         BIGINT                      NOT NULL AUTO_INCREMENT COMMENT '日报ID',
    `employee_id`                INT                         NOT NULL COMMENT '员工ID',
    `report_date`                DATE                        NOT NULL COMMENT '日报日期',

    -- 统计字段（系统自动计算）
    `yearly_new_clients`         INT                         NOT NULL DEFAULT 0 COMMENT '年度新客户总数',
    `monthly_new_clients`        INT                         NOT NULL DEFAULT 0 COMMENT '当月新客户总数',
    `days_since_last_new_client` INT                         NOT NULL DEFAULT 0 COMMENT '距离上次出新客户天数',

    -- 客户选择字段（JSON格式存储客户ID数组）
    `inquiry_clients`            JSON COMMENT '询价客户ID列表',
    `shipping_clients`           JSON COMMENT '出货客户ID列表',
    `key_development_clients`    JSON COMMENT '重点开发客户ID列表',

    -- 评估字段
    `responsibility_level`       ENUM ('优秀', '中等', '差') NOT NULL COMMENT '责任心评级',

    -- 工作检查清单（JSON格式存储）
    `end_of_day_checklist`       JSON COMMENT '下班准备工作检查清单',

    -- 文本输入字段
    `daily_results`              TEXT COMMENT '今日效果',
    `meeting_report`             TEXT COMMENT '会议报告',
    `work_diary`                 TEXT COMMENT '工作日记',

    -- 领导评价字段
    `manager_evaluation`         TEXT COMMENT '领导评价',
    `evaluation_time`            DATETIME COMMENT '评价时间',
    `evaluator_id`               INT COMMENT '评价人ID',

    -- 系统字段
    `create_time`                DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_employee_date` (`employee_id`, `report_date`),
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_report_date` (`report_date`),
    INDEX `idx_employee_date` (`employee_id`, `report_date`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售日报表';

/*-- 添加销售日报表的领导评价相关字段（如果不存在）
-- 这些字段用于支持领导对员工日报的评价功能
ALTER TABLE `sales_daily_report`
    ADD COLUMN `manager_evaluation` TEXT COMMENT '领导评价',
    ADD COLUMN `evaluation_time`    DATETIME COMMENT '评价时间',
    ADD COLUMN `evaluator_id`       INT COMMENT '评价人ID';
*/

-- 创建推广表（包含富文本编辑器和附件支持）
-- 注意：推广的所属部门通过author_id动态关联到员工的当前部门，不再存储固定的department_id
CREATE TABLE IF NOT EXISTS `promotion`
(
    `id`              BIGINT                                        NOT NULL AUTO_INCREMENT COMMENT '推广ID',
    `title`           VARCHAR(100)                                  NOT NULL COMMENT '推广标题',
    `content`         LONGTEXT                                      NOT NULL COMMENT '推广内容（支持富文本HTML，最多50000字符）',
    `content_type`    ENUM ('TEXT', 'HTML')                         NOT NULL DEFAULT 'TEXT' COMMENT '内容类型：TEXT-纯文本，HTML-富文本',
    `attachments`     JSON COMMENT '附件文件信息列表（JSON格式）',
    `content_summary` VARCHAR(500) COMMENT '内容摘要（从富文本中提取的纯文本，用于搜索和列表展示）',
    `images`          JSON COMMENT '推广图片URL列表（JSON格式）',
    `author_id`       INT                                           NOT NULL COMMENT '发布人员工ID（推广所属部门通过此字段动态关联）',
    `status`          ENUM ('待审核', '审核中', '已通过', '已拒绝') NOT NULL DEFAULT '待审核' COMMENT '审核状态',
    `submit_time`     DATETIME COMMENT '提交审核时间',
    `audit_time`      DATETIME COMMENT '审核完成时间',
    `auditor_id`      INT COMMENT '审核人员工ID（部门负责人）',
    `reject_reason`   VARCHAR(500) COMMENT '拒绝理由',
    `create_time`     DATETIME                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     DATETIME                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    FOREIGN KEY (`author_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,
    FOREIGN KEY (`auditor_id`) REFERENCES `employee` (`employee_id`) ON DELETE SET NULL,

    -- 索引优化
    INDEX `idx_author_id` (`author_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_author_status` (`author_id`, `status`),
    INDEX `idx_content_type` (`content_type`),
    INDEX `idx_content_summary` (`content_summary`(100))
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='推广信息表';

-- =====================================================
-- 推广附件详情表
-- 描述: 用于详细管理推广附件信息
-- =====================================================

-- 创建推广附件详情表
CREATE TABLE IF NOT EXISTS `promotion_attachments`
(
    `id`             BIGINT       NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `promotion_id`   BIGINT       NOT NULL COMMENT '推广ID',
    `file_name`      VARCHAR(255) NOT NULL COMMENT '原始文件名',
    `file_path`      VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    `file_size`      BIGINT       NOT NULL COMMENT '文件大小（字节）',
    `file_type`      VARCHAR(100) NOT NULL COMMENT '文件MIME类型',
    `file_extension` VARCHAR(20)  NOT NULL COMMENT '文件扩展名',
    `upload_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    `uploader_id`    INT          NOT NULL COMMENT '上传者员工ID',
    `download_count` INT          NOT NULL DEFAULT 0 COMMENT '下载次数',
    `is_deleted`     BOOLEAN      NOT NULL DEFAULT FALSE COMMENT '是否已删除',
    `create_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    FOREIGN KEY (`promotion_id`) REFERENCES `promotion` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`uploader_id`) REFERENCES `employee` (`employee_id`) ON DELETE RESTRICT,

    -- 索引优化
    INDEX `idx_promotion_id` (`promotion_id`),
    INDEX `idx_uploader_id` (`uploader_id`),
    INDEX `idx_upload_time` (`upload_time`),
    INDEX `idx_file_type` (`file_type`),
    INDEX `idx_is_deleted` (`is_deleted`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='推广附件表';

-- =====================================================
-- 推广表结构优化 - 移除冗余的department_id字段
-- 描述: 推广的所属部门应该动态关联到发布人的当前所属部门，而不是固定记录
-- =====================================================

-- 删除推广表中的department_id字段和相关约束
# ALTER TABLE `promotion` DROP FOREIGN KEY `promotion_ibfk_2`;
# ALTER TABLE `promotion` DROP COLUMN `department_id`;

-- =====================================================
-- 数据迁移脚本（已执行完成，仅作记录）
-- 注意：以下脚本已在生产环境执行，新环境无需执行
-- =====================================================

/*
-- 以下数据迁移脚本已执行完成，仅作为历史记录保留：

-- 1. 更新现有数据的content_type字段为TEXT
UPDATE `promotion` SET
    `content_type` = 'TEXT'
WHERE `content_type` IS NULL;

-- 2. 生成内容摘要（从现有content字段提取前500字符）
UPDATE `promotion` SET
    `content_summary` = LEFT(
        TRIM(
            REPLACE(
                REPLACE(
                    REPLACE(`content`, '\n', ' '),
                    '\r', ' '
                ),
                '\t', ' '
            )
        ),
        500
    )
WHERE `content_summary` IS NULL;

-- 3. 图片路径迁移（从 promotion/ 到 promotions/images/）
-- 此部分涉及文件系统迁移，已在生产环境完成
-- 旧路径格式: promotion/2025/06/13/filename.png
-- 新路径格式: promotions/images/2025/06/13/filename.png

-- 4. 更新图片路径数据库记录
UPDATE `promotion` p
SET p.images = (
    SELECT JSON_ARRAYAGG(
        CASE
            WHEN JSON_UNQUOTE(image_path) LIKE 'promotion/%'
            THEN CONCAT('promotions/images/', SUBSTRING(JSON_UNQUOTE(image_path), 11))
            ELSE JSON_UNQUOTE(image_path)
        END
    )
    FROM JSON_TABLE(
        p.images,
        '$[*]' COLUMNS (image_path JSON PATH '$')
    ) as jt
)
WHERE p.images IS NOT NULL
  AND JSON_LENGTH(p.images) > 0
  AND JSON_EXTRACT(p.images, '$[0]') LIKE '%promotion/%';
*/

-- 创建股票价格表
CREATE TABLE IF NOT EXISTS `stock_price`
(
    `id`          BIGINT         NOT NULL AUTO_INCREMENT COMMENT '股票价格ID',
    `unit_price`  DECIMAL(12, 2) NOT NULL COMMENT '股票单价',
    `time`        DATE           NOT NULL COMMENT '股票价格时间（年月日）',
    `remark`      VARCHAR(255)            DEFAULT '' COMMENT '备注',
    `create_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_time` (`time`),

    -- 索引优化
    INDEX `idx_time` (`time`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='股票价格表';

-- 创建员工股票表
CREATE TABLE IF NOT EXISTS `employee_stock`
(
    `id`               BIGINT   NOT NULL AUTO_INCREMENT COMMENT '员工股票记录ID',
    `stock_id`         BIGINT   NOT NULL COMMENT '股票价格表ID（外键）',
    `employee_id`      INT      NOT NULL COMMENT '员工ID（外键）',
    `quantity`         INT      NOT NULL DEFAULT 0 COMMENT '股票数量',
    `acquisition_time` DATETIME NOT NULL COMMENT '获取时间',
    `unlock_time`      DATETIME COMMENT '解禁时间',
    `remark`           VARCHAR(255)      DEFAULT '' COMMENT '备注',
    `create_time`      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    FOREIGN KEY (`stock_id`) REFERENCES `stock_price` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_stock_id` (`stock_id`),
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_acquisition_time` (`acquisition_time`),
    INDEX `idx_unlock_time` (`unlock_time`),
    INDEX `idx_employee_stock` (`employee_id`, `stock_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='员工股票表';

-- 客户表添加操作员字段
ALTER TABLE `client`
    ADD COLUMN `operator_id` INT DEFAULT NULL COMMENT '操作员ID' AFTER `employee_id`,
    ADD CONSTRAINT `fk_client_operator` FOREIGN KEY (`operator_id`) REFERENCES `employee` (`employee_id`);

-- =====================================================
-- 会议地点管理功能数据库表设计
-- 描述: 支持会议地点的管理和开放时间段配置
-- 创建时间: 2025-07-04 16:30:00 +08:00
-- =====================================================

-- 创建会议地点表
CREATE TABLE IF NOT EXISTS `meeting_location`
(
    `id`                   INT                         NOT NULL AUTO_INCREMENT COMMENT '地点ID',
    `name`                 VARCHAR(100)                NOT NULL COMMENT '地点名称',
    `description`          TEXT COMMENT '地点描述',
    `capacity`             INT COMMENT '容纳人数',
    `facilities`           TEXT COMMENT '设施设备描述',
    `open_time`            TIME                        NOT NULL DEFAULT '08:00:00' COMMENT '开放开始时间',
    `close_time`           TIME                        NOT NULL DEFAULT '18:00:00' COMMENT '开放结束时间',
    `available_days`       VARCHAR(20)                 NOT NULL DEFAULT '1,2,3,4,5' COMMENT '可用星期（1-7，逗号分隔）',
    `available_start_date` DATE                        NULL COMMENT '开放开始日期（可选，NULL表示无限制）',
    `available_end_date`   DATE                        NULL COMMENT '开放结束日期（可选，NULL表示无限制）',
    `status`               ENUM ('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：启用、禁用',
    `create_time`          DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          DATETIME                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),

    -- 索引优化
    INDEX `idx_name` (`name`),
    INDEX `idx_status` (`status`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_available_date_range` (`available_start_date`, `available_end_date`),

    -- 唯一约束
    UNIQUE KEY `uk_location_name` (`name`)

    -- 注意：日期范围约束已移除以确保MySQL版本兼容性
    -- 日期范围验证将在应用层实现：
    -- 1. available_start_date 和 available_end_date 可以都为 NULL（无限制）
    -- 2. 如果都不为 NULL，则 available_start_date <= available_end_date
    -- 3. 可以只设置其中一个（单边限制）
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会议地点表';



-- =====================================================
-- 会议管理功能数据库表设计
-- 描述: 支持管理员发起会议、员工查看会议、会议总结讨论功能
-- 创建时间: 2025-01-03 15:30:00 +08:00
-- =====================================================

-- 创建会议表
CREATE TABLE IF NOT EXISTS `meeting`
(
    `id`             BIGINT       NOT NULL AUTO_INCREMENT COMMENT '会议ID',
    `title`          VARCHAR(200) NOT NULL COMMENT '会议主题',
    `content`        TEXT COMMENT '会议内容描述',
    `start_time`     DATETIME     NOT NULL COMMENT '会议开始时间',
    `end_time`       DATETIME     NULL COMMENT '会议结束时间',
    `location`       VARCHAR(200) COMMENT '会议地点（文本描述，保留兼容性）',
    `location_id`    INT          NULL COMMENT '会议地点ID',
    `creator_id`     INT          NOT NULL COMMENT '发起人ID（管理员，保留原有功能）',
    `responsible_id` INT          NULL COMMENT '会议负责人ID',
    `create_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    FOREIGN KEY (`creator_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_meeting_location` FOREIGN KEY (`location_id`) REFERENCES `meeting_location` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_meeting_responsible` FOREIGN KEY (`responsible_id`) REFERENCES `employee` (`employee_id`) ON DELETE SET NULL,

    -- 索引优化
    INDEX `idx_creator_id` (`creator_id`),
    INDEX `idx_location_id` (`location_id`),
    INDEX `idx_responsible_id` (`responsible_id`),
    INDEX `idx_start_time` (`start_time`),
    INDEX `idx_end_time` (`end_time`),
    INDEX `idx_time_range` (`start_time`, `end_time`),
    INDEX `idx_create_time` (`create_time`)

    -- 注意：时间约束检查已移除以确保MySQL版本兼容性
    -- 时间顺序验证将在应用层实现：end_time 为 NULL 或 end_time > start_time
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会议信息表';

-- 创建会议参与者关联表
CREATE TABLE IF NOT EXISTS `meeting_participant`
(
    `id`               BIGINT                          NOT NULL AUTO_INCREMENT COMMENT '参与者记录ID',
    `meeting_id`       BIGINT                          NOT NULL COMMENT '会议ID',
    `participant_type` ENUM ('DEPARTMENT', 'EMPLOYEE') NOT NULL COMMENT '参与者类型：部门、员工',
    `participant_id`   INT                             NOT NULL COMMENT '参与者ID（部门ID或员工ID）',
    `create_time`      DATETIME                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    PRIMARY KEY (`id`),
    FOREIGN KEY (`meeting_id`) REFERENCES `meeting` (`id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_meeting_id` (`meeting_id`),
    INDEX `idx_participant_type` (`participant_type`),
    INDEX `idx_participant_id` (`participant_id`),
    INDEX `idx_meeting_participant` (`meeting_id`, `participant_type`, `participant_id`),

    -- 防止重复添加同一参与者
    UNIQUE KEY `uk_meeting_participant` (`meeting_id`, `participant_type`, `participant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会议参与者关联表';

-- 创建会议总结表
CREATE TABLE IF NOT EXISTS `meeting_summary`
(
    `id`              BIGINT   NOT NULL AUTO_INCREMENT COMMENT '总结ID',
    `meeting_id`      BIGINT   NOT NULL COMMENT '会议ID',
    `employee_id`     INT      NOT NULL COMMENT '总结人ID',
    `summary_content` TEXT     NOT NULL COMMENT '总结内容',
    `create_time`     DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    FOREIGN KEY (`meeting_id`) REFERENCES `meeting` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE,

    -- 索引优化
    INDEX `idx_meeting_id` (`meeting_id`),
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会议总结表';

-- =====================================================
-- 股票管理模块数据库表设计（汇总+明细混合模式）
-- 描述: 支持股票价格管理、员工股票分配、股票提现功能
-- 创建时间: 2025-07-22 10:30:00 +08:00
-- 架构模式: 汇总+明细混合模式，提供高性能查询和数据一致性保证
-- =====================================================

-- 创建股票提现申请表
CREATE TABLE IF NOT EXISTS `stock_withdrawal`
(
    `id`            BIGINT                                             NOT NULL AUTO_INCREMENT COMMENT '提现申请ID',
    `employee_id`   INT                                                NOT NULL COMMENT '员工ID',
    `quantity`      INT                                                NOT NULL COMMENT '提现股票数量',
    `unit_price`    DECIMAL(12, 2)                                     NOT NULL COMMENT '申请时股票单价',
    `total_amount`  DECIMAL(12, 2)                                     NOT NULL COMMENT '提现总金额',
    `status`        ENUM ('PENDING','APPROVED','REJECTED','CANCELLED') NOT NULL DEFAULT 'PENDING' COMMENT '申请状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝，CANCELLED-已取消',
    `apply_time`    DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    `audit_time`    DATETIME COMMENT '审核时间',
    `auditor_id`    INT COMMENT '审核人ID',
    `reject_reason` VARCHAR(500) COMMENT '拒绝理由',
    `remark`        VARCHAR(255) COMMENT '备注',
    `create_time`   DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   DATETIME                                           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),

    -- 外键约束
    CONSTRAINT `fk_stock_withdrawal_employee`
        FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`)
            ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_stock_withdrawal_auditor`
        FOREIGN KEY (`auditor_id`) REFERENCES `employee` (`employee_id`)
            ON DELETE SET NULL ON UPDATE CASCADE,

    -- 索引优化
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_apply_time` (`apply_time`),
    INDEX `idx_auditor_id` (`auditor_id`),
    INDEX `idx_employee_status` (`employee_id`, `status`),
    INDEX `idx_status_apply_time` (`status`, `apply_time`),

    -- 数据验证约束
    CONSTRAINT `chk_quantity_positive` CHECK (`quantity` > 0),
    CONSTRAINT `chk_unit_price_positive` CHECK (`unit_price` > 0),
    CONSTRAINT `chk_total_amount_positive` CHECK (`total_amount` > 0)

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '股票提现申请表';

-- 创建员工股票汇总表（汇总+明细混合模式的核心）
CREATE TABLE IF NOT EXISTS `employee_stock_summary`
(
    `employee_id`          INT      NOT NULL PRIMARY KEY COMMENT '员工ID',
    `total_quantity`       INT      NOT NULL DEFAULT 0 COMMENT '总股票数量',
    `unlocked_quantity`    INT      NOT NULL DEFAULT 0 COMMENT '已解禁数量',
    `withdrawn_quantity`   INT      NOT NULL DEFAULT 0 COMMENT '已提现数量',
    `available_quantity`   INT GENERATED ALWAYS AS (unlocked_quantity - withdrawn_quantity) STORED COMMENT '可提现数量（计算列）',
    `last_calculated_time` DATETIME          DEFAULT CURRENT_TIMESTAMP COMMENT '最后计算时间',
    `create_time`          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (`employee_id`) REFERENCES `employee` (`employee_id`) ON DELETE CASCADE ON UPDATE CASCADE,

    -- 索引优化
    INDEX `idx_total_quantity` (`total_quantity`),
    INDEX `idx_available_quantity` (`available_quantity`),
    INDEX `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '员工股票汇总表';

-- =====================================================
-- 股票汇总表自动维护触发器
-- 描述: 自动维护员工股票汇总数据的一致性
-- =====================================================

-- 创建触发器：员工股票新增时自动更新汇总表
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS `tr_employee_stock_insert_summary`
    AFTER INSERT
    ON `employee_stock`
    FOR EACH ROW
BEGIN
    DECLARE unlocked_qty INT DEFAULT 0;

    -- 计算是否已解禁
    IF NEW.unlock_time IS NULL OR NEW.unlock_time <= NOW() THEN
        SET unlocked_qty = NEW.quantity;
    END IF;

    -- 更新汇总表
    INSERT INTO employee_stock_summary (employee_id, total_quantity, unlocked_quantity, last_calculated_time)
    VALUES (NEW.employee_id, NEW.quantity, unlocked_qty, NOW())
    ON DUPLICATE KEY UPDATE total_quantity       = total_quantity + NEW.quantity,
                            unlocked_quantity    = unlocked_quantity + unlocked_qty,
                            last_calculated_time = NOW();
END$$

-- 创建触发器：员工股票更新时自动更新汇总表
CREATE TRIGGER IF NOT EXISTS `tr_employee_stock_update_summary`
    AFTER UPDATE
    ON `employee_stock`
    FOR EACH ROW
BEGIN
    DECLARE old_unlocked_qty INT DEFAULT 0;
    DECLARE new_unlocked_qty INT DEFAULT 0;

    -- 计算旧的解禁数量
    IF OLD.unlock_time IS NULL OR OLD.unlock_time <= NOW() THEN
        SET old_unlocked_qty = OLD.quantity;
    END IF;

    -- 计算新的解禁数量
    IF NEW.unlock_time IS NULL OR NEW.unlock_time <= NOW() THEN
        SET new_unlocked_qty = NEW.quantity;
    END IF;

    -- 更新汇总表
    UPDATE employee_stock_summary
    SET total_quantity       = total_quantity - OLD.quantity + NEW.quantity,
        unlocked_quantity    = unlocked_quantity - old_unlocked_qty + new_unlocked_qty,
        last_calculated_time = NOW()
    WHERE employee_id = NEW.employee_id;
END$$

-- 创建触发器：员工股票删除时自动更新汇总表
CREATE TRIGGER IF NOT EXISTS `tr_employee_stock_delete_summary`
    AFTER DELETE
    ON `employee_stock`
    FOR EACH ROW
BEGIN
    DECLARE unlocked_qty INT DEFAULT 0;

    -- 计算被删除的解禁数量
    IF OLD.unlock_time IS NULL OR OLD.unlock_time <= NOW() THEN
        SET unlocked_qty = OLD.quantity;
    END IF;

    -- 更新汇总表
    UPDATE employee_stock_summary
    SET total_quantity       = total_quantity - OLD.quantity,
        unlocked_quantity    = unlocked_quantity - unlocked_qty,
        last_calculated_time = NOW()
    WHERE employee_id = OLD.employee_id;

    -- 如果总数量为0，删除汇总记录
    DELETE
    FROM employee_stock_summary
    WHERE employee_id = OLD.employee_id
      AND total_quantity <= 0;
END$$

-- 创建触发器：股票提现审核通过时更新汇总表
CREATE TRIGGER IF NOT EXISTS `tr_stock_withdrawal_approve_summary`
    AFTER UPDATE
    ON `stock_withdrawal`
    FOR EACH ROW
BEGIN
    -- 当状态从非APPROVED变为APPROVED时，增加已提现数量
    IF NEW.status = 'APPROVED' AND OLD.status != 'APPROVED' THEN
        UPDATE employee_stock_summary
        SET withdrawn_quantity   = withdrawn_quantity + NEW.quantity,
            last_calculated_time = NOW()
        WHERE employee_id = NEW.employee_id;
    END IF;

    -- 当状态从APPROVED变为非APPROVED时，减少已提现数量
    -- 注意：CANCELLED状态不应该影响已提现数量，因为CANCELLED是从PENDING状态转换而来
    IF OLD.status = 'APPROVED' AND NEW.status NOT IN ('APPROVED') THEN
        UPDATE employee_stock_summary
        SET withdrawn_quantity   = withdrawn_quantity - NEW.quantity,
            last_calculated_time = NOW()
        WHERE employee_id = NEW.employee_id;
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 股票管理相关视图和初始化数据
-- 描述: 创建便于查询的视图和初始化汇总表数据
-- =====================================================

-- 创建视图：员工股票提现统计
CREATE OR REPLACE VIEW `v_employee_stock_withdrawal_stats` AS
SELECT e.employee_id,
       e.name                                                                              as employee_name,
       COALESCE(SUM(CASE WHEN sw.status = 'PENDING' THEN sw.quantity ELSE 0 END), 0)       as pending_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'PENDING' THEN sw.total_amount ELSE 0 END), 0)   as pending_amount,
       COALESCE(SUM(CASE WHEN sw.status = 'APPROVED' THEN sw.quantity ELSE 0 END), 0)      as approved_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'APPROVED' THEN sw.total_amount ELSE 0 END), 0)  as approved_amount,
       COALESCE(SUM(CASE WHEN sw.status = 'REJECTED' THEN sw.quantity ELSE 0 END), 0)      as rejected_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'REJECTED' THEN sw.total_amount ELSE 0 END), 0)  as rejected_amount,
       COALESCE(SUM(CASE WHEN sw.status = 'CANCELLED' THEN sw.quantity ELSE 0 END), 0)     as cancelled_quantity,
       COALESCE(SUM(CASE WHEN sw.status = 'CANCELLED' THEN sw.total_amount ELSE 0 END), 0) as cancelled_amount,
       COUNT(sw.id)                                                                        as total_applications
FROM employee e
         LEFT JOIN stock_withdrawal sw ON e.employee_id = sw.employee_id
GROUP BY e.employee_id, e.name;

-- 创建视图：员工股票完整统计（结合汇总表和价格表）
CREATE OR REPLACE VIEW `v_employee_stock_complete_stats` AS
SELECT ess.employee_id,
       e.name                                       as employee_name,
       d.department_name,
       ess.total_quantity,
       ess.unlocked_quantity,
       ess.withdrawn_quantity,
       ess.available_quantity,
       (ess.total_quantity - ess.unlocked_quantity) as locked_quantity,
       sp.unit_price                                as current_stock_price,
       sp.time                                      as current_price_date,
       (ess.total_quantity * sp.unit_price)         as total_value,
       (ess.unlocked_quantity * sp.unit_price)      as unlocked_value,
       (ess.available_quantity * sp.unit_price)     as available_value,
       (ess.withdrawn_quantity * sp.unit_price)     as withdrawn_value,
       ess.last_calculated_time,
       ess.update_time
FROM employee_stock_summary ess
         LEFT JOIN employee e ON ess.employee_id = e.employee_id
         LEFT JOIN department d ON e.department_id = d.department_id
         LEFT JOIN (SELECT unit_price, time
                    FROM stock_price
                    WHERE time = (SELECT MAX(time) FROM stock_price)) sp ON 1 = 1;

-- 初始化员工股票汇总表数据（基于现有employee_stock数据）
-- 注意：这个操作是幂等的，可以安全地重复执行
INSERT INTO employee_stock_summary (employee_id, total_quantity, unlocked_quantity, withdrawn_quantity,
                                    last_calculated_time)
SELECT es.employee_id,
       COALESCE(SUM(es.quantity), 0)             as total_quantity,
       COALESCE(SUM(CASE
                        WHEN es.unlock_time IS NULL OR es.unlock_time <= NOW()
                            THEN es.quantity
                        ELSE 0 END), 0)          as unlocked_quantity,
       COALESCE(withdrawn.withdrawn_quantity, 0) as withdrawn_quantity,
       NOW()                                     as last_calculated_time
FROM employee_stock es
         LEFT JOIN (SELECT employee_id, SUM(quantity) as withdrawn_quantity
                    FROM stock_withdrawal
                    WHERE status = 'APPROVED'
                    GROUP BY employee_id) withdrawn ON es.employee_id = withdrawn.employee_id
GROUP BY es.employee_id, withdrawn.withdrawn_quantity
ON DUPLICATE KEY UPDATE total_quantity       = VALUES(total_quantity),
                        unlocked_quantity    = VALUES(unlocked_quantity),
                        withdrawn_quantity   = VALUES(withdrawn_quantity),
                        last_calculated_time = VALUES(last_calculated_time);

-- 添加表注释说明
ALTER TABLE `stock_withdrawal`
    COMMENT = '股票提现申请表：记录员工股票提现申请和审核信息，支持完整的提现流程管理';
ALTER TABLE `employee_stock_summary`
    COMMENT = '员工股票汇总表：采用汇总+明细混合模式，提供高性能的股票数量查询，通过触发器自动维护数据一致性';

