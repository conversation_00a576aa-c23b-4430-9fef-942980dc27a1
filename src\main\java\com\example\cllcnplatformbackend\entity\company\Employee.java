package com.example.cllcnplatformbackend.entity.company;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 员工实体类（来自公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Employee {

    private Long employeeId;
    
    /**
     * 姓名
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 密码（加密）
     */
    private String password;
    
    /**
     * 角色：ADMIN, USER
     */
    private Role role;

    /**
     * 状态：Active, Inactive
     */
    private Status status;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 职位ID
     */
    private Long positionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 角色枚举
     */
    public enum Role {
        ADMIN,  // 管理员
        USER    // 普通用户
    }
    
    /**
     * 状态枚举
     */
    public enum Status {
        ACTIVE,   // 激活
        INACTIVE  // 未激活
    }
    
    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return Role.ADMIN.equals(this.role);
    }
    
    /**
     * 判断是否为激活状态
     */
    public boolean isActive() {
        return Status.ACTIVE.equals(this.status);
    }
}
