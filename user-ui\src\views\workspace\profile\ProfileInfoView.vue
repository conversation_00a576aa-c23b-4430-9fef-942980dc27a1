<!--
  账号信息页面
  
  @description 用户基本信息显示和编辑
  <AUTHOR>
  @date 2025-07-25 16:05:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="profile-info-page">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 头像和基本信息 -->
      <div class="lg:col-span-1">
        <Card>
          <CardContent class="p-6 text-center">
            <div class="mb-4">
              <Avatar class="h-24 w-24 mx-auto">
                <AvatarImage :src="userInfo.avatar" :alt="userInfo.name" />
                <AvatarFallback class="text-2xl">
                  {{ userInfo.name.charAt(0) }}
                </AvatarFallback>
              </Avatar>
            </div>
            <h3 class="text-lg font-semibold">{{ userInfo.name }}</h3>
            <p class="text-muted-foreground">{{ userInfo.email }}</p>
            <div class="mt-4 space-y-2">
              <Badge variant="secondary">{{ userInfo.memberLevel }}</Badge>
              <div class="text-sm text-muted-foreground">
                注册时间: {{ formatDate(userInfo.registerTime) }}
              </div>
            </div>
            <Button variant="outline" class="mt-4 w-full">
              <Upload class="h-4 w-4 mr-2" />
              更换头像
            </Button>
          </CardContent>
        </Card>

        <!-- 账号统计 -->
        <Card class="mt-6">
          <CardHeader>
            <CardTitle>账号统计</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-muted-foreground">总订单数</span>
              <span class="font-semibold">{{ userStats.totalOrders }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-muted-foreground">总消费金额</span>
              <span class="font-semibold">{{ formatCurrency(userStats.totalAmount) }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-muted-foreground">积分余额</span>
              <span class="font-semibold">{{ userStats.points }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-muted-foreground">优惠券数量</span>
              <span class="font-semibold">{{ userStats.coupons }}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 详细信息表单 -->
      <div class="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>个人信息</CardTitle>
            <CardDescription>更新您的个人详细信息</CardDescription>
          </CardHeader>
          <CardContent>
            <form @submit.prevent="saveUserInfo" class="space-y-6">
              <!-- 基本信息 -->
              <div class="space-y-4">
                <h4 class="text-sm font-medium text-foreground">基本信息</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label for="name">姓名 *</Label>
                    <Input
                      id="name"
                      v-model="form.name"
                      placeholder="请输入姓名"
                      required
                    />
                  </div>
                  <div>
                    <Label for="email">邮箱地址 *</Label>
                    <Input
                      id="email"
                      v-model="form.email"
                      type="email"
                      placeholder="请输入邮箱地址"
                      required
                    />
                  </div>
                  <div>
                    <Label for="phone">手机号码</Label>
                    <Input
                      id="phone"
                      v-model="form.phone"
                      placeholder="请输入手机号码"
                    />
                  </div>
                  <div>
                    <Label for="gender">性别</Label>
                    <Select v-model="form.gender">
                      <SelectTrigger>
                        <SelectValue placeholder="请选择性别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">男</SelectItem>
                        <SelectItem value="female">女</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label for="birthday">生日</Label>
                    <Input
                      id="birthday"
                      v-model="form.birthday"
                      type="date"
                    />
                  </div>
                  <div>
                    <Label for="idCard">身份证号</Label>
                    <Input
                      id="idCard"
                      v-model="form.idCard"
                      placeholder="请输入身份证号"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <!-- 公司信息 -->
              <div class="space-y-4">
                <h4 class="text-sm font-medium text-foreground">公司信息</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label for="company">公司名称</Label>
                    <Input
                      id="company"
                      v-model="form.company"
                      placeholder="请输入公司名称"
                    />
                  </div>
                  <div>
                    <Label for="position">职位</Label>
                    <Input
                      id="position"
                      v-model="form.position"
                      placeholder="请输入职位"
                    />
                  </div>
                  <div>
                    <Label for="department">部门</Label>
                    <Input
                      id="department"
                      v-model="form.department"
                      placeholder="请输入部门"
                    />
                  </div>
                  <div>
                    <Label for="workPhone">工作电话</Label>
                    <Input
                      id="workPhone"
                      v-model="form.workPhone"
                      placeholder="请输入工作电话"
                    />
                  </div>
                  <div class="md:col-span-2">
                    <Label for="address">公司地址</Label>
                    <Input
                      id="address"
                      v-model="form.address"
                      placeholder="请输入公司地址"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <!-- 偏好设置 -->
              <div class="space-y-4">
                <h4 class="text-sm font-medium text-foreground">偏好设置</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label for="language">语言偏好</Label>
                    <Select v-model="form.language">
                      <SelectTrigger>
                        <SelectValue placeholder="请选择语言" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="zh-CN">简体中文</SelectItem>
                        <SelectItem value="zh-TW">繁体中文</SelectItem>
                        <SelectItem value="en-US">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label for="timezone">时区</Label>
                    <Select v-model="form.timezone">
                      <SelectTrigger>
                        <SelectValue placeholder="请选择时区" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Shanghai">北京时间 (UTC+8)</SelectItem>
                        <SelectItem value="Asia/Hong_Kong">香港时间 (UTC+8)</SelectItem>
                        <SelectItem value="America/New_York">纽约时间 (UTC-5)</SelectItem>
                        <SelectItem value="Europe/London">伦敦时间 (UTC+0)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex justify-end space-x-2">
                <Button type="button" variant="outline" @click="resetForm">
                  重置
                </Button>
                <Button type="submit">
                  <Save class="h-4 w-4 mr-2" />
                  保存更改
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Upload, Save } from 'lucide-vue-next'

// 用户基本信息
const userInfo = ref({
  name: '张三',
  email: '<EMAIL>',
  avatar: '',
  memberLevel: '黄金会员',
  registerTime: '2024-01-15 10:30:00'
})

// 用户统计信息
const userStats = ref({
  totalOrders: 28,
  totalAmount: 156800.00,
  points: 2580,
  coupons: 5
})

// 表单数据
const form = ref({
  name: '张三',
  email: '<EMAIL>',
  phone: '150****7715',
  gender: 'male',
  birthday: '1990-01-01',
  idCard: '440***********1234',
  company: '深圳市中航物流有限公司',
  position: '物流经理',
  department: '运营部',
  workPhone: '0755-88888888',
  address: '深圳市南山区科技南路26号大冲商务中心A座18楼',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai'
})

// 原始表单数据（用于重置）
const originalForm = ref({ ...form.value })

// 方法
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const saveUserInfo = () => {
  console.log('保存用户信息:', form.value)
  // 实际实现中这里会调用API保存数据
  // 更新原始表单数据
  originalForm.value = { ...form.value }
}

const resetForm = () => {
  form.value = { ...originalForm.value }
}
</script>

<style scoped>
.profile-info-page {
  /* 页面样式 */
}
</style>
