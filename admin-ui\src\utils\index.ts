/**
 * 通用工具函数
 * 
 * @description 提供项目中常用的工具函数
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */

/**
 * 格式化相关工具
 */
export const format = {
  /**
   * 格式化货币
   */
  currency: (amount: number, currency = 'CNY'): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency
    }).format(amount)
  },

  /**
   * 格式化日期
   */
  date: (date: Date | string, format = 'YYYY-MM-DD HH:mm:ss'): string => {
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  },

  /**
   * 格式化文件大小
   */
  fileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * 格式化数字
   */
  number: (num: number, decimals = 2): string => {
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    })
  },

  /**
   * 格式化手机号（隐藏中间部分，支持国际化）
   */
  phone: (phone: string): string => {
    if (!phone || typeof phone !== 'string') return phone

    // 移除格式字符，保留数字和+号
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')

    // 如果是中国大陆手机号（11位）
    if (/^1[3-9]\d{9}$/.test(cleanPhone)) {
      return cleanPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }

    // 如果是带国际区号的格式
    if (cleanPhone.startsWith('+') || cleanPhone.length > 11) {
      const len = cleanPhone.length
      if (len <= 8) return cleanPhone

      // 显示前3位和后3位，中间用*号替代
      const start = cleanPhone.substring(0, 3)
      const end = cleanPhone.substring(len - 3)
      const middle = '*'.repeat(Math.min(4, len - 6))
      return `${start}${middle}${end}`
    }

    // 其他格式，如果长度合适就隐藏中间部分
    if (cleanPhone.length >= 7) {
      const len = cleanPhone.length
      const start = cleanPhone.substring(0, 2)
      const end = cleanPhone.substring(len - 2)
      const middle = '*'.repeat(Math.min(4, len - 4))
      return `${start}${middle}${end}`
    }

    return phone
  }
}

/**
 * 验证相关工具
 */
export const validate = {
  /**
   * 验证邮箱
   */
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 验证手机号（国际化支持）
   * 支持多种国际手机号格式，包括：
   * - 中国: +86 138 0013 8000, 13800138000
   * - 美国: ****** 123 4567, (*************
   * - 英国: +44 20 7946 0958, 020 7946 0958
   * - 其他国际格式
   */
  phone: (phone: string): boolean => {
    if (!phone || typeof phone !== 'string') return false

    // 移除所有空格、括号、连字符等格式字符
    const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, '')

    // 基本长度检查：国际手机号通常在7-15位之间
    if (cleanPhone.length < 7 || cleanPhone.length > 15) return false

    // 检查是否只包含数字
    if (!/^\d+$/.test(cleanPhone)) return false

    // 常见国际手机号格式验证
    const internationalPatterns = [
      /^86\d{11}$/,           // 中国 +86
      /^1\d{10}$/,            // 美国/加拿大 +1
      /^44\d{10,11}$/,        // 英国 +44
      /^33\d{9}$/,            // 法国 +33
      /^49\d{10,11}$/,        // 德国 +49
      /^81\d{10,11}$/,        // 日本 +81
      /^82\d{9,10}$/,         // 韩国 +82
      /^65\d{8}$/,            // 新加坡 +65
      /^852\d{8}$/,           // 香港 +852
      /^886\d{9}$/,           // 台湾 +886
      /^853\d{8}$/,           // 澳门 +853
      /^60\d{8,9}$/,          // 马来西亚 +60
      /^66\d{8,9}$/,          // 泰国 +66
      /^84\d{8,9}$/,          // 越南 +84
      /^62\d{8,12}$/,         // 印尼 +62
      /^63\d{9,10}$/,         // 菲律宾 +63
      /^91\d{10}$/,           // 印度 +91
      /^971\d{8,9}$/,         // 阿联酋 +971
      /^966\d{8,9}$/,         // 沙特 +966
      /^1[3-9]\d{9}$/         // 中国大陆本地格式
    ]

    // 检查是否匹配任一国际格式
    return internationalPatterns.some(pattern => pattern.test(cleanPhone))
  },

  /**
   * 验证身份证号
   */
  idCard: (idCard: string): boolean => {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  },

  /**
   * 验证URL
   */
  url: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  /**
   * 验证密码强度
   */
  password: (password: string): {
    valid: boolean
    strength: 'weak' | 'medium' | 'strong'
    message: string
  } => {
    if (password.length < 6) {
      return { valid: false, strength: 'weak', message: '密码长度至少6位' }
    }

    let score = 0
    if (/[a-z]/.test(password)) score++
    if (/[A-Z]/.test(password)) score++
    if (/\d/.test(password)) score++
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++

    if (score < 2) {
      return { valid: false, strength: 'weak', message: '密码强度太弱' }
    } else if (score < 3) {
      return { valid: true, strength: 'medium', message: '密码强度中等' }
    } else {
      return { valid: true, strength: 'strong', message: '密码强度很强' }
    }
  }
}

/**
 * 存储相关工具
 */
export const storage = {
  /**
   * 设置localStorage
   */
  setLocal: (key: string, value: any): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('localStorage设置失败:', error)
    }
  },

  /**
   * 获取localStorage
   */
  getLocal: <T = any>(key: string): T | null => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('localStorage获取失败:', error)
      return null
    }
  },

  /**
   * 移除localStorage
   */
  removeLocal: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('localStorage移除失败:', error)
    }
  },

  /**
   * 设置sessionStorage
   */
  setSession: (key: string, value: any): void => {
    try {
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('sessionStorage设置失败:', error)
    }
  },

  /**
   * 获取sessionStorage
   */
  getSession: <T = any>(key: string): T | null => {
    try {
      const item = sessionStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('sessionStorage获取失败:', error)
      return null
    }
  },

  /**
   * 移除sessionStorage
   */
  removeSession: (key: string): void => {
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.error('sessionStorage移除失败:', error)
    }
  }
}

/**
 * Token相关工具
 */
export const token = {
  /**
   * 解析JWT token获取payload
   */
  parseJWT: (token: string): any => {
    try {
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )
      return JSON.parse(jsonPayload)
    } catch (error) {
      console.error('解析JWT token失败:', error)
      return null
    }
  },

  /**
   * 检查token是否即将过期（5分钟内）
   */
  isTokenExpiringSoon: (tokenStr: string): boolean => {
    try {
      const payload = token.parseJWT(tokenStr)
      if (!payload || !payload.exp) return true

      const currentTime = Math.floor(Date.now() / 1000)
      const expirationTime = payload.exp
      const timeUntilExpiry = expirationTime - currentTime

      // 如果5分钟内过期，返回true
      return timeUntilExpiry <= 300 // 300秒 = 5分钟
    } catch (error) {
      console.error('检查token过期时间失败:', error)
      return true
    }
  },

  /**
   * 检查token是否已过期
   */
  isTokenExpired: (tokenStr: string): boolean => {
    try {
      const payload = token.parseJWT(tokenStr)
      if (!payload || !payload.exp) return true

      const currentTime = Math.floor(Date.now() / 1000)
      return payload.exp < currentTime
    } catch (error) {
      console.error('检查token过期状态失败:', error)
      return true
    }
  },

  /**
   * 获取token剩余有效时间（秒）
   */
  getTokenRemainingTime: (tokenStr: string): number => {
    try {
      const payload = token.parseJWT(tokenStr)
      if (!payload || !payload.exp) return 0

      const currentTime = Math.floor(Date.now() / 1000)
      const remainingTime = payload.exp - currentTime
      return Math.max(0, remainingTime)
    } catch (error) {
      console.error('获取token剩余时间失败:', error)
      return 0
    }
  }
}

/**
 * 通用工具函数
 */
export const utils = {
  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(null, args), wait)
    }
  },

  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(null, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  /**
   * 深拷贝
   */
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as any
    if (obj instanceof Array) return obj.map(item => utils.deepClone(item)) as any
    if (typeof obj === 'object') {
      const clonedObj = {} as any
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = utils.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  },

  /**
   * 生成UUID
   */
  generateUUID: (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  },

  /**
   * 下载文件
   */
  downloadFile: (url: string, filename?: string): void => {
    const link = document.createElement('a')
    link.href = url
    if (filename) {
      link.download = filename
    }
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 导出所有工具
export default {
  format,
  validate,
  storage,
  token,
  utils
}
