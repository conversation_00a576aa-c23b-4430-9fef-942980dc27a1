package com.example.cllcnplatformbackend.utils;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 分页结果封装类
 * 
 * @param <T> 数据类型
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> {

    private int pageNum;

    private int pageSize;

    private long total;

    private int pages;

    private List<T> list;
    
    /**
     * 构造方法
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param total 总记录数
     * @param list 数据列表
     */
    public PageResult(int pageNum, int pageSize, long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
        
        // 计算总页数
        this.pages = (int) Math.ceil((double) total / pageSize);
    }
    
    /**
     * 创建空的分页结果
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty(int pageNum, int pageSize) {
        return new PageResult<>(pageNum, pageSize, 0, List.of());
    }
    
    /**
     * 创建分页结果
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param total 总记录数
     * @param list 数据列表
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(int pageNum, int pageSize, long total, List<T> list) {
        return new PageResult<>(pageNum, pageSize, total, list);
    }
    
    /**
     * 判断是否有数据
     * @return true表示有数据，false表示无数据
     */
    public boolean hasData() {
        return list != null && !list.isEmpty();
    }
    
    /**
     * 判断是否为第一页
     * @return true表示是第一页，false表示不是
     */
    public boolean isFirstPage() {
        return pageNum == 1;
    }
    
    /**
     * 判断是否为最后一页
     * @return true表示是最后一页，false表示不是
     */
    public boolean isLastPage() {
        return pageNum >= pages;
    }
    
    /**
     * 获取下一页页码
     * @return 下一页页码，如果已是最后一页则返回当前页码
     */
    public int getNextPage() {
        return isLastPage() ? pageNum : pageNum + 1;
    }
    
    /**
     * 获取上一页页码
     * @return 上一页页码，如果已是第一页则返回当前页码
     */
    public int getPrevPage() {
        return isFirstPage() ? pageNum : pageNum - 1;
    }
}
