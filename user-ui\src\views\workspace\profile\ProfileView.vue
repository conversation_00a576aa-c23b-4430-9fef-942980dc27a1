<!--
  个人中心主页面
  
  @description 个人中心主页面，包含二级导航和子页面路由
  <AUTHOR>
  @date 2025-07-25 16:05:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="profile-page">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-foreground">个人中心</h1>
      <p class="text-muted-foreground mt-1">管理您的个人信息和偏好设置</p>
    </div>

    <!-- 标签页导航 -->
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="grid w-full grid-cols-4">
        <TabsTrigger value="info" class="flex items-center">
          <UserCircle class="h-4 w-4 mr-2" />
          账号信息
        </TabsTrigger>
        <TabsTrigger value="contacts" class="flex items-center">
          <Users class="h-4 w-4 mr-2" />
          联系人
        </TabsTrigger>
        <TabsTrigger value="notifications" class="flex items-center">
          <Mail class="h-4 w-4 mr-2" />
          收发通
        </TabsTrigger>
        <TabsTrigger value="coupons" class="flex items-center">
          <Ticket class="h-4 w-4 mr-2" />
          卡券中心
        </TabsTrigger>
      </TabsList>

      <!-- 账号信息 -->
      <TabsContent value="info" class="mt-6">
        <ProfileInfoView />
      </TabsContent>

      <!-- 联系人 -->
      <TabsContent value="contacts" class="mt-6">
        <ProfileContactsView />
      </TabsContent>

      <!-- 收发通 -->
      <TabsContent value="notifications" class="mt-6">
        <ProfileNotificationsView />
      </TabsContent>

      <!-- 卡券中心 -->
      <TabsContent value="coupons" class="mt-6">
        <ProfileCouponsView />
      </TabsContent>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { UserCircle, Users, Mail, Ticket } from 'lucide-vue-next'
import ProfileInfoView from './ProfileInfoView.vue'
import ProfileContactsView from './ProfileContactsView.vue'
import ProfileNotificationsView from './ProfileNotificationsView.vue'
import ProfileCouponsView from './ProfileCouponsView.vue'

const route = useRoute()
const router = useRouter()

// 当前激活的标签页
const activeTab = ref('info')

// 根据路由设置激活的标签页
const setActiveTabFromRoute = () => {
  const path = route.path
  if (path.includes('/info')) {
    activeTab.value = 'info'
  } else if (path.includes('/contacts')) {
    activeTab.value = 'contacts'
  } else if (path.includes('/notifications')) {
    activeTab.value = 'notifications'
  } else if (path.includes('/coupons')) {
    activeTab.value = 'coupons'
  } else {
    activeTab.value = 'info'
  }
}

// 监听标签页变化，更新路由
watch(activeTab, (newTab) => {
  const basePath = '/workspace/profile'
  let targetPath = `${basePath}/${newTab}`
  
  if (route.path !== targetPath) {
    router.push(targetPath)
  }
})

// 监听路由变化，更新标签页
watch(() => route.path, () => {
  setActiveTabFromRoute()
}, { immediate: true })
</script>

<style scoped>
.profile-page {
  /* 页面样式 */
}
</style>
