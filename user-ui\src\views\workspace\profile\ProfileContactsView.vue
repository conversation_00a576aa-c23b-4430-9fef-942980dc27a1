<!--
  联系人管理页面
  
  @description 管理联系人列表，支持添加、编辑、删除
  <AUTHOR>
  @date 2025-07-25 16:05:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="contacts-page">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div>
        <h2 class="text-xl font-semibold text-foreground">联系人管理</h2>
        <p class="text-muted-foreground mt-1">管理您的业务联系人</p>
      </div>
      <Button @click="showAddDialog = true">
        <Plus class="h-4 w-4 mr-2" />
        添加联系人
      </Button>
    </div>

    <!-- 搜索框 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchQuery"
            placeholder="搜索联系人姓名、公司或邮箱..."
            class="pl-10"
          />
        </div>
      </CardContent>
    </Card>

    <!-- 联系人列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <Card v-for="contact in filteredContacts" :key="contact.id" class="hover:shadow-md transition-shadow">
        <CardContent class="p-4">
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center space-x-3">
              <Avatar>
                <AvatarFallback>{{ contact.name.charAt(0) }}</AvatarFallback>
              </Avatar>
              <div>
                <h3 class="font-medium text-foreground">{{ contact.name }}</h3>
                <p class="text-sm text-muted-foreground">{{ contact.position }}</p>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal class="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem @click="editContact(contact)">
                  <Edit class="h-4 w-4 mr-2" />
                  编辑
                </DropdownMenuItem>
                <DropdownMenuItem @click="deleteContact(contact.id)" class="text-destructive">
                  <Trash2 class="h-4 w-4 mr-2" />
                  删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div class="space-y-2 text-sm">
            <div class="flex items-center text-muted-foreground">
              <Building class="h-4 w-4 mr-2" />
              {{ contact.company }}
            </div>
            <div class="flex items-center text-muted-foreground">
              <Mail class="h-4 w-4 mr-2" />
              {{ contact.email }}
            </div>
            <div class="flex items-center text-muted-foreground">
              <Phone class="h-4 w-4 mr-2" />
              {{ contact.phone }}
            </div>
          </div>

          <div class="mt-3 pt-3 border-t">
            <div class="flex space-x-2">
              <Button variant="outline" size="sm" class="flex-1">
                <Mail class="h-4 w-4 mr-2" />
                发邮件
              </Button>
              <Button variant="outline" size="sm" class="flex-1">
                <Phone class="h-4 w-4 mr-2" />
                拨打
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredContacts.length === 0" class="text-center py-12">
      <Users class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <h3 class="text-lg font-medium text-foreground mb-2">暂无联系人</h3>
      <p class="text-muted-foreground mb-4">
        {{ searchQuery ? '没有找到匹配的联系人' : '您还没有添加任何联系人' }}
      </p>
      <Button v-if="!searchQuery" @click="showAddDialog = true">
        <Plus class="h-4 w-4 mr-2" />
        添加第一个联系人
      </Button>
    </div>

    <!-- 添加/编辑联系人对话框 -->
    <Dialog v-model:open="showAddDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{{ editingContact ? '编辑联系人' : '添加联系人' }}</DialogTitle>
          <DialogDescription>
            {{ editingContact ? '修改联系人信息' : '添加新的业务联系人' }}
          </DialogDescription>
        </DialogHeader>
        <form @submit.prevent="saveContact" class="space-y-4">
          <div>
            <Label for="contact-name">姓名 *</Label>
            <Input
              id="contact-name"
              v-model="contactForm.name"
              placeholder="请输入姓名"
              required
            />
          </div>
          <div>
            <Label for="contact-email">邮箱 *</Label>
            <Input
              id="contact-email"
              v-model="contactForm.email"
              type="email"
              placeholder="请输入邮箱地址"
              required
            />
          </div>
          <div>
            <Label for="contact-phone">电话</Label>
            <Input
              id="contact-phone"
              v-model="contactForm.phone"
              placeholder="请输入电话号码"
            />
          </div>
          <div>
            <Label for="contact-company">公司</Label>
            <Input
              id="contact-company"
              v-model="contactForm.company"
              placeholder="请输入公司名称"
            />
          </div>
          <div>
            <Label for="contact-position">职位</Label>
            <Input
              id="contact-position"
              v-model="contactForm.position"
              placeholder="请输入职位"
            />
          </div>
        </form>
        <DialogFooter>
          <Button variant="outline" @click="cancelEdit">
            取消
          </Button>
          <Button @click="saveContact">
            {{ editingContact ? '保存' : '添加' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Search, 
  Mail, 
  Phone, 
  Building, 
  Users, 
  Edit, 
  Trash2, 
  MoreHorizontal 
} from 'lucide-vue-next'
import { mockContacts } from '@/data/mockData'
import type { Contact } from '@/types/workspace'

// 响应式数据
const searchQuery = ref('')
const showAddDialog = ref(false)
const editingContact = ref<Contact | null>(null)
const contacts = ref([...mockContacts])

// 联系人表单
const contactForm = ref({
  name: '',
  email: '',
  phone: '',
  company: '',
  position: ''
})

// 计算属性
const filteredContacts = computed(() => {
  if (!searchQuery.value) {
    return contacts.value
  }

  const query = searchQuery.value.toLowerCase()
  return contacts.value.filter(contact =>
    contact.name.toLowerCase().includes(query) ||
    contact.email.toLowerCase().includes(query) ||
    contact.company.toLowerCase().includes(query) ||
    contact.position.toLowerCase().includes(query)
  )
})

// 方法
const editContact = (contact: Contact) => {
  editingContact.value = contact
  contactForm.value = {
    name: contact.name,
    email: contact.email,
    phone: contact.phone,
    company: contact.company,
    position: contact.position
  }
  showAddDialog.value = true
}

const deleteContact = (contactId: string) => {
  if (confirm('确定要删除这个联系人吗？')) {
    const index = contacts.value.findIndex(c => c.id === contactId)
    if (index > -1) {
      contacts.value.splice(index, 1)
    }
  }
}

const saveContact = () => {
  if (!contactForm.value.name || !contactForm.value.email) {
    alert('请填写必填字段')
    return
  }

  if (editingContact.value) {
    // 编辑现有联系人
    const index = contacts.value.findIndex(c => c.id === editingContact.value!.id)
    if (index > -1) {
      contacts.value[index] = {
        ...contacts.value[index],
        ...contactForm.value
      }
    }
  } else {
    // 添加新联系人
    const newContact: Contact = {
      id: Date.now().toString(),
      ...contactForm.value,
      createTime: new Date().toISOString()
    }
    contacts.value.unshift(newContact)
  }

  cancelEdit()
}

const cancelEdit = () => {
  showAddDialog.value = false
  editingContact.value = null
  contactForm.value = {
    name: '',
    email: '',
    phone: '',
    company: '',
    position: ''
  }
}
</script>

<style scoped>
.contacts-page {
  /* 页面样式 */
}
</style>
