<!--
  登录弹窗组件
  
  @description 用户登录弹窗，支持手机号/邮箱登录
  <AUTHOR>
  @date 2025-07-18 16:00:00 +08:00
  @reference 基于shadcn-vue设计系统
-->

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { Eye, EyeOff, Phone, Mail, Lock, User, Loader2, CheckCircle, AlertCircle } from 'lucide-vue-next'
import { useUserStore } from '@/stores/user'
import { useUIStore } from '@/stores/ui'
import { validate, storage } from '@/utils'
import { toast, businessToast } from '@/utils/toast'

// Store和Router
const userStore = useUserStore()
const uiStore = useUIStore()
const router = useRouter()

// Props和Emits定义
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:open', 'switch-to-register', 'login-success'])

// 响应式状态
const isOpen = ref(props.open)
const showPassword = ref(false)
const loginType = ref('phone') // 'phone' | 'email'
const loading = ref(false)
const loginSuccess = ref(false)
const validating = ref(false)

// 输入框引用
const phoneInputRef = ref()
const emailInputRef = ref()
const passwordInputRef = ref()

// 表单数据
const loginForm = ref({
  phone: '',
  email: '',
  password: '',
  rememberMe: false
})

// 表单验证
const errors = ref({
  phone: '',
  email: '',
  password: ''
})

// 实时验证状态
const validationStatus = ref({
  phone: '', // 'success' | 'error' | ''
  email: '', // 'success' | 'error' | ''
  password: '' // 'success' | 'error' | ''
})

// 字段是否已被用户交互过
const touched = ref({
  phone: false,
  email: false,
  password: false
})

// 监听props变化
watch(() => props.open, (newVal) => {
  isOpen.value = newVal
})

// 监听内部状态变化
watch(isOpen, (newVal) => {
  emit('update:open', newVal)
  if (newVal) {
    // 弹窗打开时，恢复记住的登录信息并设置焦点
    restoreRememberedLogin()
    nextTick(() => {
      setInitialFocus()
    })
  }
})

// 初始化组件
onMounted(() => {
  restoreRememberedLogin()
})



// 恢复记住的登录信息
const restoreRememberedLogin = () => {
  const rememberedLogin = storage.getLocal('remembered_login')
  if (rememberedLogin) {
    if (rememberedLogin.type === 'phone') {
      loginType.value = 'phone'
      loginForm.value.phone = rememberedLogin.account
    } else if (rememberedLogin.type === 'email') {
      loginType.value = 'email'
      loginForm.value.email = rememberedLogin.account
    }
    loginForm.value.rememberMe = true
  }
}

// 设置初始焦点
const setInitialFocus = () => {
  if (loginType.value === 'phone' && phoneInputRef.value) {
    phoneInputRef.value.focus()
  } else if (loginType.value === 'email' && emailInputRef.value) {
    emailInputRef.value.focus()
  }
}

// 切换密码显示
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 实时验证手机号
const validatePhone = (value) => {
  touched.value.phone = true
  if (!value) {
    errors.value.phone = '请输入手机号'
    validationStatus.value.phone = 'error'
    return false
  }

  errors.value.phone = ''
  validationStatus.value.phone = 'success'
  return true
}

// 实时验证邮箱
const validateEmail = (value) => {
  touched.value.email = true
  if (!value) {
    errors.value.email = '请输入邮箱'
    validationStatus.value.email = 'error'
    return false
  }

  if (!validate.email(value)) {
    errors.value.email = '请输入正确的邮箱格式'
    validationStatus.value.email = 'error'
    return false
  }

  errors.value.email = ''
  validationStatus.value.email = 'success'
  return true
}

// 实时验证密码
const validatePassword = (value) => {
  touched.value.password = true
  if (!value) {
    errors.value.password = '请输入密码'
    validationStatus.value.password = 'error'
    return false
  }

  if (value.length < 6) {
    errors.value.password = '密码至少6位'
    validationStatus.value.password = 'error'
    return false
  }

  errors.value.password = ''
  validationStatus.value.password = 'success'
  return true
}

// 表单验证（提交时的最终验证）
const validateForm = () => {
  let isValid = true

  if (loginType.value === 'phone') {
    isValid = validatePhone(loginForm.value.phone) && isValid
  } else {
    isValid = validateEmail(loginForm.value.email) && isValid
  }

  isValid = validatePassword(loginForm.value.password) && isValid

  return isValid
}

// 保存记住的登录信息
const saveRememberedLogin = () => {
  if (loginForm.value.rememberMe) {
    const rememberedLogin = {
      type: loginType.value,
      account: loginType.value === 'phone' ? loginForm.value.phone : loginForm.value.email
    }
    storage.setLocal('remembered_login', rememberedLogin)
  } else {
    storage.removeLocal('remembered_login')
  }
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) {
    // 验证失败时，聚焦到第一个错误字段
    focusFirstErrorField()
    return
  }

  loading.value = true
  loginSuccess.value = false

  try {
    // 保存记住的登录信息
    saveRememberedLogin()

    // 准备登录凭据
    const credentials = {
      username: loginType.value === 'phone' ? loginForm.value.phone : loginForm.value.email,
      password: loginForm.value.password,
      rememberMe: loginForm.value.rememberMe
    }

    // 调用用户store的登录方法
    await userStore.login(credentials)

    // 显示登录成功状态
    loginSuccess.value = true

    // 获取重定向路径
    const targetPath = uiStore.loginRedirectPath || '/workspace'

    // 使用业务toast显示成功消息
    toast.success('登录成功！', {
      description: '正在跳转到工作台...',
      duration: 2000,
      action: {
        label: '立即查看',
        onClick: () => {
          // 立即跳转，不等待延迟
          isOpen.value = false
          if (targetPath !== router.currentRoute.value.path) {
            router.push(targetPath)
          }
        }
      }
    })

    // 延迟一下让用户看到成功状态
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 登录成功，关闭弹窗
    isOpen.value = false

    // 跳转到重定向路径
    if (targetPath !== router.currentRoute.value.path) {
      await router.push(targetPath)
    }

    // 重置表单
    resetForm()

    emit('login-success')
  } catch (error) {
    console.error('登录失败:', error)
    loginSuccess.value = false

    // 根据错误类型显示不同的提示
    if (error.message.includes('账户已被锁定')) {
      businessToast.loginError('locked', error.message)
    } else if (error.message.includes('账号或密码错误')) {
      businessToast.loginError('credentials')
      // 清空密码字段
      loginForm.value.password = ''
      validationStatus.value.password = ''
      // 聚焦到密码字段
      nextTick(() => {
        if (passwordInputRef.value) {
          passwordInputRef.value.focus()
        }
      })
    } else if (error.message.includes('网络') || error.message.includes('连接')) {
      businessToast.loginError('network')
    } else {
      businessToast.loginError('unknown', error.message)
    }
  } finally {
    loading.value = false
  }
}

// 聚焦到第一个错误字段
const focusFirstErrorField = () => {
  nextTick(() => {
    if (loginType.value === 'phone' && errors.value.phone && phoneInputRef.value) {
      phoneInputRef.value.focus()
    } else if (loginType.value === 'email' && errors.value.email && emailInputRef.value) {
      emailInputRef.value.focus()
    } else if (errors.value.password && passwordInputRef.value) {
      passwordInputRef.value.focus()
    }
  })
}

// 重置表单
const resetForm = () => {
  loginForm.value = {
    phone: '',
    email: '',
    password: '',
    rememberMe: false
  }
  errors.value = { phone: '', email: '', password: '' }
  validationStatus.value = { phone: '', email: '', password: '' }
  touched.value = { phone: false, email: false, password: false }
  showPassword.value = false
  loginSuccess.value = false
}

// 切换到注册
const switchToRegister = () => {
  isOpen.value = false
  emit('switch-to-register')
}

// 关闭弹窗时重置表单
const handleClose = () => {
  resetForm()
}
</script>

<template>
  <Dialog v-model:open="isOpen" @update:open="handleClose">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="text-center text-xl font-bold">登录账户</DialogTitle>
        <DialogDescription class="text-center">
          欢迎回来，请登录您的账户
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- 登录方式切换 -->
        <Tabs v-model="loginType" class="w-full">
          <TabsList class="grid w-full grid-cols-2">
            <TabsTrigger value="phone" class="flex items-center gap-2">
              <Phone class="h-4 w-4" />
              手机号
            </TabsTrigger>
            <TabsTrigger value="email" class="flex items-center gap-2">
              <Mail class="h-4 w-4" />
              邮箱
            </TabsTrigger>
          </TabsList>

          <!-- 手机号登录 -->
          <TabsContent value="phone" class="space-y-4">
            <div class="space-y-2">
              <Label for="login-phone">手机号</Label>
              <div class="relative">
                <Input
                  id="login-phone"
                  ref="phoneInputRef"
                  v-model="loginForm.phone"
                  type="tel"
                  placeholder="请输入手机号"
                  :class="{
                    'border-red-500': touched.phone && errors.phone,
                    'border-green-500': validationStatus.phone === 'success'
                  }"
                  @input="validatePhone(loginForm.phone)"
                  @blur="validatePhone(loginForm.phone)"
                />
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle v-if="validationStatus.phone === 'success'" class="h-4 w-4 text-green-500" />
                  <AlertCircle v-else-if="touched.phone && errors.phone" class="h-4 w-4 text-red-500" />
                </div>
              </div>
              <p v-if="touched.phone && errors.phone" class="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle class="h-3 w-3" />
                {{ errors.phone }}
              </p>
            </div>
          </TabsContent>

          <!-- 邮箱登录 -->
          <TabsContent value="email" class="space-y-4">
            <div class="space-y-2">
              <Label for="login-email">邮箱</Label>
              <div class="relative">
                <Input
                  id="login-email"
                  ref="emailInputRef"
                  v-model="loginForm.email"
                  type="email"
                  placeholder="请输入邮箱"
                  :class="{
                    'border-red-500': touched.email && errors.email,
                    'border-green-500': validationStatus.email === 'success'
                  }"
                  @input="validateEmail(loginForm.email)"
                  @blur="validateEmail(loginForm.email)"
                />
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <CheckCircle v-if="validationStatus.email === 'success'" class="h-4 w-4 text-green-500" />
                  <AlertCircle v-else-if="touched.email && errors.email" class="h-4 w-4 text-red-500" />
                </div>
              </div>
              <p v-if="touched.email && errors.email" class="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle class="h-3 w-3" />
                {{ errors.email }}
              </p>
            </div>
          </TabsContent>
        </Tabs>

        <!-- 密码输入 -->
        <div class="space-y-2">
          <Label for="login-password">密码</Label>
          <div class="relative">
            <Input
              id="login-password"
              ref="passwordInputRef"
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              :class="{
                'border-red-500': touched.password && errors.password,
                'border-green-500': validationStatus.password === 'success'
              }"
              class="pr-20"
              @input="validatePassword(loginForm.password)"
              @blur="validatePassword(loginForm.password)"
              @keyup.enter="handleLogin"
            />
            <div class="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
              <CheckCircle v-if="validationStatus.password === 'success'" class="h-4 w-4 text-green-500" />
              <AlertCircle v-else-if="touched.password && errors.password" class="h-4 w-4 text-red-500" />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                class="h-6 w-6 p-0 hover:bg-transparent"
                @click="togglePasswordVisibility"
              >
                <Eye v-if="showPassword" class="h-3 w-3" />
                <EyeOff v-else class="h-3 w-3" />
              </Button>
            </div>
          </div>
          <p v-if="touched.password && errors.password" class="text-sm text-red-500 flex items-center gap-1">
            <AlertCircle class="h-3 w-3" />
            {{ errors.password }}
          </p>
        </div>

        <!-- 记住我和忘记密码 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Checkbox id="login-remember" v-model:checked="loginForm.rememberMe" />
            <Label for="login-remember" class="text-sm">记住我</Label>
          </div>
          <Button variant="link" class="px-0 text-sm">
            忘记密码？
          </Button>
        </div>

        <!-- 登录按钮 -->
        <Button
          @click="handleLogin"
          class="w-full transition-all duration-200"
          :disabled="loading || loginSuccess"
          :class="{
            'bg-green-600 hover:bg-green-700': loginSuccess,
            'bg-primary hover:bg-primary/90': !loginSuccess
          }"
        >
          <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
          <CheckCircle v-else-if="loginSuccess" class="mr-2 h-4 w-4" />
          <User v-else class="mr-2 h-4 w-4" />

          <span v-if="loading">登录中...</span>
          <span v-else-if="loginSuccess">登录成功</span>
          <span v-else>登录</span>
        </Button>

        <!-- 注册链接 -->
        <div class="text-center text-sm">
          <span class="text-muted-foreground">还没有账户？</span>
          <Button variant="link" class="px-1" @click="switchToRegister">
            立即注册
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
