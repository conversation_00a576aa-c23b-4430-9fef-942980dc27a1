<!--
  运价查询空状态组件
  
  @description 展示无结果或初始状态
  <AUTHOR>
  @date 2025-07-21 10:05:00 +08:00
-->

<template>
  <Card class="w-full">
    <CardContent class="p-12 text-center">
      <!-- 图标 -->
      <div class="w-24 h-24 mx-auto mb-6 rounded-full flex items-center justify-center"
           :class="iconBgClass">
        <component :is="iconComponent" :class="iconClass" />
      </div>
      
      <!-- 标题 -->
      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        {{ title }}
      </h3>
      
      <!-- 描述 -->
      <p class="text-muted-foreground mb-6 max-w-md mx-auto">
        {{ description }}
      </p>
      
      <!-- 操作按钮 -->
      <div class="space-y-4">
        <Button v-if="showRetryButton" @click="handleRetry" class="mr-2">
          <RotateCcw class="w-4 h-4 mr-2" />
          重新查询
        </Button>
        
        <Button v-if="showContactButton" variant="outline" @click="handleContact">
          <MessageCircle class="w-4 h-4 mr-2" />
          联系客服
        </Button>
      </div>
      

    </CardContent>
  </Card>
</template>

<script setup>
import { computed } from 'vue'
import {
  Search, Ship, RotateCcw, MessageCircle
} from 'lucide-vue-next'

// UI组件
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

// Props
const props = defineProps({
  type: {
    type: String,
    default: 'initial', // 'initial', 'empty', 'error'
    validator: (value) => ['initial', 'empty', 'error'].includes(value)
  }
})

// Emits
const emit = defineEmits(['retry', 'contact'])

// 计算属性
const iconComponent = computed(() => {
  switch (props.type) {
    case 'initial':
      return Search
    case 'empty':
      return Ship
    case 'error':
      return RotateCcw
    default:
      return Search
  }
})

const iconBgClass = computed(() => {
  switch (props.type) {
    case 'initial':
      return 'bg-blue-50'
    case 'empty':
      return 'bg-gray-50'
    case 'error':
      return 'bg-red-50'
    default:
      return 'bg-blue-50'
  }
})

const iconClass = computed(() => {
  switch (props.type) {
    case 'initial':
      return 'w-12 h-12 text-blue-500'
    case 'empty':
      return 'w-12 h-12 text-gray-400'
    case 'error':
      return 'w-12 h-12 text-red-500'
    default:
      return 'w-12 h-12 text-blue-500'
  }
})

const title = computed(() => {
  switch (props.type) {
    case 'initial':
      return '开始查询运价'
    case 'empty':
      return '暂无查询结果'
    case 'error':
      return '查询出错了'
    default:
      return '开始查询运价'
  }
})

const description = computed(() => {
  switch (props.type) {
    case 'initial':
      return '输入起运港和目的港，获取最优运价方案'
    case 'empty':
      return '请尝试调整查询条件或联系客服获取更多航线信息'
    case 'error':
      return '网络连接异常，请检查网络后重试'
    default:
      return '输入起运港和目的港，获取最优运价方案'
  }
})

const showRetryButton = computed(() => {
  return props.type === 'empty' || props.type === 'error'
})

const showContactButton = computed(() => {
  return props.type === 'empty' || props.type === 'error'
})

// 方法
const handleRetry = () => {
  emit('retry')
}

const handleContact = () => {
  emit('contact')
}
</script>
