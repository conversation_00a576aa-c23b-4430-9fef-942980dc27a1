<!--
  布局组件

  @description 用户端主布局组件，包含导航和内容区域
  <AUTHOR>
  @date 2025-07-18 09:21:13 +08:00
  @reference 基于shadcn-vue设计系统
-->

<script setup lang="ts">
import { RouterView, useRoute, useRouter } from 'vue-router'
import { ref, computed, watch } from 'vue'
import { Menu, User, LogIn, UserPlus, Home, Search, Info, Monitor, Truck, Package, Plane, Phone } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from '@/components/ui/navigation-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { navigationItems } from '@/data/mockData'
import { useUserStore } from '@/stores/user'
import { useUIStore } from '@/stores/ui'

// 路由状态
const route = useRoute()
const router = useRouter()

// 状态管理
const userStore = useUserStore()
const uiStore = useUIStore()

// 移动端菜单状态
const isMobileMenuOpen = ref(false)

// 下拉菜单状态
const isDropdownOpen = ref(false)

// 计算属性
const isLoggedIn = computed(() => {
  const authenticated = userStore.isAuthenticated
  console.log('LayoutView: isLoggedIn =', authenticated)
  return authenticated
})

const user = computed(() => {
  const userInfo = userStore.userInfo
  console.log('LayoutView: user =', userInfo)
  return userInfo
})

// 监听用户状态变化
watch(() => userStore.userInfo, (newUserInfo) => {
  console.log('用户信息更新:', newUserInfo)
  // 这里可以添加额外的处理逻辑，如果需要的话
}, { deep: true })

// 导航菜单项
const menuItems = navigationItems

// 判断当前路由是否激活
const isActiveRoute = (href) => {
  return route.path === href || (href === '/home' && route.path === '/')
}

// 登录/注册/登出处理
const handleLogin = () => {
  // 从首页点击登录，不设置重定向路径，登录后保持在当前页面
  uiStore.openLoginModal()
}

const handleRegister = () => {
  uiStore.openRegisterModal()
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    console.log('用户登出成功')
    // 登出后跳转到首页
    router.push('/')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// 弹窗切换处理（这些方法现在由UI store处理，这里保留是为了兼容性）
const switchToRegister = () => {
  uiStore.switchToRegister()
}

const switchToLogin = () => {
  uiStore.switchToLogin()
}

// 登录成功处理（已移至App.vue统一处理，这里保留是为了兼容性）
const handleLoginSuccess = (userData) => {
  console.log('LayoutView: 登录成功:', userData)
  // 登录成功处理已移至App.vue，这里不再处理跳转逻辑
  // 只是简单记录日志，实际跳转由App.vue的handleLoginSuccess处理
}

// 注册成功处理
const handleRegisterSuccess = (userData) => {
  console.log('注册成功:', userData)
  // 注册成功后跳转到工作台
  router.push('/workspace')
}

// 下拉菜单控制
const openDropdown = () => {
  isDropdownOpen.value = true
}

const closeDropdown = () => {
  isDropdownOpen.value = false
}

// 事件监听器已移至UI store中统一管理
</script>

<template>
  <div class="min-h-screen bg-background">
    <!-- 顶部导航栏 -->
    <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16 flex h-16 items-center justify-between">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="flex h-9 w-9 md:h-10 md:w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground shadow-md">
            <span class="text-sm md:text-base font-bold">中</span>
          </div>
          <span class="text-xl md:text-2xl font-bold text-primary">中航网平台</span>
        </div>

        <!-- 桌面端导航菜单 -->
        <nav class="hidden md:flex items-center space-x-1">
          <router-link
            v-for="item in menuItems"
            :key="item.title"
            :to="item.href"
            class="relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-accent hover:text-accent-foreground"
            :class="{
              'text-primary bg-primary/10': isActiveRoute(item.href),
              'text-muted-foreground': !isActiveRoute(item.href)
            }"
          >
            <Home v-if="item.icon === 'home'" class="mr-2 h-4 w-4" />
            <Search v-else-if="item.icon === 'search'" class="mr-2 h-4 w-4" />
            <Monitor v-else-if="item.icon === 'monitor'" class="mr-2 h-4 w-4" />
            <Info v-else-if="item.icon === 'info'" class="mr-2 h-4 w-4" />
            {{ item.title }}
            <!-- 选中状态下划线 -->
            <div
              v-if="isActiveRoute(item.href)"
              class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-primary rounded-full"
            ></div>
          </router-link>
        </nav>

        <!-- 用户操作区域 -->
        <div class="flex items-center space-x-4">
          <!-- 已登录用户 -->
          <div v-if="isLoggedIn && user" class="flex items-center space-x-2">
            <!-- 用户电话信息 -->
            <div class="hidden lg:flex flex-col items-end">
              <span class="text-sm font-medium">{{ user?.name || '用户' }}</span>
              <span class="text-xs text-muted-foreground">{{ user?.phone || '未设置电话' }}</span>
            </div>
            
            <!-- 头像下拉菜单 -->
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="ghost" class="relative h-8 w-8 rounded-full">
                  <Avatar class="h-8 w-8">
                    <AvatarImage :src="user?.avatar || ''" :alt="user?.name || '用户'" />
                    <AvatarFallback>{{ user?.name?.charAt(0) || 'U' }}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="w-56" align="end">
                <div class="flex items-center justify-start gap-2 p-2">
                  <div class="flex flex-col space-y-1 leading-none">
                    <p class="font-medium">{{ user?.name || '用户' }}</p>
                    <p class="w-[200px] truncate text-sm text-muted-foreground">{{ user.email }}</p>
                    <p class="text-xs text-muted-foreground">{{ user?.phone || '未设置电话' }}</p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User class="mr-2 h-4 w-4" />
                  个人中心
                </DropdownMenuItem>
                <DropdownMenuItem>
                  我的订单
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem @click="handleLogout">
                  退出登录
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <!-- 未登录用户 -->
          <div v-else class="hidden md:flex items-center space-x-2">
            <Button variant="ghost" @click="handleLogin">
              <LogIn class="mr-2 h-4 w-4" />
              登录
            </Button>
            <Button @click="handleRegister">
              <UserPlus class="mr-2 h-4 w-4" />
              注册
            </Button>
          </div>

          <!-- 移动端菜单按钮 -->
          <Sheet v-model:open="isMobileMenuOpen">
            <SheetTrigger as-child>
              <Button variant="ghost" size="icon" class="md:hidden">
                <Menu class="h-5 w-5" />
                <span class="sr-only">打开菜单</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" class="w-[300px] sm:w-[400px]">
              <nav class="flex flex-col space-y-4">
                <div class="flex items-center space-x-3 pb-4 border-b">
                  <div class="flex h-9 w-9 items-center justify-center rounded-lg bg-primary text-primary-foreground shadow-md">
                    <span class="text-sm font-bold">中</span>
                  </div>
                  <span class="text-xl font-bold text-primary">中航网平台</span>
                </div>

                <!-- 移动端菜单项 -->
                <div class="flex flex-col space-y-2">
                  <router-link
                    v-for="item in menuItems"
                    :key="item.title"
                    :to="item.href"
                    class="flex items-center space-x-3 rounded-lg px-3 py-3 text-sm font-medium transition-colors border-l-4"
                    :class="{
                      'bg-primary/10 text-primary border-primary': isActiveRoute(item.href),
                      'hover:bg-accent hover:text-accent-foreground border-transparent': !isActiveRoute(item.href)
                    }"
                    @click="isMobileMenuOpen = false"
                  >
                    <Home v-if="item.icon === 'home'" class="h-5 w-5" />
                    <Search v-else-if="item.icon === 'search'" class="h-5 w-5" />
                    <Monitor v-else-if="item.icon === 'monitor'" class="h-5 w-5" />
                    <Info v-else-if="item.icon === 'info'" class="h-5 w-5" />
                    <span>{{ item.title }}</span>
                  </router-link>
                </div>

                <!-- 移动端用户操作 -->
                <div v-if="!isLoggedIn" class="flex flex-col space-y-2 pt-4 border-t">
                  <Button variant="outline" @click="handleLogin" class="w-full justify-start">
                    <LogIn class="mr-2 h-4 w-4" />
                    登录
                  </Button>
                  <Button @click="handleRegister" class="w-full justify-start">
                    <UserPlus class="mr-2 h-4 w-4" />
                    注册
                  </Button>
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex-1">
      <RouterView />
    </main>

    <!-- 底部信息 -->
    <footer class="border-t bg-muted/50">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16 py-8 md:py-12 lg:py-16">
        <div class="grid grid-cols-1 gap-6 md:gap-8 lg:gap-10 md:grid-cols-2 lg:grid-cols-4">
          <!-- 公司信息 -->
          <div class="space-y-4 md:space-y-5 lg:space-y-6">
            <div class="flex items-center space-x-2">
              <div class="flex h-6 w-6 md:h-7 md:w-7 lg:h-8 lg:w-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
                <span class="text-xs md:text-sm font-bold">中</span>
              </div>
              <span class="font-bold text-base md:text-lg">中航网平台</span>
            </div>
            <p class="text-sm md:text-base text-muted-foreground leading-relaxed max-w-sm">
              中航网（CLLCN）平台是深圳中航物流有限公司旗下专业的国际物流服务平台，为您提供安全、快捷、可靠的物流解决方案。
            </p>
          </div>

          <!-- 服务项目 -->
          <div class="space-y-4 md:space-y-5 lg:space-y-6">
            <h4 class="text-sm md:text-base font-semibold">服务项目</h4>
            <ul class="space-y-2 md:space-y-3 text-sm md:text-base text-muted-foreground">
              <li><a href="#" class="hover:text-foreground transition-colors">海运服务</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">空运服务</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">陆运服务</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">仓储服务</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">运价查询</a></li>
            </ul>
          </div>

          <!-- 客户支持 -->
          <div class="space-y-4 md:space-y-5 lg:space-y-6">
            <h4 class="text-sm md:text-base font-semibold">客户支持</h4>
            <ul class="space-y-2 md:space-y-3 text-sm md:text-base text-muted-foreground">
              <li><a href="#" class="hover:text-foreground transition-colors">帮助中心</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">在线客服</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">联系我们</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">服务条款</a></li>
              <li><a href="#" class="hover:text-foreground transition-colors">隐私政策</a></li>
            </ul>
          </div>

          <!-- 联系方式 -->
          <div class="space-y-4 md:space-y-5 lg:space-y-6">
            <h4 class="text-sm md:text-base font-semibold">联系方式</h4>
            <ul class="space-y-2 md:space-y-3 text-sm md:text-base text-muted-foreground">
              <li class="flex items-start">
                <span class="font-medium mr-2">电话：</span>
                <span>400-123-4567</span>
              </li>
              <li class="flex items-start">
                <span class="font-medium mr-2">邮箱：</span>
                <span><EMAIL></span>
              </li>
              <li class="flex items-start">
                <span class="font-medium mr-2">地址：</span>
                <span class="leading-relaxed">深圳市</span>
              </li>
              <li class="flex items-start">
                <span class="font-medium mr-2">营业时间：</span>
                <span>周一至周五 9:00-18:00</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- 版权信息 -->
        <div class="mt-8 md:mt-10 lg:mt-12 border-t pt-6 md:pt-8 lg:pt-10">
          <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div class="text-center md:text-left">
              <p class="text-sm md:text-base text-muted-foreground">
                &copy; 2025 中航网平台 (CLLCN). 保留所有权利.
              </p>
              <p class="text-xs md:text-sm text-muted-foreground mt-1">
                深圳中航物流有限公司 | 粤ICP备XXXXXXXX号
              </p>
            </div>
            <div class="flex items-center space-x-4 text-xs md:text-sm text-muted-foreground">
              <a href="#" class="hover:text-foreground transition-colors">网站地图</a>
              <span>|</span>
              <a href="#" class="hover:text-foreground transition-colors">法律声明</a>
              <span>|</span>
              <a href="#" class="hover:text-foreground transition-colors">友情链接</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- 登录和注册弹窗已移至App.vue中统一管理 -->
  </div>
</template>
