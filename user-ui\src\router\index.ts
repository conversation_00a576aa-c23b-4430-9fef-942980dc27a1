/**
 * 路由配置
 *
 * @description user-ui的路由配置和管理
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 * @note 与admin-ui共享相同架构，但适配用户端场景
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { RouteGuards } from './guards'

// 基础路由配置 - 核心页面（登录改为组件弹窗）
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/layout/LayoutView.vue'),
    redirect: '/home',
    meta: {
      title: '首页',
      requiresAuth: false
    },
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/views/HomeView.vue'),
        meta: {
          title: '首页',
          icon: 'home',
          requiresAuth: false,
          showInTabbar: true,
          tabbarIcon: 'home'
        }
      },
      {
        path: '/shipping-rate',
        name: 'ShippingRate',
        component: () => import('@/views/shipping/ShippingRateView.vue'),
        meta: {
          title: '运价查询',
          icon: 'shipping',
          requiresAuth: false,
          showInTabbar: true,
          tabbarIcon: 'shipping'
        }
      },
      {
        path: '/about',
        name: 'About',
        component: () => import('@/views/AboutView.vue'),
        meta: {
          title: '关于我们',
          icon: 'info',
          requiresAuth: false,
          showInTabbar: false,
          tabbarIcon: 'info'
        }
      },
      {
        path: '/workspace',
        name: 'Workspace',
        component: () => import('@/views/workspace/WorkspaceView.vue'),
        redirect: '/workspace/orders',
        meta: {
          title: '工作台',
          icon: 'monitor',
          requiresAuth: true,
          showInTabbar: false
        },
        children: [
          {
            path: '/workspace/orders',
            name: 'WorkspaceOrders',
            component: () => import('@/views/workspace/orders/OrdersView.vue'),
            meta: {
              title: '订单管理',
              icon: 'package',
              requiresAuth: true
            }
          },
          {
            path: '/workspace/bills',
            name: 'WorkspaceBills',
            component: () => import('@/views/workspace/bills/BillsView.vue'),
            meta: {
              title: '账单管理',
              icon: 'credit-card',
              requiresAuth: true
            }
          },
          {
            path: '/workspace/invoices',
            name: 'WorkspaceInvoices',
            component: () => import('@/views/workspace/invoices/InvoicesView.vue'),
            meta: {
              title: '发票管理',
              icon: 'file-text',
              requiresAuth: true
            }
          },
          {
            path: '/workspace/account',
            name: 'WorkspaceAccount',
            component: () => import('@/views/workspace/account/AccountView.vue'),
            meta: {
              title: '账号管理',
              icon: 'settings',
              requiresAuth: true
            }
          },
          {
            path: '/workspace/profile',
            name: 'WorkspaceProfile',
            component: () => import('@/views/workspace/profile/ProfileView.vue'),
            redirect: '/workspace/profile/info',
            meta: {
              title: '个人中心',
              icon: 'user',
              requiresAuth: true
            },
            children: [
              {
                path: '/workspace/profile/info',
                name: 'WorkspaceProfileInfo',
                component: () => import('@/views/workspace/profile/ProfileInfoView.vue'),
                meta: {
                  title: '账号信息',
                  icon: 'user-circle',
                  requiresAuth: true
                }
              },
              {
                path: '/workspace/profile/contacts',
                name: 'WorkspaceProfileContacts',
                component: () => import('@/views/workspace/profile/ProfileContactsView.vue'),
                meta: {
                  title: '联系人',
                  icon: 'users',
                  requiresAuth: true
                }
              },
              {
                path: '/workspace/profile/notifications',
                name: 'WorkspaceProfileNotifications',
                component: () => import('@/views/workspace/profile/ProfileNotificationsView.vue'),
                meta: {
                  title: '收发通',
                  icon: 'mail',
                  requiresAuth: true
                }
              },
              {
                path: '/workspace/profile/coupons',
                name: 'WorkspaceProfileCoupons',
                component: () => import('@/views/workspace/profile/ProfileCouponsView.vue'),
                meta: {
                  title: '卡券中心',
                  icon: 'ticket',
                  requiresAuth: true
                }
              }
            ]
          },
          {
            path: '/workspace/notifications',
            name: 'WorkspaceNotifications',
            component: () => import('@/views/workspace/notifications/NotificationsView.vue'),
            meta: {
              title: '消息通知',
              icon: 'bell',
              requiresAuth: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403View.vue'),
    meta: {
      title: '无权限',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404View.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 注册路由守卫
router.beforeEach(RouteGuards.beforeEach)
router.afterEach(RouteGuards.afterEach)

export default router
