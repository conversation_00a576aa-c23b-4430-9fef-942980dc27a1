import request from './request'

// 登录
export function login(data: { username: string; password: string; rememberMe?: boolean }) {
  return request({
    url: '/auth/login',
    method: 'post',
    data: {
      account: data.username,
      password: data.password,
      rememberMe: data.rememberMe || false
    }
  })
}

// 登出
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/auth/me',
    method: 'get'
  })
}

// 刷新令牌
export function refreshToken(data: { refreshToken: string }) {
  return request({
    url: '/auth/refresh',
    method: 'post',
    data: {
      refreshToken: data.refreshToken
    }
  })
}

// 验证令牌
export function validateToken(data: { token: string }) {
  return request({
    url: '/auth/validate',
    method: 'post',
    data: {
      token: data.token
    }
  })
}
