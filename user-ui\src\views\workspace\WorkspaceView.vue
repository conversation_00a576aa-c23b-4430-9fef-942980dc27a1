<!--
  工作台主页面
  
  @description 工作台主页面，包含左侧边栏和右侧内容区
  <AUTHOR>
  @date 2025-07-25 15:40:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="workspace-container h-screen flex bg-background">
    <!-- 桌面端侧边栏 -->
    <div class="hidden lg:flex lg:w-64 lg:flex-col">
      <WorkspaceSidebar />
    </div>

    <!-- 移动端侧边栏 (Sheet) -->
    <Sheet v-model:open="sidebarOpen">
      <SheetContent side="left" class="w-64 p-0">
        <WorkspaceSidebar />
      </SheetContent>
    </Sheet>

    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col min-w-0">
      <!-- 顶部工具栏 -->
      <header class="bg-background border-b px-4 py-3 flex items-center justify-between">
        <!-- 移动端菜单按钮 -->
        <Button
          variant="ghost"
          size="sm"
          class="lg:hidden"
          @click="sidebarOpen = true"
        >
          <Menu class="h-5 w-5" />
        </Button>

        <!-- 面包屑导航 -->
        <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
          <span class="text-foreground font-medium">工作台</span>
          <ChevronRight v-if="currentPageTitle" class="h-4 w-4" />
          <span v-if="currentPageTitle" class="text-foreground">{{ currentPageTitle }}</span>
          <ChevronRight v-if="currentSubPageTitle" class="h-4 w-4" />
          <span v-if="currentSubPageTitle" class="text-foreground">{{ currentSubPageTitle }}</span>
        </nav>

        <!-- 右侧操作区 -->
        <div class="flex items-center space-x-2">
          <!-- 通知按钮 -->
          <Button variant="ghost" size="sm" @click="goToNotifications">
            <Bell class="h-4 w-4" />
            <Badge v-if="unreadCount > 0" class="ml-1 px-1 py-0 text-xs">
              {{ unreadCount }}
            </Badge>
          </Button>

          <!-- 用户菜单 -->
          <Button variant="ghost" size="sm" @click="goToProfile">
            <User class="h-4 w-4" />
          </Button>
        </div>
      </header>

      <!-- 内容区域 -->
      <main class="flex-1 overflow-auto">
        <div class="container mx-auto p-4 max-w-7xl">
          <!-- 路由视图 -->
          <RouterView />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Sheet, SheetContent } from '@/components/ui/sheet'
import { Menu, ChevronRight, Bell, User } from 'lucide-vue-next'
import WorkspaceSidebar from '@/components/workspace/WorkspaceSidebar.vue'

const route = useRoute()
const router = useRouter()

// 移动端侧边栏状态
const sidebarOpen = ref(false)

// 模拟未读消息数量
const unreadCount = ref(3)

/**
 * 获取当前页面标题
 */
const currentPageTitle = computed(() => {
  const routeMeta = route.meta
  if (route.name?.toString().startsWith('WorkspaceProfile')) {
    return '个人中心'
  }
  return routeMeta?.title as string || ''
})

/**
 * 获取当前子页面标题
 */
const currentSubPageTitle = computed(() => {
  if (route.name?.toString().startsWith('WorkspaceProfile') && route.name !== 'WorkspaceProfile') {
    return route.meta?.title as string || ''
  }
  return ''
})

/**
 * 导航到通知页面
 */
const goToNotifications = () => {
  router.push('/workspace/notifications')
  sidebarOpen.value = false
}

/**
 * 导航到个人中心
 */
const goToProfile = () => {
  router.push('/workspace/profile')
  sidebarOpen.value = false
}
</script>

<style scoped>
.workspace-container {
  /* 确保容器占满整个视口 */
  min-height: 100vh;
}

/* 响应式设计优化 */
@media (max-width: 1024px) {
  .workspace-container {
    /* 移动端优化 */
    overflow-x: hidden;
  }
}

/* 滚动条样式优化 */
main::-webkit-scrollbar {
  width: 6px;
}

main::-webkit-scrollbar-track {
  background: transparent;
}

main::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

main::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}
</style>
