package com.example.cllcnplatformbackend.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 登录尝试管理服务
 * 简化版本：只提供基本的登录失败次数统计和账户锁定功能
 *
 * <AUTHOR> Platform
 * @since 2025-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginAttemptService {

    private final RedisTemplate<String, Object> redisTemplate;

    // 登录失败次数阈值
    private static final int MAX_ATTEMPT = 5;

    // 账户锁定时间（分钟）
    private static final int LOCK_TIME_MINUTES = 15;

    // Redis key前缀
    private static final String ATTEMPT_PREFIX = "login_attempt:";
    private static final String LOCK_PREFIX = "account_lock:";
    
    /**
     * 记录登录失败
     * @param identifier 用户标识（邮箱或手机号）
     */
    public void recordFailedAttempt(String identifier) {
        String key = ATTEMPT_PREFIX + identifier;

        // 获取当前失败次数
        Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
        if (attempts == null) {
            attempts = 0;
        }

        attempts++;
        log.warn("用户 {} 登录失败，当前失败次数: {}", identifier, attempts);

        // 设置失败次数，过期时间为锁定时间
        redisTemplate.opsForValue().set(key, attempts, LOCK_TIME_MINUTES, TimeUnit.MINUTES);

        // 如果达到最大失败次数，锁定账户
        if (attempts >= MAX_ATTEMPT) {
            lockAccount(identifier);
        }
    }

    /**
     * 清除登录失败记录（登录成功时调用）
     * @param identifier 用户标识
     */
    public void clearFailedAttempts(String identifier) {
        String attemptKey = ATTEMPT_PREFIX + identifier;
        String lockKey = LOCK_PREFIX + identifier;

        redisTemplate.delete(attemptKey);
        redisTemplate.delete(lockKey);

        log.info("用户 {} 登录成功，清除失败记录", identifier);
    }

    /**
     * 检查账户是否被锁定
     * @param identifier 用户标识
     * @return 是否被锁定
     */
    public boolean isAccountLocked(String identifier) {
        String lockKey = LOCK_PREFIX + identifier;
        Boolean locked = redisTemplate.hasKey(lockKey);
        return Boolean.TRUE.equals(locked);
    }

    /**
     * 获取账户锁定剩余时间（秒）
     * @param identifier 用户标识
     * @return 剩余锁定时间，-1表示未锁定
     */
    public long getLockRemainingTime(String identifier) {
        String lockKey = LOCK_PREFIX + identifier;
        Long expire = redisTemplate.getExpire(lockKey, TimeUnit.SECONDS);
        return expire != null ? expire : -1;
    }

    /**
     * 锁定账户
     * @param identifier 用户标识
     */
    private void lockAccount(String identifier) {
        String lockKey = LOCK_PREFIX + identifier;

        // 设置锁定标记，过期时间为锁定时间
        redisTemplate.opsForValue().set(lockKey, System.currentTimeMillis(),
                LOCK_TIME_MINUTES, TimeUnit.MINUTES);

        log.warn("用户 {} 因连续登录失败 {} 次被锁定 {} 分钟", identifier, MAX_ATTEMPT, LOCK_TIME_MINUTES);
    }

    /**
     * 手动解锁账户（管理员功能）
     * @param identifier 用户标识
     */
    public void unlockAccount(String identifier) {
        String attemptKey = ATTEMPT_PREFIX + identifier;
        String lockKey = LOCK_PREFIX + identifier;

        redisTemplate.delete(attemptKey);
        redisTemplate.delete(lockKey);

        log.info("管理员手动解锁用户: {}", identifier);
    }
}
