package com.example.cllcnplatformbackend.mapper;

import com.example.cllcnplatformbackend.entity.company.Client;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户Mapper接口（公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-08-01
 */
@Mapper
public interface ClientMapper {
    
    /**
     * 根据邮箱查找客户
     */
    Client findByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查找客户
     */
    Client findByPhone(@Param("phone") String phone);
    
    /**
     * 根据邮箱和状态列表查找客户（用于认证）
     */
    Client findByEmailAndStatusIn(@Param("email") String email, @Param("statusList") List<String> statusList);

    /**
     * 根据手机号和状态列表查找客户（用于认证）
     */
    Client findByPhoneAndStatusIn(@Param("phone") String phone, @Param("statusList") List<String> statusList);

    /**
     * 根据客户ID和状态列表查找客户（用于认证）
     */
    Client findByClientIdAndStatusIn(@Param("clientId") Long clientId, @Param("statusList") List<String> statusList);
    
    /**
     * 根据客户ID查找客户
     */
    Client findByClientId(@Param("clientId") Long clientId);
    
    /**
     * 根据公司名称查找客户
     */
    List<Client> findByCompanyNameContaining(@Param("companyName") String companyName);
    
    /**
     * 查找所有激活状态的客户
     */
    List<Client> findAllActive();
    
    /**
     * 根据状态查找客户
     */
    List<Client> findByStatus(@Param("status") String status);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(@Param("email") String email);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(@Param("phone") String phone);
    
    /**
     * 插入客户
     */
    int insert(Client client);
    
    /**
     * 更新客户
     */
    int update(Client client);

    /**
     * 更新客户密码
     */
    int updatePassword(@Param("clientId") Long clientId, @Param("password") String password);

    /**
     * 删除客户
     */
    int deleteById(@Param("clientId") Long clientId);

    /**
     * 统计所有客户数量（用于数据库连接验证）
     */
    int countAllClients();

    /**
     * 根据手机号模糊查询客户（用于调试验证）
     */
    List<Client> findByPhoneLike(@Param("phone") String phone);
}
