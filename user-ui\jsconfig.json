{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": false, "noEmit": true, "jsx": "preserve", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "allowJs": true, "checkJs": false}, "include": ["src/**/*.js", "src/**/*.vue", "src/**/*.ts"], "exclude": ["node_modules", "dist"]}