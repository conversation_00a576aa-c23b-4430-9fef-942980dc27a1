package com.example.cllcnplatformbackend.exception;

import com.example.cllcnplatformbackend.utils.Result;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.converter.HttpMessageNotReadableException;

import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.error("业务异常：{}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理参数校验异常 (@Valid注解触发)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationExceptions(MethodArgumentNotValidException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        
        String errorMessage = fieldErrors.stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining("; "));
        
        log.error("参数校验异常: {}", errorMessage);
        return Result.validateFailed(errorMessage);
    }
    
    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public Result<Void> handleBindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder sb = new StringBuilder();
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            sb.append(fieldError.getField()).append(": ").append(fieldError.getDefaultMessage()).append(", ");
        }
        String msg = sb.toString();
        if (msg.length() > 2) {
            msg = msg.substring(0, msg.length() - 2);
        }
        log.error("参数绑定异常：{}", msg);
        return Result.validateFailed(msg);
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException ex) {
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        
        String errorMessage = violations.stream()
            .map(ConstraintViolation::getMessage)
            .collect(Collectors.joining("; "));
        
        log.error("约束违反异常: {}", errorMessage);
        return Result.validateFailed(errorMessage);
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.error("缺少请求参数：{}", e.getMessage());
        return Result.validateFailed("缺少请求参数：" + e.getParameterName());
    }
    
    /**
     * 处理JSON解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Result<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("JSON解析异常：{}", e.getMessage());
        return Result.validateFailed("JSON解析异常，请检查请求参数格式");
    }
    
    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.error("请求方法不支持：{}", e.getMessage());
        return Result.error(405, "请求方法不支持：" + e.getMethod());
    }
    
    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Result<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        log.error("参数类型不匹配：{}", e.getMessage());
        return Result.validateFailed("参数类型不匹配：" + e.getName());
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Result<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件上传大小超限：{}", e.getMessage());
        
        String message = e.getMessage();
        if (message != null && message.contains("exceeds its maximum permitted size of")) {
            try {
                int startIndex = message.indexOf("size of ") + 8;
                int endIndex = message.indexOf(" bytes", startIndex);
                if (startIndex > 8 && endIndex > startIndex) {
                    long maxSizeBytes = Long.parseLong(message.substring(startIndex, endIndex));
                    double maxSizeMB = maxSizeBytes / (1024.0 * 1024.0);
                    return Result.error(413, String.format("文件大小超过限制，当前限制为 %.1fMB", maxSizeMB));
                }
            } catch (Exception ex) {
                log.warn("解析文件大小限制失败", ex);
            }
        }
        
        return Result.error(413, "文件大小超过限制，请选择较小的文件");
    }
    


    /**
     * 处理数据完整性约束违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result<Void> handleDataIntegrityViolationException(DataIntegrityViolationException e) {
        log.error("数据完整性约束违反：{}", e.getMessage());

        String message = e.getMessage();
        if (message != null) {
            if (message.contains("Data too long for column")) {
                String columnName = extractColumnName(message);
                return Result.validateFailed(getFieldDisplayName(columnName) + "长度超出限制，请减少输入内容");
            } else if (message.contains("Duplicate entry") && message.contains("for key")) {
                if (message.contains("email")) {
                    return Result.validateFailed("该邮箱已被注册，请使用其他邮箱");
                } else if (message.contains("phone")) {
                    return Result.validateFailed("该手机号已被注册，请使用其他手机号");
                } else {
                    return Result.validateFailed("数据已存在，请勿重复添加");
                }
            } else if (message.contains("foreign key constraint fails")) {
                return Result.validateFailed("关联数据不存在，请先添加相关数据");
            }
        }

        return Result.validateFailed("数据操作失败，请检查输入内容是否符合要求");
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<Void> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        return Result.error(500, "系统内部错误");
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error(500, "系统内部错误");
    }

    /**
     * 提取字段名
     */
    private String extractColumnName(String message) {
        try {
            int startIndex = message.indexOf("column '") + 8;
            int endIndex = message.indexOf("'", startIndex);
            if (startIndex > 8 && endIndex > startIndex) {
                return message.substring(startIndex, endIndex);
            }
        } catch (Exception e) {
            log.warn("提取字段名失败", e);
        }
        return "数据";
    }

    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(String columnName) {
        return switch (columnName) {
            case "name" -> "姓名";
            case "email" -> "邮箱";
            case "phone" -> "手机号";
            case "id_card" -> "身份证号码";
            case "company_name" -> "公司名称";
            case "contact_person" -> "联系人";
            case "address" -> "地址";
            case "description" -> "描述";
            case "remark" -> "备注";
            default -> "数据";
        };
    }
}
