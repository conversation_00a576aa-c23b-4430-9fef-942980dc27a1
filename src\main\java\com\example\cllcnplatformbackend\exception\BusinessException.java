package com.example.cllcnplatformbackend.exception;

import lombok.Getter;

/**
 * 业务异常类
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Getter
public class BusinessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误信息
     */
    private final String message;
    
    /**
     * 构造方法
     * @param message 错误信息
     */
    public BusinessException(String message) {
        super(message);
        this.code = 500;
        this.message = message;
    }
    
    /**
     * 构造方法
     * @param code 错误码
     * @param message 错误信息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造方法
     * @param message 错误信息
     * @param cause 原因
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
        this.message = message;
    }
    
    /**
     * 构造方法
     * @param code 错误码
     * @param message 错误信息
     * @param cause 原因
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 创建参数验证异常
     * @param message 错误信息
     * @return 业务异常
     */
    public static BusinessException validateFailed(String message) {
        return new BusinessException(400, message);
    }
    
    /**
     * 创建未授权异常
     * @param message 错误信息
     * @return 业务异常
     */
    public static BusinessException unauthorized(String message) {
        return new BusinessException(401, message);
    }
    
    /**
     * 创建禁止访问异常
     * @param message 错误信息
     * @return 业务异常
     */
    public static BusinessException forbidden(String message) {
        return new BusinessException(403, message);
    }
    
    /**
     * 创建资源不存在异常
     * @param message 错误信息
     * @return 业务异常
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(404, message);
    }
    
    /**
     * 创建服务器内部错误异常
     * @param message 错误信息
     * @return 业务异常
     */
    public static BusinessException internalError(String message) {
        return new BusinessException(500, message);
    }
}
