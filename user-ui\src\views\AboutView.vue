<!--
  关于我们页面
  
  @description 用户端关于我们页面，展示公司信息和服务介绍
  <AUTHOR>
  @date 2025-07-18 15:30:00 +08:00
  @reference 基于shadcn-vue设计系统
-->

<script setup lang="ts">
import { ref } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Zap, Shield, Globe, Users, Phone, Mail, MapPin, Clock } from 'lucide-vue-next'

// 公司信息
const companyInfo = ref({
  name: '中航网（CLLCN）平台',
  fullName: '深圳中航物流有限公司',
  description: '专业的国际物流服务平台，致力于为客户提供安全、快捷、可靠的物流解决方案。',
  established: '2020年',
  employees: '200+',
  coverage: '全球50+国家和地区'
})

// 服务优势
const advantages = ref([
  {
    icon: 'zap',
    title: '全网超低航运价',
    description: '价格直接对接船司，全网超低航运价，每日推出特惠运价，让您的物流成本降低30%'
  },
  {
    icon: 'shield',
    title: '运价实时更新',
    description: '航线费用明细一目了然，拒绝隐性收费，保证每一笔费用支出详细可查'
  },
  {
    icon: 'users',
    title: '便捷高效的客户服务',
    description: '港口信息全面，24小时报价、运价、订舱信息一键复制，不做重复工作'
  },
  {
    icon: 'globe',
    title: '物流全程追踪',
    description: '精准船期、船舶航行轨迹、货柜状态一目了然，为您提供最真实有效、更智能的物流管理体验'
  }
])

// 联系信息
const contactInfo = ref({
  phone: '************',
  email: '<EMAIL>',
  address: '深圳市',
  workTime: '周一至周五 9:00-18:00'
})
</script>

<template>
  <div class="min-h-screen bg-background">
    <!-- 页面标题 -->
    <section class="py-12 md:py-16 lg:py-20 bg-gradient-to-br from-primary/5 to-primary/10">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="text-center">
          <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6">关于我们</h1>
          <p class="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
            {{ companyInfo.description }}
          </p>
        </div>
      </div>
    </section>

    <!-- 公司介绍 -->
    <section class="py-12 md:py-16 lg:py-20">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-10 lg:gap-12 items-center">
          <!-- 左侧：公司信息 -->
          <div class="space-y-6 md:space-y-8">
            <div>
              <h2 class="text-2xl md:text-3xl font-bold mb-4 md:mb-6">{{ companyInfo.name }}</h2>
              <p class="text-base md:text-lg text-muted-foreground leading-relaxed mb-6">
                {{ companyInfo.fullName }}是一家专注于国际物流服务的现代化企业。
                公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍
              </p>
            </div>

            <!-- 公司数据 -->
            <div class="grid grid-cols-3 gap-4 md:gap-6">
              <div class="text-center p-4 bg-muted/50 rounded-lg">
                <div class="text-2xl md:text-3xl font-bold text-primary mb-2">{{ companyInfo.established }}</div>
                <div class="text-sm md:text-base text-muted-foreground">成立时间</div>
              </div>
              <div class="text-center p-4 bg-muted/50 rounded-lg">
                <div class="text-2xl md:text-3xl font-bold text-primary mb-2">{{ companyInfo.employees }}</div>
                <div class="text-sm md:text-base text-muted-foreground">员工数量</div>
              </div>
              <div class="text-center p-4 bg-muted/50 rounded-lg">
                <div class="text-2xl md:text-3xl font-bold text-primary mb-2">50+</div>
                <div class="text-sm md:text-base text-muted-foreground">服务国家</div>
              </div>
            </div>
          </div>

          <!-- 右侧：服务优势 -->
          <div class="space-y-4 md:space-y-6">
            <h3 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">服务优势</h3>
            <div class="space-y-4">
              <Card v-for="advantage in advantages" :key="advantage.title" class="p-4 md:p-6">
                <CardHeader class="pb-3">
                  <div class="flex items-center gap-3 mb-2">
                    <div class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Zap v-if="advantage.icon === 'zap'" class="h-4 w-4 text-primary" />
                      <Shield v-else-if="advantage.icon === 'shield'" class="h-4 w-4 text-primary" />
                      <Users v-else-if="advantage.icon === 'users'" class="h-4 w-4 text-primary" />
                      <Globe v-else class="h-4 w-4 text-primary" />
                    </div>
                    <CardTitle class="text-base md:text-lg">{{ advantage.title }}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent class="pt-0">
                  <CardDescription class="text-sm md:text-base">{{ advantage.description }}</CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="py-12 md:py-16 lg:py-20 bg-muted/50">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="text-center mb-8 md:mb-12 lg:mb-16">
          <h2 class="text-2xl md:text-3xl font-bold mb-4 md:mb-6">联系我们</h2>
          <p class="text-base md:text-lg text-muted-foreground">
            如有任何疑问或需要帮助，请随时联系我们
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
          <!-- 电话 -->
          <Card class="text-center p-6 md:p-8">
            <CardHeader class="pb-4">
              <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Phone class="h-6 w-6 text-primary" />
              </div>
              <CardTitle class="text-lg">客服电话</CardTitle>
            </CardHeader>
            <CardContent>
              <p class="text-sm md:text-base font-medium">{{ contactInfo.phone }}</p>
            </CardContent>
          </Card>

          <!-- 邮箱 -->
          <Card class="text-center p-6 md:p-8">
            <CardHeader class="pb-4">
              <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Mail class="h-6 w-6 text-primary" />
              </div>
              <CardTitle class="text-lg">邮箱地址</CardTitle>
            </CardHeader>
            <CardContent>
              <p class="text-sm md:text-base font-medium">{{ contactInfo.email }}</p>
            </CardContent>
          </Card>

          <!-- 地址 -->
          <Card class="text-center p-6 md:p-8">
            <CardHeader class="pb-4">
              <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <MapPin class="h-6 w-6 text-primary" />
              </div>
              <CardTitle class="text-lg">公司地址</CardTitle>
            </CardHeader>
            <CardContent>
              <p class="text-sm md:text-base font-medium">{{ contactInfo.address }}</p>
            </CardContent>
          </Card>

          <!-- 营业时间 -->
          <Card class="text-center p-6 md:p-8">
            <CardHeader class="pb-4">
              <div class="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Clock class="h-6 w-6 text-primary" />
              </div>
              <CardTitle class="text-lg">营业时间</CardTitle>
            </CardHeader>
            <CardContent>
              <p class="text-sm md:text-base font-medium">{{ contactInfo.workTime }}</p>
            </CardContent>
          </Card>
        </div>

        <!-- 联系按钮 -->
        <div class="text-center mt-8 md:mt-10 lg:mt-12">
          <Button size="lg" class="px-8">
            <Phone class="mr-2 h-4 w-4" />
            立即联系我们
          </Button>
        </div>
      </div>
    </section>
  </div>
</template>
