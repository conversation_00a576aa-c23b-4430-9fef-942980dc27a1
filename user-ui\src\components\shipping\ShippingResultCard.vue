<!--
  运价结果卡片组件
  
  @description 展示单个运价查询结果
  <AUTHOR>
  @date 2025-07-21 10:00:00 +08:00
-->

<template>
  <Card class="w-full hover:shadow-lg transition-all duration-200 border-l-4 border-l-transparent hover:border-l-primary">
    <CardContent class="p-6">
      <div class="flex items-start justify-between gap-6">
        <!-- 左侧：船司和航线信息 -->
        <div class="flex-1 min-w-0">
          <!-- 船司信息 -->
          <div class="flex items-center space-x-4 mb-4">
            <div class="flex items-center space-x-3">
              <!-- 船司Logo -->
              <div class="w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center border">
                <span class="text-sm font-bold text-blue-600">
                  {{ result.carrier.substring(0, 3) }}
                </span>
              </div>
              
              <!-- 船司名称和时效 -->
              <div>
                <div class="font-semibold text-gray-900">{{ result.carrier }}</div>
                <div class="flex items-center text-sm text-muted-foreground">
                  <Clock class="w-3 h-3 mr-1" />
                  航程 {{ result.route.transitDays }} 天
                </div>
              </div>
            </div>
            
            <!-- 特性标签 -->
            <div class="flex flex-wrap gap-2">
              <Badge 
                v-for="feature in result.features" 
                :key="feature"
                :variant="getBadgeVariant(feature)"
                class="text-xs"
              >
                <component :is="getFeatureIcon(feature)" class="w-3 h-3 mr-1" />
                {{ feature }}
              </Badge>
            </div>
          </div>
          
          <!-- 航线详情 -->
          <div class="flex items-center space-x-2 text-sm mb-4">
            <div class="flex items-center space-x-2">
              <MapPin class="w-4 h-4 text-blue-600" />
              <span class="font-medium">{{ result.route.origin }}</span>
            </div>
            
            <ArrowRight class="w-4 h-4 text-muted-foreground" />
            
            <!-- 中转港显示 -->
            <div v-if="!result.route.isDirect" class="flex items-center space-x-2">
              <div class="flex items-center space-x-1 text-orange-600">
                <RotateCw class="w-3 h-3" />
                <span class="text-xs">{{ result.route.transitPorts?.join(', ') }}</span>
              </div>
              <ArrowRight class="w-4 h-4 text-muted-foreground" />
            </div>
            
            <div class="flex items-center space-x-2">
              <Navigation class="w-4 h-4 text-green-600" />
              <span class="font-medium">{{ result.route.destination }}</span>
            </div>
          </div>
          
          <!-- 有效期 -->
          <div class="flex items-center text-xs text-muted-foreground">
            <Calendar class="w-3 h-3 mr-1" />
            有效期: {{ formatDate(result.validPeriod.start) }} - {{ formatDate(result.validPeriod.end) }}
          </div>
        </div>
        
        <!-- 右侧：价格和操作 -->
        <div class="flex-shrink-0 min-w-[600px]">
          <!-- 价格和操作在同一行 -->
          <div class="flex items-center gap-4">
            <!-- 价格区域 -->
            <div class="flex gap-2">
              <div
                v-for="(price, containerType) in result.prices"
                :key="containerType"
                class="text-center px-3 py-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors min-w-[70px]"
              >
                <div class="text-xs text-muted-foreground">{{ containerType }}</div>
                <div class="text-sm font-bold text-orange-600">***</div>
              </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="flex gap-1">
              <Button variant="outline" size="sm" class="text-xs h-8 px-2">
                <DollarSign class="w-3 h-3 mr-1" />
                更多费用
              </Button>
              <Button variant="outline" size="sm" class="text-xs h-8 px-2">
                <Info class="w-3 h-3 mr-1" />
                特别说明
              </Button>
              <Button variant="outline" size="sm" class="text-xs h-8 px-2">
                <Copy class="w-3 h-3 mr-1" />
                复制运价
              </Button>
              <Button variant="outline" size="sm" class="text-xs h-8 px-2">
                <TrendingUp class="w-3 h-3 mr-1" />
                运价历史
              </Button>
              <Button class="h-8 px-4" @click="handleBooking">
                <Anchor class="w-3 h-3 mr-1" />
                立即订舱
              </Button>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup>
import { 
  Clock, MapPin, Navigation, ArrowRight, RotateCw, Calendar,
  ExternalLink, DollarSign, Info, Copy, TrendingUp, Anchor,
  Zap, Route, Package
} from 'lucide-vue-next'

// UI组件
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// Props
const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['booking'])

// 方法
const getBadgeVariant = (feature) => {
  switch (feature) {
    case '特惠':
      return 'destructive'
    case '直达':
      return 'default'
    case '中转':
      return 'secondary'
    default:
      return 'outline'
  }
}

const getFeatureIcon = (feature) => {
  switch (feature) {
    case '特惠':
      return Zap
    case '直达':
      return Route
    case '中转':
      return RotateCw
    default:
      return Package
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

const handleBooking = () => {
  emit('booking', props.result)
}
</script>
