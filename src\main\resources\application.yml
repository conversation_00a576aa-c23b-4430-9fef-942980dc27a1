# 开发环境配置文件
server:
  port: 8081
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

spring:
  # 应用信息
  application:
    name: cllcn-logistics-platform

  # 主配置
  main:
    banner-mode: console  # 保留启动横幅，但简化输出

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # 数据源配置 - 混合架构（用户认证共享，业务数据独立）
  datasource:
    # 公司管理系统数据库（用户认证共享）
    company:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ********************************************************************************************************************************************************************
      username: root
      password: 123456
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

    # 物流平台数据库（业务数据独立）
    logistics:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: *************************************************************************************************************************************************************
      username: root
      password: 123456
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境使用update，自动修复数据类型不匹配
    show-sql: false  # 关闭SQL日志输出
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false
        generate_statistics: false
        # 减少Hibernate日志输出
        jdbc:
          lob:
            non_contextual_creation: true

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5

# JWT配置
jwt:
  secret: cllcn_logistics_platform_dev_secret_key_2025
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer

# 自定义应用配置
app:
  upload:
    # 物理存储根路径
    path: D:/Jobs/Project/cllcn_logistics_platform/uploads
    # URL访问前缀
    url-prefix: /uploads
    # 应用基础URL
    base-url: http://localhost:8081

  # 跨域配置
  cors:
    allowed-origins: http://localhost:3001,http://localhost:3002
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

# MyBatis配置
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 启用SQL日志
    map-underscore-to-camel-case: true

# 日志配置 - 使用logback-spring.xml进行详细配置
logging:
  file:
    name: logs/cllcn-logistics-dev.log
  level:
    # 启用SQL日志输出
    com.example.cllcnplatformbackend.mapper: DEBUG
    # 启用认证服务详细日志
    com.example.cllcnplatformbackend.service.impl.AuthServiceImpl: DEBUG
    # 启用拦截器日志
    com.example.cllcnplatformbackend.interceptor: DEBUG



# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"  # 只暴露必要端点
  endpoint:
    health:
      show-details: when-authorized  # 减少健康检查详情输出
  # 禁用不必要的监控功能
  metrics:
    enable:
      all: false
    export:
      simple:
        enabled: false