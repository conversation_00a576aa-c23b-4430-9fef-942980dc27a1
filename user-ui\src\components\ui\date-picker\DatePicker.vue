<!--
  日期选择器组件
  
  @description 基于shadcn-vue的日期选择器
  <AUTHOR>
  @date 2025-07-21 10:15:00 +08:00
-->

<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="cn(
          'w-full h-11 justify-start text-left font-normal',
          !modelValue && 'text-muted-foreground',
          props.class
        )"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ modelValue ? formatDate(modelValue) : placeholder }}
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0" align="start">
      <Calendar
        v-model="selectedDate"
        :min-value="minDate"
        :max-value="maxDate"
        locale="zh-CN"
        weekday-format="short"
        initial-focus
      />
    </PopoverContent>
  </Popover>
</template>

<script setup>
import { computed } from 'vue'
import { CalendarDate, getLocalTimeZone, today, parseDate } from '@internationalized/date'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { cn } from '@/utils'

// UI组件
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择日期'
  },
  minDate: {
    type: Object,
    default: () => today(getLocalTimeZone())
  },
  maxDate: {
    type: Object,
    default: null
  },
  class: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 计算属性
const selectedDate = computed({
  get: () => {
    if (!props.modelValue) return undefined
    try {
      return parseDate(props.modelValue)
    } catch {
      return undefined
    }
  },
  set: (value) => {
    if (value) {
      emit('update:modelValue', value.toString())
    } else {
      emit('update:modelValue', '')
    }
  }
})

// 方法
const formatDate = (dateString) => {
  try {
    const date = parseDate(dateString)
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date.year, date.month - 1, date.day))
  } catch {
    return dateString
  }
}
</script>
