<!--
  账单管理页面
  
  @description 账单管理页面，包含账单列表、搜索筛选和操作功能
  <AUTHOR>
  @date 2025-07-25 15:45:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="bills-page">
    <!-- 页面标题和操作区 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-foreground">账单管理</h1>
        <p class="text-muted-foreground mt-1">管理您的账单和付款</p>
      </div>
      <div class="flex space-x-2 mt-4 sm:mt-0">
        <Button variant="outline">
          <Download class="h-4 w-4 mr-2" />
          导出账单
        </Button>
        <Button>
          <CreditCard class="h-4 w-4 mr-2" />
          批量付款
        </Button>
      </div>
    </div>

    <!-- 搜索和筛选区 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 搜索框 -->
          <div class="md:col-span-2">
            <Label for="search">搜索账单</Label>
            <div class="relative mt-1">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="输入账单号或订单号..."
                class="pl-10"
              />
            </div>
          </div>

          <!-- 状态筛选 -->
          <div>
            <Label for="status">账单状态</Label>
            <Select v-model="selectedStatus">
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="unpaid">未付款</SelectItem>
                <SelectItem value="paid">已付款</SelectItem>
                <SelectItem value="overdue">已逾期</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-end">
            <Button variant="outline" @click="resetFilters" class="w-full">
              <RotateCcw class="h-4 w-4 mr-2" />
              重置
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <FileText class="h-6 w-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">总账单</p>
              <p class="text-2xl font-bold">{{ totalBills }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <CheckCircle class="h-6 w-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">已付款</p>
              <p class="text-2xl font-bold">{{ paidBills }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <Clock class="h-6 w-6 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">待付款</p>
              <p class="text-2xl font-bold">{{ unpaidBills }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
              <AlertCircle class="h-6 w-6 text-red-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">已逾期</p>
              <p class="text-2xl font-bold">{{ overdueBills }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 账单列表 -->
    <Card>
      <CardContent class="p-0">
        <!-- 桌面端表格 -->
        <div class="hidden md:block">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>账单号</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>到期日期</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>关联订单</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="bill in paginatedBills" :key="bill.id">
                <TableCell class="font-medium">{{ bill.billNumber }}</TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(bill.status)">
                    {{ getStatusText(bill.status) }}
                  </Badge>
                </TableCell>
                <TableCell>{{ formatCurrency(bill.amount, bill.currency) }}</TableCell>
                <TableCell>{{ formatDate(bill.dueDate) }}</TableCell>
                <TableCell>{{ formatDate(bill.createTime) }}</TableCell>
                <TableCell>{{ getOrderNumber(bill.orderId) }}</TableCell>
                <TableCell>
                  <div class="flex space-x-2">
                    <Button variant="ghost" size="sm">
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button 
                      v-if="bill.status === 'unpaid' || bill.status === 'overdue'"
                      variant="ghost" 
                      size="sm"
                      class="text-green-600"
                    >
                      <CreditCard class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 移动端卡片列表 -->
        <div class="md:hidden p-4 space-y-4">
          <Card v-for="bill in paginatedBills" :key="bill.id" class="p-4">
            <div class="flex justify-between items-start mb-2">
              <div>
                <h3 class="font-medium">{{ bill.billNumber }}</h3>
                <Badge :variant="getStatusVariant(bill.status)" class="mt-1">
                  {{ getStatusText(bill.status) }}
                </Badge>
              </div>
              <div class="text-right">
                <div class="font-medium">{{ formatCurrency(bill.amount, bill.currency) }}</div>
                <div class="text-sm text-muted-foreground">到期: {{ formatDate(bill.dueDate) }}</div>
              </div>
            </div>
            <div class="space-y-1 text-sm text-muted-foreground">
              <div>关联订单: {{ getOrderNumber(bill.orderId) }}</div>
              <div>创建时间: {{ formatDate(bill.createTime) }}</div>
            </div>
            <div class="flex space-x-2 mt-3">
              <Button variant="outline" size="sm" class="flex-1">
                <Eye class="h-4 w-4 mr-2" />
                查看
              </Button>
              <Button 
                v-if="bill.status === 'unpaid' || bill.status === 'overdue'"
                size="sm" 
                class="flex-1"
              >
                <CreditCard class="h-4 w-4 mr-2" />
                付款
              </Button>
            </div>
          </Card>
        </div>

        <!-- 分页 -->
        <div class="p-4 border-t">
          <div class="flex items-center justify-between">
            <div class="text-sm text-muted-foreground">
              显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredBills.length) }} 
              共 {{ filteredBills.length }} 条记录
            </div>
            <div class="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === 1"
                @click="currentPage--"
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  RotateCcw, 
  Eye, 
  Download, 
  CreditCard, 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle 
} from 'lucide-vue-next'
import { mockBills, mockOrders } from '@/data/mockData'
import { BillStatus } from '@/types/workspace'
import type { Bill } from '@/types/workspace'

// 响应式数据
const searchQuery = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const filteredBills = computed(() => {
  let bills = mockBills

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    bills = bills.filter(bill => 
      bill.billNumber.toLowerCase().includes(query) ||
      bill.orderId.toLowerCase().includes(query)
    )
  }

  // 状态筛选
  if (selectedStatus.value) {
    bills = bills.filter(bill => bill.status === selectedStatus.value)
  }

  return bills
})

const totalPages = computed(() => Math.ceil(filteredBills.value.length / pageSize.value))

const paginatedBills = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredBills.value.slice(start, end)
})

// 统计数据
const totalBills = computed(() => mockBills.length)
const paidBills = computed(() => mockBills.filter(bill => bill.status === BillStatus.PAID).length)
const unpaidBills = computed(() => mockBills.filter(bill => bill.status === BillStatus.UNPAID).length)
const overdueBills = computed(() => mockBills.filter(bill => bill.status === BillStatus.OVERDUE).length)

// 方法
const getStatusVariant = (status: BillStatus) => {
  switch (status) {
    case BillStatus.UNPAID: return 'secondary'
    case BillStatus.PAID: return 'default'
    case BillStatus.OVERDUE: return 'destructive'
    case BillStatus.CANCELLED: return 'outline'
    default: return 'secondary'
  }
}

const getStatusText = (status: BillStatus) => {
  switch (status) {
    case BillStatus.UNPAID: return '未付款'
    case BillStatus.PAID: return '已付款'
    case BillStatus.OVERDUE: return '已逾期'
    case BillStatus.CANCELLED: return '已取消'
    default: return '未知'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

const getOrderNumber = (orderId: string) => {
  const order = mockOrders.find(o => o.id === orderId)
  return order ? order.orderNumber : `ORD-${orderId}`
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}
</script>

<style scoped>
.bills-page {
  /* 页面样式 */
}
</style>
