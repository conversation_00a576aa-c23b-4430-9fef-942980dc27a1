/**
 * 模拟数据
 *
 * @description 为demo演示提供模拟数据
 * <AUTHOR>
 * @date 2025-07-18 09:21:13 +08:00
 */

import type { WorkspaceMenuItem, Order, Bill, Invoice, Contact, Coupon, Message } from '@/types/workspace'
import { OrderStatus, BillStatus, InvoiceStatus, CouponStatus, MessageType } from '@/types/workspace'

export interface CarouselItem {
  id: number
  title: string
  description: string
  image: string
  link: string
}

export interface ServiceFeature {
  id: number
  title: string
  description: string
  icon: string
}

export interface Port {
  code: string
  name: string
  city: string
  country: string
}

// 轮播图数据
export const carouselData: CarouselItem[] = [
  {
    id: 1,
    title: '全球物流网络',
    description: '覆盖全球200+港口，提供一站式物流解决方案',
    image: '',
    link: '/services'
  },
  {
    id: 2,
    title: '智能追踪系统',
    description: '实时货物追踪，让您随时掌握货物动态',
    image: '',
    link: '/tracking'
  },
  {
    id: 3,
    title: '专业团队服务',
    description: '7x24小时专业客服，为您提供贴心服务',
    image: '',
    link: '/contact'
  }
]

// 服务特色数据
export const serviceFeatures: ServiceFeature[] = [
  {
    id: 1,
    title: '快速报价',
    description: '在线即时报价，透明价格体系',
    icon: 'zap'
  },
  {
    id: 2,
    title: '安全保障',
    description: '全程保险覆盖，货物安全有保障',
    icon: 'shield'
  },
  {
    id: 3,
    title: '全球网络',
    description: '覆盖全球主要港口和城市',
    icon: 'globe'
  },
  {
    id: 4,
    title: '专业服务',
    description: '专业团队提供一对一服务',
    icon: 'users'
  }
]

// 港口数据
export const ports: Port[] = [
  { code: 'CNSZX', name: '深圳港', city: '深圳', country: '中国' },
  { code: 'CNSHA', name: '上海港', city: '上海', country: '中国' },
  { code: 'CNGZH', name: '广州港', city: '广州', country: '中国' },
  { code: 'CNQIN', name: '青岛港', city: '青岛', country: '中国' },
  { code: 'CNTAO', name: '天津港', city: '天津', country: '中国' },
  { code: 'SGSIN', name: '新加坡港', city: '新加坡', country: '新加坡' },
  { code: 'HKHKG', name: '香港港', city: '香港', country: '中国香港' },
  { code: 'USNYC', name: '纽约港', city: '纽约', country: '美国' },
  { code: 'USLAX', name: '洛杉矶港', city: '洛杉矶', country: '美国' },
  { code: 'DEHAM', name: '汉堡港', city: '汉堡', country: '德国' }
]

// 导航菜单数据
export const navigationItems = [
  { title: '首页', href: '/home', icon: 'home' },
  { title: '运价查询', href: '/shipping-rate', icon: 'search' },
  { title: '工作台', href: '/workspace', icon: 'monitor' },
  { title: '关于我们', href: '/about', icon: 'info' }
]

// 运输方式数据
export interface ShippingMode {
  id: string
  name: string
  icon: string
  description: string
}

export const shippingModes: ShippingMode[] = [
  {
    id: 'fcl',
    name: '海运整箱',
    icon: 'ship',
    description: '整柜运输，适合大批量货物'
  },
  {
    id: 'lcl',
    name: '海运拼箱',
    icon: 'package',
    description: '拼箱运输，适合小批量货物'
  },
  {
    id: 'air',
    name: '空运',
    icon: 'plane',
    description: '快速运输，适合急件货物'
  },
  {
    id: 'rail',
    name: '铁路拼箱',
    icon: 'train',
    description: '铁路运输，经济实惠'
  }
]

// 搜索历史数据
export interface SearchHistory {
  id: number
  origin: string
  destination: string
  mode: string
  searchTime: string
}

export const searchHistory: SearchHistory[] = [
  {
    id: 1,
    origin: 'CNSZX',
    destination: 'USNYC',
    mode: 'fcl',
    searchTime: '2025-07-18 09:30:00'
  },
  {
    id: 2,
    origin: 'CNSHA',
    destination: 'DEHAM',
    mode: 'air',
    searchTime: '2025-07-18 08:15:00'
  },
  {
    id: 3,
    origin: 'CNGZH',
    destination: 'SGSIN',
    mode: 'lcl',
    searchTime: '2025-07-17 16:45:00'
  }
]

// 工作台菜单数据
export const workspaceMenuItems: WorkspaceMenuItem[] = [
  {
    title: '订单管理',
    href: '/workspace/orders',
    icon: 'package',
    children: [
      {
        title: '订单列表',
        href: '/workspace/orders/list',
        icon: 'list'
      }
    ]
  },
  {
    title: '账单管理',
    href: '/workspace/bills',
    icon: 'credit-card'
  },
  {
    title: '发票管理',
    href: '/workspace/invoices',
    icon: 'file-text'
  },
  {
    title: '账号管理',
    href: '/workspace/account',
    icon: 'settings'
  },
  {
    title: '个人中心',
    href: '/workspace/profile',
    icon: 'user',
    children: [
      {
        title: '账号信息',
        href: '/workspace/profile/info',
        icon: 'user-circle'
      },
      {
        title: '联系人',
        href: '/workspace/profile/contacts',
        icon: 'users'
      },
      {
        title: '收发通',
        href: '/workspace/profile/notifications',
        icon: 'mail'
      },
      {
        title: '卡券中心',
        href: '/workspace/profile/coupons',
        icon: 'ticket'
      }
    ]
  },
  {
    title: '消息通知',
    href: '/workspace/notifications',
    icon: 'bell'
  }
]

// 订单模拟数据
export const mockOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-2025-001',
    status: OrderStatus.CONFIRMED,
    origin: 'CNSHA',
    destination: 'USNYC',
    createTime: '2025-07-20 10:30:00',
    estimatedDelivery: '2025-08-05 15:00:00',
    totalAmount: 2580.00,
    currency: 'USD'
  },
  {
    id: '2',
    orderNumber: 'ORD-2025-002',
    status: OrderStatus.IN_TRANSIT,
    origin: 'CNGZH',
    destination: 'DEHAM',
    createTime: '2025-07-18 14:20:00',
    estimatedDelivery: '2025-08-10 12:00:00',
    totalAmount: 3200.00,
    currency: 'EUR'
  },
  {
    id: '3',
    orderNumber: 'ORD-2025-003',
    status: OrderStatus.PENDING,
    origin: 'CNSZX',
    destination: 'SGSIN',
    createTime: '2025-07-25 09:15:00',
    estimatedDelivery: '2025-07-30 18:00:00',
    totalAmount: 1850.00,
    currency: 'USD'
  },
  {
    id: '4',
    orderNumber: 'ORD-2025-004',
    status: OrderStatus.DELIVERED,
    origin: 'CNQIN',
    destination: 'JPYOK',
    createTime: '2025-07-10 16:45:00',
    estimatedDelivery: '2025-07-25 10:00:00',
    totalAmount: 1200.00,
    currency: 'JPY'
  },
  {
    id: '5',
    orderNumber: 'ORD-2025-005',
    status: OrderStatus.CANCELLED,
    origin: 'CNTAO',
    destination: 'AUMEL',
    createTime: '2025-07-15 11:30:00',
    estimatedDelivery: '2025-08-01 14:00:00',
    totalAmount: 2100.00,
    currency: 'AUD'
  }
]

// 账单模拟数据
export const mockBills: Bill[] = [
  {
    id: '1',
    billNumber: 'BILL-2025-001',
    status: BillStatus.PAID,
    amount: 2580.00,
    currency: 'USD',
    dueDate: '2025-08-20',
    createTime: '2025-07-20 10:30:00',
    orderId: '1'
  },
  {
    id: '2',
    billNumber: 'BILL-2025-002',
    status: BillStatus.UNPAID,
    amount: 3200.00,
    currency: 'EUR',
    dueDate: '2025-08-25',
    createTime: '2025-07-18 14:20:00',
    orderId: '2'
  },
  {
    id: '3',
    billNumber: 'BILL-2025-003',
    status: BillStatus.OVERDUE,
    amount: 1200.00,
    currency: 'JPY',
    dueDate: '2025-07-20',
    createTime: '2025-07-10 16:45:00',
    orderId: '4'
  },
  {
    id: '4',
    billNumber: 'BILL-2025-004',
    status: BillStatus.UNPAID,
    amount: 1850.00,
    currency: 'USD',
    dueDate: '2025-08-30',
    createTime: '2025-07-25 09:15:00',
    orderId: '3'
  }
]

// 发票模拟数据
export const mockInvoices: Invoice[] = [
  {
    id: '1',
    invoiceNumber: 'INV-2025-001',
    status: InvoiceStatus.ISSUED,
    amount: 2580.00,
    currency: 'USD',
    issueDate: '2025-07-21 10:30:00',
    billId: '1',
    downloadUrl: '/downloads/INV-2025-001.pdf'
  },
  {
    id: '2',
    invoiceNumber: 'INV-2025-002',
    status: InvoiceStatus.SENT,
    amount: 1200.00,
    currency: 'JPY',
    issueDate: '2025-07-22 14:20:00',
    billId: '3',
    downloadUrl: '/downloads/INV-2025-002.pdf'
  },
  {
    id: '3',
    invoiceNumber: 'INV-2025-003',
    status: InvoiceStatus.PENDING,
    amount: 3200.00,
    currency: 'EUR',
    issueDate: '2025-07-23 09:15:00',
    billId: '2'
  },
  {
    id: '4',
    invoiceNumber: 'INV-2025-004',
    status: InvoiceStatus.ISSUED,
    amount: 1850.00,
    currency: 'USD',
    issueDate: '2025-07-25 16:45:00',
    billId: '4',
    downloadUrl: '/downloads/INV-2025-004.pdf'
  }
]

// 联系人模拟数据
export const mockContacts: Contact[] = [
  {
    id: '1',
    name: '李经理',
    email: '<EMAIL>',
    phone: '+86 138-0013-8000',
    company: '上海进出口贸易有限公司',
    position: '物流经理',
    createTime: '2025-01-15 10:30:00'
  },
  {
    id: '2',
    name: '王总监',
    email: '<EMAIL>',
    phone: '+86 139-0013-9000',
    company: '广州国际货运代理有限公司',
    position: '业务总监',
    createTime: '2025-02-20 14:20:00'
  },
  {
    id: '3',
    name: 'John Smith',
    email: '<EMAIL>',
    phone: '******-0123',
    company: 'Global Logistics Inc.',
    position: 'Operations Manager',
    createTime: '2025-03-10 09:15:00'
  },
  {
    id: '4',
    name: '陈主管',
    email: '<EMAIL>',
    phone: '+86 137-0013-7000',
    company: '深圳海运物流有限公司',
    position: '运营主管',
    createTime: '2025-04-05 16:45:00'
  }
]

// 优惠券模拟数据
export const mockCoupons: Coupon[] = [
  {
    id: '1',
    name: '新用户专享优惠券',
    type: '满减券',
    discount: 100,
    status: CouponStatus.AVAILABLE,
    expiryDate: '2025-12-31',
    minAmount: 1000,
    description: '首次下单满1000元减100元'
  },
  {
    id: '2',
    name: '海运专线折扣券',
    type: '折扣券',
    discount: 0.9,
    status: CouponStatus.AVAILABLE,
    expiryDate: '2025-08-31',
    minAmount: 2000,
    description: '海运专线9折优惠，满2000元可用'
  },
  {
    id: '3',
    name: '快递服务券',
    type: '满减券',
    discount: 50,
    status: CouponStatus.USED,
    expiryDate: '2025-07-31',
    minAmount: 500,
    description: '快递服务满500元减50元'
  },
  {
    id: '4',
    name: '年度会员专享',
    type: '折扣券',
    discount: 0.85,
    status: CouponStatus.EXPIRED,
    expiryDate: '2025-06-30',
    minAmount: 5000,
    description: '年度会员专享8.5折优惠'
  }
]

// 消息模拟数据
export const mockMessages: Message[] = [
  {
    id: '1',
    type: MessageType.ORDER,
    title: '订单状态更新',
    content: '您的订单 ORD-2025-001 已确认，预计7月30日送达。',
    isRead: false,
    createTime: '2025-07-25 10:30:00',
    relatedId: '1'
  },
  {
    id: '2',
    type: MessageType.BILL,
    title: '账单到期提醒',
    content: '您有一张账单 BILL-2025-002 将于3天后到期，请及时付款。',
    isRead: false,
    createTime: '2025-07-24 14:20:00',
    relatedId: '2'
  },
  {
    id: '3',
    type: MessageType.SYSTEM,
    title: '系统维护通知',
    content: '系统将于本周六凌晨2:00-4:00进行维护，期间可能影响服务使用。',
    isRead: true,
    createTime: '2025-07-23 09:15:00'
  },
  {
    id: '4',
    type: MessageType.PROMOTION,
    title: '新用户优惠活动',
    content: '新用户专享优惠券已发放到您的账户，有效期至年底。',
    isRead: true,
    createTime: '2025-07-22 16:45:00'
  },
  {
    id: '5',
    type: MessageType.INVOICE,
    title: '发票开具完成',
    content: '您申请的发票 INV-2025-001 已开具完成，请查收。',
    isRead: true,
    createTime: '2025-07-21 11:30:00',
    relatedId: '1'
  }
]
