-- =====================================================
-- CLLCN国际物流平台 - 混合架构数据库更新脚本
-- 描述: 基于现有数据库环境，调整为混合架构模式
-- 执行前提: 已执行 init-1.sql, init-1-supplement.sql, init-2.sql
-- 创建时间: 2025-07-31
-- 架构模式: 用户认证共享 + 业务数据独立
-- =====================================================

-- =====================================================
-- 第一步：验证当前数据库环境
-- =====================================================

-- 检查公司管理系统数据库是否存在
SELECT 
    SCHEMA_NAME as '数据库名',
    DEFAULT_CHARACTER_SET_NAME as '字符集',
    DEFAULT_COLLATION_NAME as '排序规则'
FROM INFORMATION_SCHEMA.SCHEMATA 
WHERE SCHEMA_NAME IN ('company_management_system', 'logistics_platform');

-- 检查关键表是否存在
SELECT 
    TABLE_SCHEMA as '数据库',
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'company_management_system' 
AND TABLE_NAME IN ('employee', 'client')
UNION ALL
SELECT 
    TABLE_SCHEMA as '数据库',
    TABLE_NAME as '表名', 
    TABLE_COMMENT as '表注释'
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'logistics_platform' 
AND TABLE_NAME IN ('user_mapping', 'order_info');

-- =====================================================
-- 第二步：公司管理系统数据库调整
-- =====================================================

USE `company_management_system`;

-- 2.1 确保客户表有password字段（如果没有则添加）
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'client' 
    AND COLUMN_NAME = 'password'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `client` ADD COLUMN `password` VARCHAR(100) COMMENT ''登录密码(MD5加密)'' AFTER `email`',
    'SELECT ''客户表password字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2.2 为没有密码的客户设置默认密码
UPDATE `client` 
SET `password` = MD5('123456')
WHERE `password` IS NULL OR `password` = '';

-- 2.3 确保员工表有phone字段（如果没有则添加）
SET @phone_column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'employee' 
    AND COLUMN_NAME = 'phone'
);

SET @sql = IF(@phone_column_exists = 0,
    'ALTER TABLE `employee` ADD COLUMN `phone` VARCHAR(20) COMMENT ''手机号'' AFTER `name`',
    'SELECT ''员工表phone字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2.4 优化员工表role字段值（统一为大写）
UPDATE `employee` 
SET `role` = CASE 
    WHEN LOWER(`role`) = 'admin' THEN 'ADMIN'
    WHEN LOWER(`role`) = 'manager' THEN 'MANAGER' 
    WHEN LOWER(`role`) = 'user' THEN 'USER'
    ELSE 'USER'
END
WHERE `role` NOT IN ('ADMIN', 'MANAGER', 'USER');

-- 2.5 添加必要的索引（如果不存在）
-- 客户邮箱索引
SET @client_email_index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'client' 
    AND INDEX_NAME = 'idx_client_email'
);

SET @sql = IF(@client_email_index_exists = 0, 
    'ALTER TABLE `client` ADD INDEX `idx_client_email` (`email`)',
    'SELECT ''客户邮箱索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 客户手机号索引
SET @client_phone_index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'client' 
    AND INDEX_NAME = 'idx_client_phone'
);

SET @sql = IF(@client_phone_index_exists = 0, 
    'ALTER TABLE `client` ADD INDEX `idx_client_phone` (`phone`)',
    'SELECT ''客户手机号索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 员工手机号索引
SET @emp_phone_index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'company_management_system' 
    AND TABLE_NAME = 'employee' 
    AND INDEX_NAME = 'idx_employee_phone'
);

SET @sql = IF(@emp_phone_index_exists = 0, 
    'ALTER TABLE `employee` ADD INDEX `idx_employee_phone` (`phone`)',
    'SELECT ''员工手机号索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第三步：物流平台数据库调整
-- =====================================================

USE `logistics_platform`;

-- 3.1 更新用户映射表结构（如果需要）
-- 检查user_mapping表是否存在正确的字段
SET @mapping_table_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = 'logistics_platform' 
    AND TABLE_NAME = 'user_mapping'
);

-- 如果user_mapping表不存在，创建它
SET @sql = IF(@mapping_table_exists = 0,
    'CREATE TABLE `user_mapping` (
        `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT ''映射ID'',
        `platform_user_id` VARCHAR(20) NOT NULL UNIQUE COMMENT ''平台用户ID (E123/C456)'',
        `source_table` ENUM(''employee'', ''client'') NOT NULL COMMENT ''来源表'',
        `source_id` INT NOT NULL COMMENT ''来源表ID'',
        `user_type` ENUM(''admin'', ''manager'', ''employee'', ''client'') NOT NULL COMMENT ''用户类型'',
        `real_name` VARCHAR(50) NOT NULL COMMENT ''真实姓名'',
        `email` VARCHAR(100) COMMENT ''邮箱'',
        `mobile` VARCHAR(20) COMMENT ''手机号'',
        `status` ENUM(''active'', ''inactive'') NOT NULL DEFAULT ''active'' COMMENT ''状态'',
        `department_id` INT COMMENT ''部门ID（仅员工）'',
        `employee_id` INT COMMENT ''负责员工ID（仅客户）'',
        `last_sync_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''最后同步时间'',
        `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'',
        `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'',
        INDEX `idx_source` (`source_table`, `source_id`),
        INDEX `idx_email` (`email`),
        INDEX `idx_user_type` (`user_type`),
        INDEX `idx_platform_user_id` (`platform_user_id`),
        INDEX `idx_status` (`status`),
        INDEX `idx_employee_id` (`employee_id`)
    ) COMMENT ''用户映射表''',
    'SELECT ''用户映射表已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3.2 重新创建或更新用户视图
CREATE OR REPLACE VIEW `v_platform_users` AS
SELECT
    CONCAT('E', employee_id) as platform_user_id,
    'employee' as source_table,
    employee_id as source_id,
    CASE
        WHEN role = 'ADMIN' THEN 'admin'
        WHEN role = 'MANAGER' THEN 'manager'
        ELSE 'employee'
    END as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE WHEN status = 'Active' THEN 'active' ELSE 'inactive' END as status,
    department_id,
    NULL as employee_id,
    role as original_role,
    update_time as last_sync_time
FROM company_management_system.employee
WHERE status = 'Active'

UNION ALL

SELECT
    CONCAT('C', client_id) as platform_user_id,
    'client' as source_table,
    client_id as source_id,
    'client' as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE 
        WHEN status IN ('审核通过', '已合作') THEN 'active' 
        ELSE 'inactive' 
    END as status,
    NULL as department_id,
    employee_id,
    'client' as original_role,
    update_time as last_sync_time
FROM company_management_system.client
WHERE password IS NOT NULL
AND password != '';

-- =====================================================
-- 第四步：同步用户映射数据
-- =====================================================

-- 清空现有映射数据并重新同步
TRUNCATE TABLE `user_mapping`;

-- 同步所有用户数据
INSERT INTO `user_mapping` (
    `platform_user_id`, `source_table`, `source_id`, `user_type`,
    `real_name`, `email`, `mobile`, `status`, `department_id`, `employee_id`
)
SELECT
    platform_user_id, source_table, source_id, user_type,
    real_name, email, mobile, status, department_id, employee_id
FROM `v_platform_users`;

-- =====================================================
-- 第五步：数据验证和完整性检查
-- =====================================================

-- 验证公司管理系统用户数据
SELECT 
    '公司管理系统用户统计' as '检查项目',
    '员工' as '用户类型',
    COUNT(*) as '总数',
    COUNT(CASE WHEN status = 'Active' THEN 1 END) as '激活数'
FROM company_management_system.employee
UNION ALL
SELECT 
    '公司管理系统用户统计' as '检查项目',
    '客户' as '用户类型',
    COUNT(*) as '总数',
    COUNT(CASE WHEN status IN ('审核通过', '已合作') AND password IS NOT NULL THEN 1 END) as '可登录数'
FROM company_management_system.client;

-- 验证物流平台用户映射
SELECT 
    '物流平台用户映射统计' as '检查项目',
    user_type as '用户类型',
    status as '状态',
    COUNT(*) as '数量'
FROM logistics_platform.user_mapping
GROUP BY user_type, status
ORDER BY user_type, status;

-- 验证视图数据一致性
SELECT 
    '数据一致性检查' as '检查项目',
    '视图数据' as '数据源',
    COUNT(*) as '记录数'
FROM logistics_platform.v_platform_users
UNION ALL
SELECT 
    '数据一致性检查' as '检查项目',
    '映射表数据' as '数据源',
    COUNT(*) as '记录数'
FROM logistics_platform.user_mapping;

-- 验证索引创建情况
SELECT 
    TABLE_SCHEMA as '数据库',
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as '索引列'
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA IN ('company_management_system', 'logistics_platform')
AND TABLE_NAME IN ('employee', 'client', 'user_mapping')
AND INDEX_NAME LIKE 'idx_%'
GROUP BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME
ORDER BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME;

-- =====================================================
-- 完成提示
-- =====================================================

SELECT 
    '混合架构数据库更新完成！' as message,
    '✅ 公司管理系统：用户认证共享配置完成' as company_status,
    '✅ 物流平台：业务数据独立配置完成' as logistics_status,
    '✅ 用户映射：数据同步完成' as mapping_status,
    '请检查上述验证结果，确保所有修改都已正确应用。' as note,
    '下一步：启动应用程序，测试混合架构认证功能' as next_step;
