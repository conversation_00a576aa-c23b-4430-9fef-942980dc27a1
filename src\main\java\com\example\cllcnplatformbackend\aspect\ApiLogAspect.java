package com.example.cllcnplatformbackend.aspect;

import com.example.cllcnplatformbackend.utils.ThreadLocalUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

/**
 * API日志切面
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ApiLogAspect {
    
    private final ObjectMapper objectMapper;
    
    /**
     * 定义切点：所有Controller方法
     */
    @Pointcut("execution(* com.example.cllcnplatformbackend.controller..*.*(..))")
    public void controllerMethods() {}
    
    /**
     * 环绕通知：记录API调用日志
     */
    @Around("controllerMethods()")
    public Object logApiCall(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        String method = request.getMethod();
        String url = request.getRequestURL().toString();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        String clientIp = getClientIp(request);
        
        // 获取用户信息
        @SuppressWarnings("unchecked")
        Map<String, Object> userInfo = ThreadLocalUtil.get("userInfo", Map.class);
        String userId = userInfo != null ? String.valueOf(userInfo.get("userId")) : "anonymous";
        String username = userInfo != null ? String.valueOf(userInfo.get("username")) : "anonymous";
        
        // 获取方法信息
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        try {
            // 记录请求日志
            log.info("API请求开始 - {} {} {} - 用户: {}({}) - IP: {} - 方法: {}.{}", 
                    method, uri, queryString != null ? "?" + queryString : "", 
                    username, userId, clientIp, className, methodName);
            
            // 记录请求参数（排除敏感信息）
            if (args != null && args.length > 0) {
                try {
                    String argsJson = objectMapper.writeValueAsString(filterSensitiveData(args));
                    log.debug("请求参数: {}", argsJson);
                } catch (Exception e) {
                    log.debug("请求参数序列化失败: {}", e.getMessage());
                }
            }
            
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 计算执行时间
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 记录响应日志
            log.info("API请求完成 - {} {} - 用户: {}({}) - 耗时: {}ms", 
                    method, uri, username, userId, executionTime);
            
            // 记录响应结果（仅在DEBUG级别）
            if (log.isDebugEnabled() && result != null) {
                try {
                    String resultJson = objectMapper.writeValueAsString(result);
                    log.debug("响应结果: {}", resultJson);
                } catch (Exception e) {
                    log.debug("响应结果序列化失败: {}", e.getMessage());
                }
            }
            
            return result;
            
        } catch (Exception e) {
            // 计算执行时间
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 记录异常日志
            log.error("API请求异常 - {} {} - 用户: {}({}) - 耗时: {}ms - 异常: {}", 
                    method, uri, username, userId, executionTime, e.getMessage());
            
            throw e;
        }
    }
    
    /**
     * 异常通知：记录异常信息
     */
    @AfterThrowing(pointcut = "controllerMethods()", throwing = "ex")
    public void logException(JoinPoint joinPoint, Throwable ex) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        log.error("方法执行异常 - {}.{} - 异常类型: {} - 异常信息: {}", 
                className, methodName, ex.getClass().getSimpleName(), ex.getMessage(), ex);
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
    
    /**
     * 过滤敏感数据
     */
    private Object[] filterSensitiveData(Object[] args) {
        // 这里可以实现敏感数据过滤逻辑
        // 例如：密码、身份证号等敏感信息
        return args;
    }
}
