<!--
  订单管理页面
  
  @description 订单管理页面，包含订单列表、搜索筛选和操作功能
  <AUTHOR>
  @date 2025-07-25 15:45:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="orders-page">
    <!-- 页面标题和操作区 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-foreground">订单管理</h1>
        <p class="text-muted-foreground mt-1">管理您的物流订单</p>
      </div>
      <Button class="mt-4 sm:mt-0">
        <Plus class="h-4 w-4 mr-2" />
        新建订单
      </Button>
    </div>

    <!-- 搜索和筛选区 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 搜索框 -->
          <div class="md:col-span-2">
            <Label for="search">搜索订单</Label>
            <div class="relative mt-1">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="输入订单号或目的地..."
                class="pl-10"
              />
            </div>
          </div>

          <!-- 状态筛选 -->
          <div>
            <Label for="status">订单状态</Label>
            <Select v-model="selectedStatus">
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="pending">待确认</SelectItem>
                <SelectItem value="confirmed">已确认</SelectItem>
                <SelectItem value="in_transit">运输中</SelectItem>
                <SelectItem value="delivered">已送达</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-end">
            <Button variant="outline" @click="resetFilters" class="w-full">
              <RotateCcw class="h-4 w-4 mr-2" />
              重置
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 订单列表 -->
    <Card>
      <CardContent class="p-0">
        <!-- 桌面端表格 -->
        <div class="hidden md:block">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单号</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>起始地</TableHead>
                <TableHead>目的地</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>预计送达</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="order in paginatedOrders" :key="order.id">
                <TableCell class="font-medium">{{ order.orderNumber }}</TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(order.status)">
                    {{ getStatusText(order.status) }}
                  </Badge>
                </TableCell>
                <TableCell>{{ order.origin }}</TableCell>
                <TableCell>{{ order.destination }}</TableCell>
                <TableCell>{{ formatDate(order.createTime) }}</TableCell>
                <TableCell>{{ formatDate(order.estimatedDelivery) }}</TableCell>
                <TableCell>{{ formatCurrency(order.totalAmount, order.currency) }}</TableCell>
                <TableCell>
                  <div class="flex space-x-2">
                    <Button variant="ghost" size="sm">
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit class="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" class="text-destructive">
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 移动端卡片列表 -->
        <div class="md:hidden p-4 space-y-4">
          <Card v-for="order in paginatedOrders" :key="order.id" class="p-4">
            <div class="flex justify-between items-start mb-2">
              <div>
                <h3 class="font-medium">{{ order.orderNumber }}</h3>
                <Badge :variant="getStatusVariant(order.status)" class="mt-1">
                  {{ getStatusText(order.status) }}
                </Badge>
              </div>
              <div class="flex space-x-1">
                <Button variant="ghost" size="sm">
                  <Eye class="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Edit class="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div class="space-y-1 text-sm text-muted-foreground">
              <div>{{ order.origin }} → {{ order.destination }}</div>
              <div>创建时间: {{ formatDate(order.createTime) }}</div>
              <div>预计送达: {{ formatDate(order.estimatedDelivery) }}</div>
              <div class="font-medium text-foreground">
                {{ formatCurrency(order.totalAmount, order.currency) }}
              </div>
            </div>
          </Card>
        </div>

        <!-- 分页 -->
        <div class="p-4 border-t">
          <div class="flex items-center justify-between">
            <div class="text-sm text-muted-foreground">
              显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredOrders.length) }} 
              共 {{ filteredOrders.length }} 条记录
            </div>
            <div class="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === 1"
                @click="currentPage--"
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Plus, Search, RotateCcw, Eye, Edit, Trash2 } from 'lucide-vue-next'
import { mockOrders } from '@/data/mockData'
import { OrderStatus } from '@/types/workspace'
import type { Order } from '@/types/workspace'

// 响应式数据
const searchQuery = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const filteredOrders = computed(() => {
  let orders = mockOrders

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    orders = orders.filter(order => 
      order.orderNumber.toLowerCase().includes(query) ||
      order.destination.toLowerCase().includes(query) ||
      order.origin.toLowerCase().includes(query)
    )
  }

  // 状态筛选
  if (selectedStatus.value) {
    orders = orders.filter(order => order.status === selectedStatus.value)
  }

  return orders
})

const totalPages = computed(() => Math.ceil(filteredOrders.value.length / pageSize.value))

const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredOrders.value.slice(start, end)
})

// 方法
const getStatusVariant = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.PENDING: return 'secondary'
    case OrderStatus.CONFIRMED: return 'default'
    case OrderStatus.IN_TRANSIT: return 'default'
    case OrderStatus.DELIVERED: return 'default'
    case OrderStatus.CANCELLED: return 'destructive'
    default: return 'secondary'
  }
}

const getStatusText = (status: OrderStatus) => {
  switch (status) {
    case OrderStatus.PENDING: return '待确认'
    case OrderStatus.CONFIRMED: return '已确认'
    case OrderStatus.IN_TRANSIT: return '运输中'
    case OrderStatus.DELIVERED: return '已送达'
    case OrderStatus.CANCELLED: return '已取消'
    default: return '未知'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}
</script>

<style scoped>
.orders-page {
  /* 页面样式 */
}
</style>
