# CLLCN 国际物流平台 - 前端项目

## 项目概述

本项目包含两个完全独立的前端应用：

- **admin-ui**: 管理后台系统 (端口: 3001)
- **user-ui**: 用户客户端系统 (端口: 3002)

## 技术栈

- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **UI组件库**: Element Plus
- **HTTP客户端**: Axios

## 项目结构

```
cllcn-platform-frontend/
├── .git/                    # 前端统一Git管理
├── admin-ui/                # 管理后台项目
│   ├── src/
│   │   ├── types/          # Admin独立类型定义
│   │   ├── api/            # Admin API调用
│   │   ├── components/     # Admin组件
│   │   ├── views/          # Admin页面
│   │   └── stores/         # Admin状态管理
│   ├── package.json        # Admin独立依赖
│   └── vite.config.ts      # Admin独立配置
│
└── user-ui/                 # 用户客户端项目
    ├── src/
    │   ├── types/          # User独立类型定义
    │   ├── api/            # User API调用
    │   ├── components/     # User组件
    │   ├── views/          # User页面
    │   └── stores/         # User状态管理
    ├── package.json        # User独立依赖
    └── vite.config.ts      # User独立配置
```

## 开发指南

### 安装依赖

```bash
# 安装Admin项目依赖
cd admin-ui
npm install

# 安装User项目依赖
cd user-ui
npm install
```

### 启动开发服务器

```bash
# 启动Admin项目 (localhost:3001)
cd admin-ui
npm run dev

# 启动User项目 (localhost:3002)
cd user-ui
npm run dev
```

### 构建项目

```bash
# 构建Admin项目
cd admin-ui
npm run build

# 构建User项目
cd user-ui
npm run build
```

## 项目特点

### 完全独立架构
- 各自独立的依赖管理
- 各自独立的类型定义
- 各自独立的构建配置
- 各自独立的开发环境

### 针对性优化
- **Admin**: 功能丰富，支持复杂图表和数据处理
- **User**: 轻量化，移动端友好，性能优化

### API集成
- 统一后端API地址: http://localhost:8080
- 各项目独立实现API调用逻辑
- 通过代理配置处理跨域问题

## 部署说明

### Admin项目部署
- 构建产物: `admin-ui/dist/`
- 建议域名: `admin.yourdomain.com`
- 目标用户: 内部管理员

### User项目部署
- 构建产物: `user-ui/dist/`
- 建议域名: `app.yourdomain.com`
- 目标用户: 外部客户

## 开发规范

1. **类型定义**: 各项目维护自己的TypeScript类型定义
2. **API调用**: 根据后端API文档独立实现
3. **代码规范**: 使用ESLint进行代码检查
4. **提交规范**: 遵循Git提交规范

## 注意事项

- 两个项目完全独立，不共享任何代码或配置
- API接口一致性通过后端文档保证
- 各项目可以使用不同版本的依赖包
- 独立的构建和部署流程
