/**
 * 事件总线
 * 
 * @description 全局事件总线，用于组件间通信
 * <AUTHOR>
 * @date 2025-07-18 10:35:00 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 * @note 用于路由守卫与UI组件间的通信
 */

import mitt from 'mitt'

// 定义事件类型
type Events = {
  'auth:login-required': { redirectPath: string }
  'auth:login-success': { user: any }
  'auth:logout': void
  'ui:show-login-modal': { redirectPath?: string }
  'ui:show-register-modal': void
  'ui:close-all-modals': void
}

// 创建事件总线实例
export const eventBus = mitt<Events>()

// 导出事件类型供其他地方使用
export type { Events }
