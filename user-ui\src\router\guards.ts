/**
 * 路由守卫
 * 
 * @description 路由权限控制和导航守卫
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 * @note 与admin-ui共享相同架构，使用简单的alert提示
 */

import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import type { PermissionCheckResult } from './types'
import { useUserStore } from '@/stores/user'
import { useUIStore } from '@/stores/ui'
import { eventBus } from '@/utils/eventBus'
import { storage, token } from '@/utils'
import { toast } from 'vue-sonner'

/**
 * 路由守卫类
 */
export class RouteGuards {
  /**
   * 前置守卫 - 权限验证
   */
  static async beforeEach(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ): Promise<void> {
    try {
      // 设置页面标题
      if (to.meta?.title) {
        document.title = `${to.meta.title} - CLLCN国际物流`
      }

      // 显示页面加载状态
      RouteGuards.showPageLoading()

      // 检查是否需要认证
      if (to.meta?.requiresAuth !== false) {
        const authResult = await RouteGuards.checkAuthentication(to)

        if (!authResult.isAuthenticated) {
          RouteGuards.hidePageLoading()

          if (authResult.reason === 'token_expired') {
            toast.error('登录已过期，请重新登录')
          } else if (authResult.reason === 'no_token') {
            // 静默处理，不显示错误提示
          }

          // 保存重定向路径并触发登录弹窗
          const uiStore = useUIStore()
          uiStore.setLoginRedirectPath(to.fullPath)
          eventBus.emit('ui:show-login-modal', { redirectPath: to.fullPath })
          next(false) // 阻止导航
          return
        }

        // 检查权限
        const permissionResult = RouteGuards.checkPermissions(to)
        if (!permissionResult.hasPermission) {
          RouteGuards.hidePageLoading()

          // 更友好的权限错误提示
          const missingItems = [
            ...(permissionResult.missingPermissions || []),
            ...(permissionResult.missingRoles || [])
          ]

          toast.error(`访问被拒绝：缺少必要权限 (${missingItems.join(', ')})`, {
            duration: 5000
          })

          if (permissionResult.redirectTo) {
            next(permissionResult.redirectTo)
          } else {
            next('/403')
          }
          return
        }
      }

      // 处理外部链接
      if (to.meta?.external) {
        RouteGuards.hidePageLoading()
        window.open(to.path as string, (to.meta.target as string) || '_blank')
        next(false)
        return
      }

      next()
    } catch (error) {
      console.error('路由守卫错误:', error)
      RouteGuards.hidePageLoading()
      toast.error('页面加载失败，请稍后重试')
      next(false)
    }
  }

  /**
   * 后置守卫 - 页面加载完成处理
   */
  static afterEach(
    to: RouteLocationNormalized,
    _from: RouteLocationNormalized
  ): void {
    // 更新面包屑
    RouteGuards.updateBreadcrumbs(to)
    
    // 更新底部导航状态
    RouteGuards.updateTabbar(to)
    
    // 页面加载完成，隐藏loading
    const loadingElement = document.getElementById('app-loading')
    if (loadingElement) {
      loadingElement.style.display = 'none'
    }
  }

  /**
   * 检查路由权限
   */
  static checkPermissions(route: RouteLocationNormalized): PermissionCheckResult {
    const userStore = useUserStore()
    const { permissions: userPermissions, roles: userRoles } = userStore
    
    const requiredPermissions = (route.meta?.permissions || []) as string[]
    const requiredRoles = (route.meta?.roles || []) as string[]

    // 检查权限
    const missingPermissions = requiredPermissions.filter(
      (permission: string) => !userPermissions.includes(permission)
    )

    // 检查角色
    const missingRoles = requiredRoles.filter(
      (role: string) => !userRoles.includes(role)
    )

    const hasPermission = missingPermissions.length === 0 && missingRoles.length === 0

    return {
      hasPermission,
      missingPermissions: missingPermissions.length > 0 ? missingPermissions : undefined,
      missingRoles: missingRoles.length > 0 ? missingRoles : undefined,
      redirectTo: !hasPermission ? '/403' : undefined
    }
  }

  /**
   * 更新面包屑
   */
  private static updateBreadcrumbs(route: RouteLocationNormalized): void {
    // 这里可以实现面包屑更新逻辑
    // 可以通过store或者事件总线来通知面包屑组件更新
    console.log('更新面包屑:', route.path)
  }

  /**
   * 更新底部导航状态
   */
  private static updateTabbar(route: RouteLocationNormalized): void {
    // 这里可以实现底部导航状态更新逻辑
    if (route.meta?.showInTabbar) {
      console.log('更新底部导航:', route.path)
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(permission: string): boolean {
    const userStore = useUserStore()
    return userStore.permissions.includes(permission)
  }

  /**
   * 检查用户是否有指定角色
   */
  static hasRole(role: string): boolean {
    const userStore = useUserStore()
    return userStore.roles.includes(role)
  }

  /**
   * 检查用户是否有任一权限
   */
  static hasAnyPermission(permissions: string[]): boolean {
    const userStore = useUserStore()
    return permissions.some(permission => userStore.permissions.includes(permission))
  }

  /**
   * 检查用户是否有任一角色
   */
  static hasAnyRole(roles: string[]): boolean {
    const userStore = useUserStore()
    return roles.some(role => userStore.roles.includes(role))
  }

  /**
   * 检查用户认证状态
   */
  static async checkAuthentication(route: RouteLocationNormalized): Promise<{
    isAuthenticated: boolean
    reason?: 'no_token' | 'token_expired' | 'token_invalid' | 'user_not_found'
  }> {
    const userStore = useUserStore()

    // 检查是否有token
    const userToken = storage.getLocal('user_token')
    if (!userToken) {
      return { isAuthenticated: false, reason: 'no_token' }
    }

    // 检查token是否过期
    if (token.isTokenExpired(userToken)) {
      // 尝试刷新token
      try {
        await userStore.refreshToken()
        return { isAuthenticated: true }
      } catch (error) {
        console.error('Token刷新失败:', error)
        // 清除过期的认证信息
        storage.removeLocal('user_token')
        storage.removeLocal('user_refresh_token')
        storage.removeLocal('user_info')
        return { isAuthenticated: false, reason: 'token_expired' }
      }
    }

    // 检查用户信息是否存在
    if (!userStore.userInfo) {
      try {
        // 尝试获取用户信息
        await userStore.getCurrentUser()
        return { isAuthenticated: true }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return { isAuthenticated: false, reason: 'user_not_found' }
      }
    }

    return { isAuthenticated: true }
  }

  /**
   * 显示页面加载状态
   */
  static showPageLoading(): void {
    const loadingElement = document.getElementById('app-loading')
    if (loadingElement) {
      loadingElement.style.display = 'flex'
    }
  }

  /**
   * 隐藏页面加载状态
   */
  static hidePageLoading(): void {
    const loadingElement = document.getElementById('app-loading')
    if (loadingElement) {
      loadingElement.style.display = 'none'
    }
  }

  /**
   * 检查路由是否为公开路由
   */
  static isPublicRoute(route: RouteLocationNormalized): boolean {
    const publicRoutes = ['/', '/home', '/shipping-rate', '/about', '/404', '/403']
    return publicRoutes.includes(route.path) || route.meta?.requiresAuth === false
  }

  /**
   * 获取用户友好的权限名称
   */
  static getPermissionDisplayName(permission: string): string {
    const permissionMap: Record<string, string> = {
      'order:read': '查看订单',
      'order:write': '管理订单',
      'bill:read': '查看账单',
      'bill:write': '管理账单',
      'invoice:read': '查看发票',
      'invoice:write': '管理发票',
      'profile:read': '查看个人信息',
      'profile:write': '修改个人信息',
      'notification:read': '查看通知',
      'notification:write': '管理通知'
    }

    return permissionMap[permission] || permission
  }
}
