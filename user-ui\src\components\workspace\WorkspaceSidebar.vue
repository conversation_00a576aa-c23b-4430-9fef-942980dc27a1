<!--
  工作台侧边栏组件
  
  @description 支持多级菜单和折叠功能的工作台侧边栏
  <AUTHOR>
  @date 2025-07-25 15:40:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="workspace-sidebar h-full bg-background border-r">
    <!-- 侧边栏头部 -->
    <div class="p-4 border-b">
      <h2 class="text-lg font-semibold text-foreground">工作台</h2>
    </div>

    <!-- 菜单列表 -->
    <nav class="p-2">
      <div v-for="item in menuItems" :key="item.href" class="mb-1">
        <!-- 一级菜单项 -->
        <div
          v-if="!item.children || item.children.length === 0"
          class="menu-item"
          :class="{ 'menu-item-active': isActiveRoute(item.href) }"
          @click="navigateTo(item.href)"
        >
          <component :is="getIconComponent(item.icon)" class="h-4 w-4" />
          <span class="ml-2">{{ item.title }}</span>
        </div>

        <!-- 有子菜单的菜单项 -->
        <div v-else>
          <div
            class="menu-item cursor-pointer"
            :class="{ 'menu-item-active': isParentActive(item) }"
            @click="toggleSubmenu(item.href)"
          >
            <component :is="getIconComponent(item.icon)" class="h-4 w-4" />
            <span class="ml-2 flex-1">{{ item.title }}</span>
            <ChevronDown
              class="h-4 w-4 transition-transform duration-200"
              :class="{ 'rotate-180': expandedMenus.includes(item.href) }"
            />
          </div>

          <!-- 子菜单 -->
          <Collapsible :open="expandedMenus.includes(item.href)">
            <CollapsibleContent class="ml-6 mt-1 space-y-1">
              <div
                v-for="child in item.children"
                :key="child.href"
                class="submenu-item"
                :class="{ 'submenu-item-active': isActiveRoute(child.href) }"
                @click="navigateTo(child.href)"
              >
                <component :is="getIconComponent(child.icon)" class="h-3 w-3" />
                <span class="ml-2 text-sm">{{ child.title }}</span>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible'
import {
  Package,
  CreditCard,
  FileText,
  Settings,
  User,
  Bell,
  ChevronDown,
  List,
  UserCircle,
  Users,
  Mail,
  Ticket
} from 'lucide-vue-next'
import { workspaceMenuItems } from '@/data/mockData'
import type { WorkspaceMenuItem } from '@/types/workspace'

const route = useRoute()
const router = useRouter()

// 展开的菜单项
const expandedMenus = ref<string[]>([])

// 菜单数据
const menuItems = computed(() => workspaceMenuItems)

// 图标组件映射
const iconComponents = {
  'package': Package,
  'credit-card': CreditCard,
  'file-text': FileText,
  'settings': Settings,
  'user': User,
  'bell': Bell,
  'list': List,
  'user-circle': UserCircle,
  'users': Users,
  'mail': Mail,
  'ticket': Ticket
}

/**
 * 获取图标组件
 */
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || Package
}

/**
 * 检查路由是否激活
 */
const isActiveRoute = (href: string) => {
  return route.path === href || route.path.startsWith(href + '/')
}

/**
 * 检查父菜单是否激活
 */
const isParentActive = (item: WorkspaceMenuItem) => {
  if (!item.children) return false
  return item.children.some(child => isActiveRoute(child.href))
}

/**
 * 切换子菜单展开状态
 */
const toggleSubmenu = (href: string) => {
  const index = expandedMenus.value.indexOf(href)
  if (index > -1) {
    expandedMenus.value.splice(index, 1)
  } else {
    expandedMenus.value.push(href)
  }
}

/**
 * 导航到指定路由
 */
const navigateTo = (href: string) => {
  router.push(href)
}

// 初始化时展开当前激活的父菜单
const initExpandedMenus = () => {
  menuItems.value.forEach(item => {
    if (item.children && isParentActive(item)) {
      if (!expandedMenus.value.includes(item.href)) {
        expandedMenus.value.push(item.href)
      }
    }
  })
}

// 组件挂载时初始化
initExpandedMenus()
</script>

<style scoped>
.menu-item {
  @apply flex items-center px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-accent transition-colors cursor-pointer;
}

.menu-item-active {
  @apply text-foreground bg-accent;
}

.submenu-item {
  @apply flex items-center px-3 py-2 rounded-md text-sm text-muted-foreground hover:text-foreground hover:bg-accent/50 transition-colors cursor-pointer;
}

.submenu-item-active {
  @apply text-foreground bg-accent/50;
}
</style>
