-- =====================================================
-- CLLCN国际物流平台数据库初始化脚本
-- 描述: 基于混合架构的物流平台独立数据库
-- 依赖: company_management_system 数据库（用户认证共享）
-- 创建时间: 2025-07-31
-- 架构模式: 用户认证共享 + 业务数据独立
-- 技术栈: Spring Boot 3.5.3 + MySQL 8.0 + JWT认证
-- =====================================================

-- 删除已存在的数据库（谨慎操作）
DROP DATABASE IF EXISTS `logistics_platform`;

-- 创建物流平台数据库
CREATE DATABASE IF NOT EXISTS `logistics_platform`
DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用物流平台数据库
USE `logistics_platform`;

-- =====================================================
-- 第一部分：用户映射和认证相关表
-- =====================================================

-- 用户映射表（缓存和统一用户信息）
CREATE TABLE `user_mapping` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '映射ID',
    `platform_user_id` VARCHAR(20) NOT NULL UNIQUE COMMENT '平台用户ID (E123/C456)',
    `source_table` ENUM('employee', 'client') NOT NULL COMMENT '来源表',
    `source_id` INT NOT NULL COMMENT '来源表ID',
    `user_type` ENUM('admin', 'manager', 'employee', 'client') NOT NULL COMMENT '用户类型',
    `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
    `email` VARCHAR(100) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '手机号',
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
    `department_id` INT COMMENT '部门ID（仅员工）',
    `employee_id` INT COMMENT '负责员工ID（仅客户）',
    `last_sync_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_source` (`source_table`, `source_id`),
    INDEX `idx_email` (`email`),
    INDEX `idx_user_type` (`user_type`),
    INDEX `idx_platform_user_id` (`platform_user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_employee_id` (`employee_id`)
) COMMENT '用户映射表';

-- 创建统一用户视图（实时同步公司管理系统用户数据）
CREATE VIEW `v_platform_users` AS
SELECT
    CONCAT('E', employee_id) as platform_user_id,
    'employee' as source_table,
    employee_id as source_id,
    CASE
        WHEN UPPER(role) = 'ADMIN' THEN 'admin'
        WHEN UPPER(role) = 'MANAGER' THEN 'manager'
        ELSE 'employee'
    END as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE WHEN status = 'Active' THEN 'active' ELSE 'inactive' END as status,
    department_id,
    NULL as employee_id,
    UPPER(role) as original_role,
    update_time as last_sync_time
FROM company_management_system.employee
WHERE status = 'Active'

UNION ALL

SELECT
    CONCAT('C', client_id) as platform_user_id,
    'client' as source_table,
    client_id as source_id,
    'client' as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE
        WHEN status IN ('审核通过', '已合作') THEN 'active'
        ELSE 'inactive'
    END as status,
    NULL as department_id,
    employee_id,
    'CLIENT' as original_role,
    update_time as last_sync_time
FROM company_management_system.client
WHERE password IS NOT NULL
AND password != '';

-- =====================================================
-- 第二部分：基础数据表
-- =====================================================

-- 首页轮播图表
CREATE TABLE `banner` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '轮播图ID',
    `title` VARCHAR(200) COMMENT '标题',
    `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `link_url` VARCHAR(500) COMMENT '链接URL',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_banner_status_sort` (`status`, `sort_order`)
) COMMENT '轮播图表';

-- 国家表
CREATE TABLE `country` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '国家ID',
    `country_code` VARCHAR(10) NOT NULL UNIQUE COMMENT '国家代码',
    `country_name_en` VARCHAR(100) NOT NULL COMMENT '国家英文名',
    `country_name_cn` VARCHAR(100) NOT NULL COMMENT '国家中文名',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_country_code` (`country_code`),
    INDEX `idx_country_status` (`status`)
) COMMENT '国家表';

-- 港口表
CREATE TABLE `port` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '港口ID',
    `port_code` VARCHAR(10) NOT NULL UNIQUE COMMENT '港口代码',
    `port_name_en` VARCHAR(100) NOT NULL COMMENT '港口英文名',
    `port_name_cn` VARCHAR(100) NOT NULL COMMENT '港口中文名',
    `country_id` BIGINT NOT NULL COMMENT '所属国家ID',
    `is_hot` TINYINT NOT NULL DEFAULT 0 COMMENT '是否热门港口:0-否,1-是',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY `fk_port_country_id` (`country_id`) REFERENCES `country`(`id`),
    INDEX `idx_port_country_id` (`country_id`),
    INDEX `idx_port_hot` (`is_hot`),
    INDEX `idx_port_name_en` (`port_name_en`),
    INDEX `idx_port_name_cn` (`port_name_cn`),
    INDEX `idx_port_status` (`status`)
) COMMENT '港口表';

-- 船司表
CREATE TABLE `shipping_company` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '船司ID',
    `company_code` VARCHAR(20) NOT NULL UNIQUE COMMENT '船司代码',
    `company_name_en` VARCHAR(200) NOT NULL COMMENT '船司英文名',
    `company_name_cn` VARCHAR(200) NOT NULL COMMENT '船司中文名',
    `logo_url` VARCHAR(500) COMMENT 'Logo图片URL',
    `contact_info` TEXT COMMENT '联系信息',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_shipping_company_code` (`company_code`),
    INDEX `idx_shipping_company_status` (`status`)
) COMMENT '船司表';

-- =====================================================
-- 第三部分：航线运价相关表
-- =====================================================

-- 集装箱类型表
CREATE TABLE `container_type` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '箱型ID',
    `type_code` VARCHAR(10) NOT NULL UNIQUE COMMENT '箱型代码',
    `type_name` VARCHAR(50) NOT NULL COMMENT '箱型名称',
    `description` VARCHAR(200) COMMENT '描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_container_type_status` (`status`)
) COMMENT '集装箱类型表';

-- 费用类型表
CREATE TABLE `fee_type` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '费用类型ID',
    `fee_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '费用代码',
    `fee_name` VARCHAR(100) NOT NULL COMMENT '费用名称',
    `fee_category` TINYINT NOT NULL COMMENT '费用类别:1-海运费,2-附加费,3-港口费,4-其他费用',
    `charge_unit` TINYINT NOT NULL COMMENT '计费单位:1-按箱型,2-按箱,3-按票',
    `payment_method` TINYINT NOT NULL DEFAULT 1 COMMENT '付款方式:1-预付,2-到付',
    `description` VARCHAR(500) COMMENT '费用描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_fee_type_category` (`fee_category`),
    INDEX `idx_fee_type_status` (`status`)
) COMMENT '费用类型表';

-- 航线表
CREATE TABLE `route` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '航线ID',
    `route_code` VARCHAR(50) NOT NULL COMMENT '航线代码',
    `shipping_company_id` BIGINT NOT NULL COMMENT '船司ID',
    `origin_port_id` BIGINT NOT NULL COMMENT '起运港ID',
    `destination_port_id` BIGINT NOT NULL COMMENT '目的港ID',
    `transit_port_id` BIGINT COMMENT '中转港ID',
    `voyage_days` INT NOT NULL COMMENT '航程天数',
    `is_direct` TINYINT NOT NULL DEFAULT 1 COMMENT '是否直达:0-中转,1-直达',
    `is_special` TINYINT NOT NULL DEFAULT 0 COMMENT '是否特惠航线:0-否,1-是',
    `cut_off_time` TIME COMMENT '截关时间',
    `etd_time` DATETIME COMMENT '预计开船时间',
    `atd_time` DATETIME COMMENT '实际开船时间',
    `valid_from` DATE NOT NULL COMMENT '有效期开始',
    `valid_to` DATE NOT NULL COMMENT '有效期结束',
    `route_remark` TEXT COMMENT '航线特别说明',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY `fk_route_shipping_company_id` (`shipping_company_id`) REFERENCES `shipping_company`(`id`),
    FOREIGN KEY `fk_route_origin_port_id` (`origin_port_id`) REFERENCES `port`(`id`),
    FOREIGN KEY `fk_route_destination_port_id` (`destination_port_id`) REFERENCES `port`(`id`),
    FOREIGN KEY `fk_route_transit_port_id` (`transit_port_id`) REFERENCES `port`(`id`),
    INDEX `idx_route_ports` (`origin_port_id`, `destination_port_id`),
    INDEX `idx_route_company` (`shipping_company_id`),
    INDEX `idx_route_valid` (`valid_from`, `valid_to`),
    INDEX `idx_route_status` (`status`),
    INDEX `idx_route_special` (`is_special`)
) COMMENT '航线表';

-- 运价表
CREATE TABLE `pricing` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '运价ID',
    `route_id` BIGINT NOT NULL COMMENT '航线ID',
    `fee_type_id` BIGINT NOT NULL COMMENT '费用类型ID',
    `container_type_id` BIGINT COMMENT '集装箱类型ID(按箱型计费时必填)',
    `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
    `currency` VARCHAR(10) NOT NULL DEFAULT 'USD' COMMENT '币种',
    `remark` VARCHAR(500) COMMENT '备注',
    `valid_from` DATE NOT NULL COMMENT '有效期开始',
    `valid_to` DATE NOT NULL COMMENT '有效期结束',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY `fk_pricing_route_id` (`route_id`) REFERENCES `route`(`id`),
    FOREIGN KEY `fk_pricing_fee_type_id` (`fee_type_id`) REFERENCES `fee_type`(`id`),
    FOREIGN KEY `fk_pricing_container_type_id` (`container_type_id`) REFERENCES `container_type`(`id`),
    INDEX `idx_pricing_route` (`route_id`),
    INDEX `idx_pricing_valid` (`valid_from`, `valid_to`),
    INDEX `idx_pricing_status` (`status`),
    UNIQUE KEY `uk_pricing_route_fee_container` (`route_id`, `fee_type_id`, `container_type_id`, `valid_from`)
) COMMENT '运价表';

-- 运价历史表
CREATE TABLE `pricing_history` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史ID',
    `route_id` BIGINT NOT NULL COMMENT '航线ID',
    `fee_type_id` BIGINT NOT NULL COMMENT '费用类型ID',
    `container_type_id` BIGINT COMMENT '集装箱类型ID',
    `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
    `currency` VARCHAR(10) NOT NULL COMMENT '币种',
    `record_date` DATE NOT NULL COMMENT '记录日期',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_pricing_history_route_date` (`route_id`, `record_date`),
    INDEX `idx_pricing_history_date` (`record_date`)
) COMMENT '运价历史表';

-- =====================================================
-- 第四部分：订单相关表
-- =====================================================

-- 订单表（关联用户映射表）
CREATE TABLE `order_info` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    `order_no` VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    `platform_user_id` VARCHAR(20) NOT NULL COMMENT '平台用户ID (E123/C456)',
    `route_id` BIGINT NOT NULL COMMENT '航线ID',
    `order_status` TINYINT NOT NULL DEFAULT 1 COMMENT '订单状态:1-待确认,2-已确认,3-进行中,4-已完成,5-已取消',
    `logistics_status` TINYINT NOT NULL DEFAULT 1 COMMENT '物流状态:1-订舱,2-装箱,3-开船,4-在途,5-到港,6-清关,7-提货,8-完成',
    `total_amount` DECIMAL(12,2) COMMENT '订单总金额',
    `currency` VARCHAR(10) DEFAULT 'USD' COMMENT '币种',
    `customer_ref_no` VARCHAR(100) COMMENT '客户委托号',
    `so_no` VARCHAR(100) COMMENT 'SO号',
    `hbl_no` VARCHAR(100) COMMENT 'HBL号',
    `mbl_no` VARCHAR(100) COMMENT 'MBL号',
    `vessel_name` VARCHAR(100) COMMENT '船名',
    `voyage` VARCHAR(50) COMMENT '航次',
    `order_source` TINYINT NOT NULL DEFAULT 1 COMMENT '订单来源:1-网站,2-API,3-客服',
    `booking_remark` TEXT COMMENT '订舱委托备注',
    `door_to_door_ref` VARCHAR(100) COMMENT '门到门报价方案单号',
    `contact_name` VARCHAR(50) COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) COMMENT '联系人电话',
    `contact_email` VARCHAR(100) COMMENT '联系人邮箱',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY `fk_order_route_id` (`route_id`) REFERENCES `route`(`id`),
    INDEX `idx_order_platform_user_id` (`platform_user_id`),
    INDEX `idx_order_status` (`order_status`),
    INDEX `idx_order_logistics_status` (`logistics_status`),
    INDEX `idx_order_create_time` (`create_time`),
    INDEX `idx_order_no` (`order_no`)
) COMMENT '订单表';

-- 订单集装箱表
CREATE TABLE `order_container` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单集装箱ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `container_type_id` BIGINT NOT NULL COMMENT '集装箱类型ID',
    `quantity` INT NOT NULL COMMENT '箱量',
    `weight` DECIMAL(8,2) COMMENT '单柜重量(吨)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY `fk_order_container_order_id` (`order_id`) REFERENCES `order_info`(`id`),
    FOREIGN KEY `fk_order_container_type_id` (`container_type_id`) REFERENCES `container_type`(`id`),
    INDEX `idx_order_container_order_id` (`order_id`)
) COMMENT '订单集装箱表';

-- 订单货物表
CREATE TABLE `order_cargo` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单货物ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `order_container_id` BIGINT NOT NULL COMMENT '订单集装箱ID',
    `hs_code` VARCHAR(20) COMMENT 'HS编码',
    `cargo_name_cn` VARCHAR(200) NOT NULL COMMENT '中文品名',
    `cargo_name_en` VARCHAR(200) NOT NULL COMMENT '英文品名',
    `cargo_nature` TINYINT NOT NULL DEFAULT 1 COMMENT '货物性质:1-普通货物,2-危险品,3-冷藏货物',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY `fk_order_cargo_order_id` (`order_id`) REFERENCES `order_info`(`id`),
    FOREIGN KEY `fk_order_cargo_container_id` (`order_container_id`) REFERENCES `order_container`(`id`),
    INDEX `idx_order_cargo_order_id` (`order_id`),
    INDEX `idx_order_cargo_container_id` (`order_container_id`)
) COMMENT '订单货物表';

-- 订单附件表
CREATE TABLE `order_attachment` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '附件ID',
    `order_id` BIGINT NOT NULL COMMENT '订单ID',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
    `file_size` BIGINT COMMENT '文件大小(字节)',
    `file_type` VARCHAR(50) COMMENT '文件类型',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY `fk_order_attachment_order_id` (`order_id`) REFERENCES `order_info`(`id`),
    INDEX `idx_order_attachment_order_id` (`order_id`)
) COMMENT '订单附件表';

-- =====================================================
-- 第五部分：个人中心相关表
-- =====================================================

-- 联系人表
CREATE TABLE `contact` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '联系人ID',
    `platform_user_id` VARCHAR(20) NOT NULL COMMENT '平台用户ID',
    `contact_name` VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    `contact_email` VARCHAR(100) NOT NULL COMMENT '联系人邮箱',
    `contact_phone` VARCHAR(20) COMMENT '联系人电话',
    `company_name` VARCHAR(200) COMMENT '公司名称',
    `remark` VARCHAR(500) COMMENT '备注信息',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_contact_platform_user_id` (`platform_user_id`)
) COMMENT '联系人表';

-- 卡券表
CREATE TABLE `coupon` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '卡券ID',
    `coupon_name` VARCHAR(100) NOT NULL COMMENT '卡券名称',
    `coupon_type` TINYINT NOT NULL COMMENT '卡券类型:1-运费优惠券,2-新用户优惠券,3-节日活动券,4-积分兑换券',
    `discount_type` TINYINT NOT NULL COMMENT '折扣类型:1-固定金额,2-百分比折扣',
    `discount_value` DECIMAL(10,2) NOT NULL COMMENT '折扣值',
    `min_amount` DECIMAL(10,2) COMMENT '最低使用金额',
    `valid_from` DATETIME NOT NULL COMMENT '有效期开始',
    `valid_to` DATETIME NOT NULL COMMENT '有效期结束',
    `total_quantity` INT NOT NULL COMMENT '发放总量',
    `used_quantity` INT NOT NULL DEFAULT 0 COMMENT '已使用数量',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_coupon_valid` (`valid_from`, `valid_to`),
    INDEX `idx_coupon_type` (`coupon_type`),
    INDEX `idx_coupon_status` (`status`)
) COMMENT '卡券表';

-- 用户卡券表
CREATE TABLE `user_coupon` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户卡券ID',
    `platform_user_id` VARCHAR(20) NOT NULL COMMENT '平台用户ID',
    `coupon_id` BIGINT NOT NULL COMMENT '卡券ID',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-未使用,2-已使用,3-已过期',
    `used_time` DATETIME COMMENT '使用时间',
    `order_id` BIGINT COMMENT '使用订单ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY `fk_user_coupon_coupon_id` (`coupon_id`) REFERENCES `coupon`(`id`),
    FOREIGN KEY `fk_user_coupon_order_id` (`order_id`) REFERENCES `order_info`(`id`),
    INDEX `idx_user_coupon_platform_user_id` (`platform_user_id`),
    INDEX `idx_user_coupon_status` (`status`)
) COMMENT '用户卡券表';

-- 消息通知表
CREATE TABLE `message` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    `platform_user_id` VARCHAR(20) NOT NULL COMMENT '平台用户ID',
    `message_title` VARCHAR(200) NOT NULL COMMENT '消息标题',
    `message_content` TEXT NOT NULL COMMENT '消息内容',
    `message_type` TINYINT NOT NULL COMMENT '消息类型:1-系统通知,2-订单通知,3-营销通知,4-认证通知',
    `priority` TINYINT NOT NULL DEFAULT 1 COMMENT '重要程度:1-普通,2-重要,3-紧急',
    `read_status` TINYINT NOT NULL DEFAULT 0 COMMENT '阅读状态:0-未读,1-已读',
    `read_time` DATETIME COMMENT '阅读时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_message_platform_user_id` (`platform_user_id`),
    INDEX `idx_message_read_status` (`read_status`),
    INDEX `idx_message_type` (`message_type`),
    INDEX `idx_message_create_time` (`create_time`)
) COMMENT '消息通知表';

-- =====================================================
-- 第六部分：系统相关表
-- =====================================================

-- 搜索历史表
CREATE TABLE `search_history` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '搜索历史ID',
    `platform_user_id` VARCHAR(20) COMMENT '平台用户ID(可为空,支持游客)',
    `search_type` TINYINT NOT NULL COMMENT '搜索类型:1-港口搜索,2-航线搜索',
    `search_keyword` VARCHAR(200) NOT NULL COMMENT '搜索关键词',
    `search_result_count` INT COMMENT '搜索结果数量',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_search_history_platform_user_id` (`platform_user_id`),
    INDEX `idx_search_history_type` (`search_type`),
    INDEX `idx_search_history_time` (`create_time`)
) COMMENT '搜索历史表';

-- 系统配置表
CREATE TABLE `system_config` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    `config_value` TEXT NOT NULL COMMENT '配置值',
    `config_desc` VARCHAR(500) COMMENT '配置描述',
    `config_type` VARCHAR(50) NOT NULL COMMENT '配置类型',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_system_config_key` (`config_key`)
) COMMENT '系统配置表';

-- =====================================================
-- 第七部分：初始化基础数据
-- =====================================================

-- 初始化集装箱类型数据
INSERT INTO `container_type` (`type_code`, `type_name`, `description`) VALUES
('20GP', '20英尺普通柜', '20英尺标准集装箱'),
('40GP', '40英尺普通柜', '40英尺标准集装箱'),
('40HQ', '40英尺高柜', '40英尺高箱集装箱'),
('40NOR', '40英尺开顶柜', '40英尺开顶集装箱'),
('45HQ', '45英尺高柜', '45英尺高箱集装箱');

-- 初始化费用类型数据
INSERT INTO `fee_type` (`fee_code`, `fee_name`, `fee_category`, `charge_unit`, `payment_method`, `description`) VALUES
('OCEAN_FREIGHT', '海运费', 1, 1, 1, '基础海运运输费用'),
('DG_SURCHARGE', '海运危险品附加费', 2, 1, 1, '危险品货物附加费用'),
('DOC_FEE', '起运港文件费', 3, 3, 1, '起运港单据处理费'),
('SECURITY_FEE', '安保费', 2, 2, 1, '安全检查费用'),
('THC_ORIGIN', '起运港码头费', 3, 1, 1, '起运港码头操作费'),
('EIR_FEE', '设备交接单费', 4, 2, 1, '设备交接单处理费'),
('SEAL_FEE', '封条费', 4, 2, 1, '集装箱封条费用');

-- 初始化国家数据（示例）
INSERT INTO `country` (`country_code`, `country_name_en`, `country_name_cn`) VALUES
('CN', 'China', '中国'),
('US', 'United States', '美国'),
('JP', 'Japan', '日本'),
('KR', 'South Korea', '韩国'),
('SG', 'Singapore', '新加坡'),
('DE', 'Germany', '德国'),
('GB', 'United Kingdom', '英国'),
('NL', 'Netherlands', '荷兰');

-- 初始化港口数据（示例）
INSERT INTO `port` (`port_code`, `port_name_en`, `port_name_cn`, `country_id`, `is_hot`) VALUES
('CNSHA', 'Shanghai', '上海', 1, 1),
('CNNGB', 'Ningbo', '宁波', 1, 1),
('CNSZX', 'Shenzhen', '深圳', 1, 1),
('CNQIN', 'Qingdao', '青岛', 1, 1),
('USLAX', 'Los Angeles', '洛杉矶', 2, 1),
('USLGB', 'Long Beach', '长滩', 2, 1),
('USNYC', 'New York', '纽约', 2, 1),
('JPTYO', 'Tokyo', '东京', 3, 1),
('KRPUS', 'Busan', '釜山', 4, 1),
('SGSIN', 'Singapore', '新加坡', 5, 1);

-- 初始化船司数据（示例）
INSERT INTO `shipping_company` (`company_code`, `company_name_en`, `company_name_cn`) VALUES
('MSK', 'Maersk Line', '马士基'),
('MSC', 'Mediterranean Shipping Company', '地中海航运'),
('CMA', 'CMA CGM', '达飞轮船'),
('COSCO', 'COSCO Shipping', '中远海运'),
('HAPAG', 'Hapag-Lloyd', '赫伯罗特'),
('ONE', 'Ocean Network Express', 'ONE'),
('EVERGREEN', 'Evergreen Line', '长荣海运'),
('YANG', 'Yang Ming', '阳明海运');

-- 初始化系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`, `config_type`) VALUES
('system.name', 'CLLCN国际物流平台', '系统名称', 'system'),
('system.version', '1.0.0', '系统版本', 'system'),
('file.upload.max.size', '10485760', '文件上传最大大小(字节)', 'file'),
('file.upload.allowed.types', 'pdf,doc,docx,xls,xlsx,jpg,png', '允许上传的文件类型', 'file'),
('order.auto.cancel.hours', '24', '订单自动取消时间(小时)', 'order'),
('pricing.default.currency', 'USD', '默认币种', 'pricing');

-- =====================================================
-- 第八部分：数据同步和验证
-- =====================================================

-- 同步用户映射表（从视图同步数据，允许邮箱为空）
INSERT INTO `user_mapping` (
    `platform_user_id`, `source_table`, `source_id`, `user_type`,
    `real_name`, `email`, `phone`, `status`, `department_id`, `employee_id`
)
SELECT
    platform_user_id, source_table, source_id, user_type,
    real_name, email, mobile, status, department_id, employee_id
FROM `v_platform_users`;

-- =====================================================
-- 第九部分：数据验证和完整性检查
-- =====================================================

-- 验证表创建情况
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '数据行数'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'logistics_platform'
ORDER BY TABLE_NAME;

-- 验证外键约束
SELECT
    TABLE_NAME as '表名',
    CONSTRAINT_NAME as '约束名',
    CONSTRAINT_TYPE as '约束类型'
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = 'logistics_platform'
AND CONSTRAINT_TYPE = 'FOREIGN KEY'
ORDER BY TABLE_NAME;

-- 验证索引创建
SELECT
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as '索引列'
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = 'logistics_platform'
AND INDEX_NAME != 'PRIMARY'
GROUP BY TABLE_NAME, INDEX_NAME
ORDER BY TABLE_NAME, INDEX_NAME;

-- 验证用户视图数据
SELECT
    user_type as '用户类型',
    COUNT(*) as '用户数量',
    COUNT(CASE WHEN status = 'active' THEN 1 END) as '活跃用户数'
FROM `v_platform_users`
GROUP BY user_type;

-- 验证基础数据初始化
SELECT '集装箱类型' as '数据类型', COUNT(*) as '记录数' FROM `container_type`
UNION ALL
SELECT '费用类型' as '数据类型', COUNT(*) as '记录数' FROM `fee_type`
UNION ALL
SELECT '国家' as '数据类型', COUNT(*) as '记录数' FROM `country`
UNION ALL
SELECT '港口' as '数据类型', COUNT(*) as '记录数' FROM `port`
UNION ALL
SELECT '船司' as '数据类型', COUNT(*) as '记录数' FROM `shipping_company`
UNION ALL
SELECT '用户映射' as '数据类型', COUNT(*) as '记录数' FROM `user_mapping`;

-- =====================================================
-- 完成提示
-- =====================================================

SELECT
    '物流平台数据库初始化完成！' as message,
    '请检查上述验证结果，确保所有表和数据都已正确创建。' as note,
    '下一步：配置应用程序连接两个数据库，开发统一认证API' as next_step;


-- =====================================================
-- 第一步：更新 logistics_platform 中的视图
-- =====================================================

USE logistics_platform;

-- 重新创建视图，与 company_management_system 中的视图保持一致
CREATE OR REPLACE VIEW `v_platform_users` AS
SELECT
    CONCAT('E', employee_id) as platform_user_id,
    'employee' as source_table,
    employee_id as source_id,
    CASE
        WHEN role = 'admin' THEN 'admin'
        WHEN role = 'manager' THEN 'manager'
        ELSE 'employee'
        END as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE WHEN status = 'Active' THEN 'active' ELSE 'inactive' END as status,
    department_id,
    NULL as employee_id,
    role as original_role,
    update_time as last_sync_time
FROM company_management_system.employee
WHERE status = 'Active'

UNION ALL

SELECT
    CONCAT('C', client_id) as platform_user_id,
    'client' as source_table,
    client_id as source_id,
    'client' as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE
        WHEN status IN ('审核通过', '已合作') THEN 'active'
        ELSE 'inactive'
        END as status,
    NULL as department_id,
    employee_id,
    'client' as original_role,
    update_time as last_sync_time
FROM company_management_system.client
WHERE password IS NOT NULL
  AND password != '';

-- =====================================================
-- 第二步：重新同步用户映射表
-- =====================================================

-- 清空现有映射数据
TRUNCATE TABLE `user_mapping`;

-- 重新同步所有用户数据
INSERT INTO `user_mapping` (
    `platform_user_id`, `source_table`, `source_id`, `user_type`,
    `real_name`, `email`, `phone`, `status`, `department_id`, `employee_id`
)
SELECT
    platform_user_id, source_table, source_id, user_type,
    real_name, email, mobile, status, department_id, employee_id
FROM `v_platform_users`;

-- =====================================================
-- 第三步：验证同步结果
-- =====================================================

-- 验证用户映射表数据
SELECT
    '用户映射表同步结果' as '验证项目',
    user_type as '用户类型',
    status as '状态',
    COUNT(*) as '数量'
FROM `user_mapping`
GROUP BY user_type, status
ORDER BY user_type, status;

-- 对比视图和映射表的数据一致性
SELECT
    '数据一致性检查' as '检查项目',
    '视图数据' as '数据源',
    COUNT(*) as '记录数'
FROM `v_platform_users`
UNION ALL
SELECT
    '数据一致性检查' as '检查项目',
    '映射表数据' as '数据源',
    COUNT(*) as '记录数'
FROM `user_mapping`;

-- 检查客户数据是否完整
SELECT
    '客户数据检查' as '检查项目',
    '原始客户表(有密码)' as '数据源',
    COUNT(*) as '记录数'
FROM company_management_system.client
WHERE password IS NOT NULL AND password != ''
UNION ALL
SELECT
    '客户数据检查' as '检查项目',
    '视图中客户' as '数据源',
    COUNT(*) as '记录数'
FROM `v_platform_users`
WHERE user_type = 'client'
UNION ALL
SELECT
    '客户数据检查' as '检查项目',
    '映射表中客户' as '数据源',
    COUNT(*) as '记录数'
FROM `user_mapping`
WHERE user_type = 'client';

-- =====================================================
-- 完成提示
-- =====================================================

SELECT
    '同步完成！' as message,
    '现在所有有密码的客户都可以登录物流平台了' as note,
    '请检查上述验证结果确保数据同步正确' as suggestion;