package com.example.cllcnplatformbackend.controller;

import com.example.cllcnplatformbackend.utils.PasswordEncoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 密码管理控制器
 * 提供密码强度检查、密码升级状态查询等功能
 * 
 * <AUTHOR> Platform
 * @since 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/api/password")
@RequiredArgsConstructor
public class PasswordController {
    
    private final PasswordEncoder passwordEncoder;
    
    /**
     * 检查密码强度
     * @param request 密码检查请求
     * @return 密码强度信息
     */
    @PostMapping("/check-strength")
    public ResponseEntity<Map<String, Object>> checkPasswordStrength(@RequestBody PasswordCheckRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String password = request.getPassword();
            
            if (password == null || password.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "密码不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            boolean isStrong = passwordEncoder.isPasswordStrong(password);
            String advice = passwordEncoder.getPasswordStrengthAdvice(password);
            
            response.put("success", true);
            response.put("isStrong", isStrong);
            response.put("advice", advice);
            response.put("message", "密码强度检查完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("密码强度检查失败", e);
            response.put("success", false);
            response.put("message", "密码强度检查失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取密码加密算法信息
     * @param request 密码算法查询请求
     * @return 密码算法信息
     */
    @PostMapping("/algorithm-info")
    public ResponseEntity<Map<String, Object>> getPasswordAlgorithmInfo(@RequestBody PasswordAlgorithmRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String encodedPassword = request.getEncodedPassword();
            
            if (encodedPassword == null || encodedPassword.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "加密密码不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            String algorithmType = passwordEncoder.getAlgorithmType(encodedPassword);
            boolean needsUpgrade = passwordEncoder.needsUpgrade(encodedPassword);
            
            response.put("success", true);
            response.put("algorithmType", algorithmType);
            response.put("needsUpgrade", needsUpgrade);
            response.put("message", "密码算法信息获取成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取密码算法信息失败", e);
            response.put("success", false);
            response.put("message", "获取密码算法信息失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 密码检查请求
     */
    public static class PasswordCheckRequest {
        private String password;
        
        public String getPassword() {
            return password;
        }
        
        public void setPassword(String password) {
            this.password = password;
        }
    }
    
    /**
     * 密码算法查询请求
     */
    public static class PasswordAlgorithmRequest {
        private String encodedPassword;
        
        public String getEncodedPassword() {
            return encodedPassword;
        }
        
        public void setEncodedPassword(String encodedPassword) {
            this.encodedPassword = encodedPassword;
        }
    }
}
