package com.example.cllcnplatformbackend.mapper;

import com.example.cllcnplatformbackend.entity.company.Employee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工Mapper接口（公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-08-01
 */
@Mapper
public interface EmployeeMapper {
    
    /**
     * 根据邮箱查找员工
     */
    Employee findByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查找员工
     */
    Employee findByPhone(@Param("phone") String phone);
    
    /**
     * 根据邮箱和状态查找员工（用于认证）
     */
    Employee findByEmailAndStatus(@Param("email") String email, @Param("status") Employee.Status status);

    /**
     * 根据手机号和状态查找员工（用于认证）
     */
    Employee findByPhoneAndStatus(@Param("phone") String phone, @Param("status") Employee.Status status);

    /**
     * 根据员工ID和状态查找员工（用于认证）
     */
    Employee findByEmployeeIdAndStatus(@Param("employeeId") Long employeeId, @Param("status") Employee.Status status);
    
    /**
     * 根据员工ID查找员工
     */
    Employee findByEmployeeId(@Param("employeeId") Long employeeId);
    
    /**
     * 根据部门ID查找激活员工
     */
    List<Employee> findActiveByDepartmentId(@Param("departmentId") Long departmentId);
    
    /**
     * 根据角色查找激活员工
     */
    List<Employee> findActiveByRole(@Param("role") Employee.Role role);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(@Param("email") String email);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(@Param("phone") String phone);
    
    /**
     * 插入员工
     */
    int insert(Employee employee);
    
    /**
     * 更新员工
     */
    int update(Employee employee);

    /**
     * 更新员工密码
     */
    int updatePassword(@Param("employeeId") Long employeeId, @Param("password") String password);

    /**
     * 删除员工
     */
    int deleteById(@Param("employeeId") Long employeeId);
}
