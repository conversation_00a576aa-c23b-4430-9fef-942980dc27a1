/**
 * API服务统一导出
 * 
 * @description 提供统一的API调用接口，方便业务层使用
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */

import httpClient from './http'
import type { 
  LoginCredentials, 
  LoginResponse, 
  UserInfo,
  PaginationParams,
  PaginationResponse
} from '@/types/api'

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  login: (credentials: LoginCredentials): Promise<LoginResponse> => {
    return httpClient.post<LoginResponse>('/auth/login', credentials)
  },

  /**
   * 用户登出
   */
  logout: (): Promise<void> => {
    return httpClient.post<void>('/auth/logout')
  },

  /**
   * 刷新token
   */
  refreshToken: (refreshToken: string): Promise<LoginResponse> => {
    return httpClient.post<LoginResponse>('/auth/refresh', { refreshToken })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: (): Promise<UserInfo> => {
    return httpClient.get<UserInfo>('/auth/me')
  }
}

/**
 * 用户管理API
 */
export const userApi = {
  /**
   * 获取用户列表
   */
  getUsers: (params: PaginationParams): Promise<PaginationResponse<UserInfo>> => {
    return httpClient.get<PaginationResponse<UserInfo>>('/users', { params })
  },

  /**
   * 获取用户详情
   */
  getUser: (id: string): Promise<UserInfo> => {
    return httpClient.get<UserInfo>(`/users/${id}`)
  },

  /**
   * 创建用户
   */
  createUser: (userData: Partial<UserInfo>): Promise<UserInfo> => {
    return httpClient.post<UserInfo>('/users', userData)
  },

  /**
   * 更新用户
   */
  updateUser: (id: string, userData: Partial<UserInfo>): Promise<UserInfo> => {
    return httpClient.put<UserInfo>(`/users/${id}`, userData)
  },

  /**
   * 删除用户
   */
  deleteUser: (id: string): Promise<void> => {
    return httpClient.delete<void>(`/users/${id}`)
  }
}

/**
 * 系统配置API
 */
export const systemApi = {
  /**
   * 获取系统配置
   */
  getConfigs: (): Promise<Record<string, any>> => {
    return httpClient.get<Record<string, any>>('/system/configs')
  },

  /**
   * 更新系统配置
   */
  updateConfigs: (configs: Record<string, any>): Promise<void> => {
    return httpClient.put<void>('/system/configs', configs)
  },

  /**
   * 获取系统状态
   */
  getSystemStatus: (): Promise<{
    status: 'healthy' | 'warning' | 'error'
    uptime: number
    version: string
    environment: string
  }> => {
    return httpClient.get('/system/status')
  }
}

/**
 * 文件上传API
 */
export const uploadApi = {
  /**
   * 上传单个文件
   */
  uploadFile: (file: File, onProgress?: (progress: number) => void): Promise<{
    url: string
    filename: string
    size: number
  }> => {
    const formData = new FormData()
    formData.append('file', file)

    return httpClient.post('/upload/file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })
  },

  /**
   * 上传多个文件
   */
  uploadFiles: (files: File[]): Promise<Array<{
    url: string
    filename: string
    size: number
  }>> => {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })

    return httpClient.post('/upload/files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 导出HTTP客户端实例，供特殊场景使用
export { httpClient }

// 导出所有API
export default {
  auth: authApi,
  user: userApi,
  system: systemApi,
  upload: uploadApi
}
