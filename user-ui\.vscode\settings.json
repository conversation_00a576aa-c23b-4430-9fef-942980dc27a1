{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "vue.codeActions.enabled": true, "vue.complete.casing.tags": "kebab", "vue.complete.casing.props": "camel", "vue.server.hybridMode": true, "files.associations": {"*.vue": "vue"}, "emmet.includeLanguages": {"vue": "html"}, "editor.quickSuggestions": {"strings": true}, "[vue]": {"editor.defaultFormatter": "Vue.volar"}}