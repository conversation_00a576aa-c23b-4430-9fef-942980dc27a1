<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.cllcnplatformbackend.mapper.EmployeeMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.cllcnplatformbackend.entity.company.Employee">
        <id column="employee_id" property="employeeId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="role" property="role" jdbcType="VARCHAR" 
                typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="status" property="status" jdbcType="VARCHAR" 
                typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="department_id" property="departmentId" jdbcType="BIGINT"/>
        <result column="position_id" property="positionId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        employee_id, name, email, phone, id_card, password, role, status, 
        department_id, position_id, create_time, update_time
    </sql>

    <!-- 不包含密码的列 -->
    <sql id="Column_List_No_Password">
        employee_id, name, email, phone, id_card, role, status, 
        department_id, position_id, create_time, update_time
    </sql>

    <!-- 根据邮箱查找员工 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM employee
        WHERE email = #{email}
    </select>

    <!-- 根据手机号查找员工 -->
    <select id="findByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM employee
        WHERE phone = #{phone}
    </select>

    <!-- 根据邮箱和状态查找员工（用于认证） -->
    <select id="findByEmailAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM employee
        WHERE email = #{email} AND status = #{status}
    </select>

    <!-- 根据手机号和状态查找员工（用于认证） -->
    <select id="findByPhoneAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM employee
        WHERE phone = #{phone} AND status = #{status}
    </select>

    <!-- 根据员工ID和状态查找员工（用于认证） -->
    <select id="findByEmployeeIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM employee
        WHERE employee_id = #{employeeId} AND status = #{status}
    </select>

    <!-- 根据员工ID查找员工 -->
    <select id="findByEmployeeId" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM employee
        WHERE employee_id = #{employeeId}
    </select>

    <!-- 根据部门ID查找激活员工 -->
    <select id="findActiveByDepartmentId" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM employee
        WHERE department_id = #{departmentId} AND status = 'ACTIVE'
    </select>

    <!-- 根据角色查找激活员工 -->
    <select id="findActiveByRole" resultMap="BaseResultMap">
        SELECT <include refid="Column_List_No_Password"/>
        FROM employee
        WHERE role = #{role} AND status = 'ACTIVE'
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM employee
        WHERE email = #{email}
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM employee
        WHERE phone = #{phone}
    </select>

    <!-- 插入员工 -->
    <insert id="insert" parameterType="com.example.cllcnplatformbackend.entity.company.Employee" 
            useGeneratedKeys="true" keyProperty="employeeId">
        INSERT INTO employee (
            name, email, phone, id_card, password, role, status,
            department_id, position_id, create_time, update_time
        ) VALUES (
            #{name}, #{email}, #{phone}, #{idCard}, #{password}, #{role}, #{status},
            #{departmentId}, #{positionId}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新员工 -->
    <update id="update" parameterType="com.example.cllcnplatformbackend.entity.company.Employee">
        UPDATE employee
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="password != null">password = #{password},</if>
            <if test="role != null">role = #{role},</if>
            <if test="status != null">status = #{status},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="positionId != null">position_id = #{positionId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE employee_id = #{employeeId}
    </update>

    <!-- 更新员工密码 -->
    <update id="updatePassword">
        UPDATE employee
        SET password = #{password}, update_time = NOW()
        WHERE employee_id = #{employeeId}
    </update>

    <!-- 删除员工 -->
    <delete id="deleteById">
        DELETE FROM employee WHERE employee_id = #{employeeId}
    </delete>

</mapper>
