package com.example.cllcnplatformbackend.entity.logistics;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户映射实体类（物流平台专用）
 * 可选：用于缓存公司管理系统的用户信息，提高查询性能
 * 在混合架构中，这个表是可选的，主要用于性能优化
 *
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserMapping {

    private Long id;
    
    /**
     * 原始用户ID（来自公司管理系统）
     */
    private Long originalUserId;

    /**
     * 用户类型：employee（员工）或 client（客户）
     */
    private UserType userType;

    /**
     * 平台用户ID（格式：E123 或 C456）
     */
    private String platformUserId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 姓名
     */
    private String name;
    
    /**
     * 状态
     */
    private UserStatus status = UserStatus.ACTIVE;

    /**
     * 角色
     */
    private UserRole role = UserRole.USER;

    /**
     * 部门ID（仅员工有效）
     */
    private Long departmentId;

    /**
     * 职位ID（仅员工有效）
     */
    private Long positionId;
    
    /**
     * 公司ID（仅客户有效）
     */
    private Long companyId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 用户类型枚举
     */
    public enum UserType {
        EMPLOYEE,  // 员工
        CLIENT     // 客户
    }
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE,    // 激活
        INACTIVE,  // 未激活
        LOCKED,    // 锁定
        DELETED    // 已删除
    }
    
    /**
     * 用户角色枚举
     */
    public enum UserRole {
        ADMIN,     // 管理员
        USER,      // 普通用户
        MANAGER,   // 经理
        OPERATOR   // 操作员
    }
    
    /**
     * 判断是否为员工
     */
    public boolean isEmployee() {
        return UserType.EMPLOYEE.equals(this.userType);
    }
    
    /**
     * 判断是否为客户
     */
    public boolean isClient() {
        return UserType.CLIENT.equals(this.userType);
    }
    
    /**
     * 判断是否为激活状态
     */
    public boolean isActive() {
        return UserStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return UserRole.ADMIN.equals(this.role);
    }
}
