# CLLCN国际物流平台 - 数据模型设计（集成版）

## 1. 架构集成说明

### 1.1 集成策略
本文档基于**混合架构**设计，采用以下集成策略：
- **用户认证共享**：直接使用公司管理系统的 `employee` 和 `client` 表
- **业务数据独立**：物流业务数据使用独立数据库 `logistics_platform`
- **跨库关联**：通过数据库视图和用户映射表实现数据关联

### 1.2 数据库架构
```
company_management_system (公司管理数据库)
├── employee (员工表) - 认证共享
├── client (客户表) - 认证共享
├── department (部门表) - 引用关联
└── 其他管理业务表...

logistics_platform (物流平台数据库)
├── user_mapping (用户映射表) - 缓存用户信息
├── country (国家表)
├── port (港口表)
├── route (航线表)
├── order_info (订单表) - 关联用户映射
└── 其他物流业务表...
```

## 2. 数据库设计原则

### 2.1 设计原则
- **集成优先**：优先复用现有用户体系，避免重复建设
- **适度解耦**：业务数据独立，用户数据共享
- **性能优化**：合理使用索引，优化跨库查询性能
- **数据一致性**：通过视图和同步机制保证数据一致性
- **安全性**：敏感数据加密存储，权限严格控制

### 2.2 命名规范
- **表名**：使用下划线分隔的小写字母，如 `user_mapping`
- **字段名**：使用下划线分隔的小写字母，如 `create_time`
- **索引名**：`idx_表名_字段名`，如 `idx_user_mapping_email`
- **外键名**：`fk_表名_字段名`，如 `fk_order_user_id`

## 3. 用户体系集成设计

### 3.1 现有用户表分析

#### 3.1.1 员工表 (company_management_system.employee)
**现有结构**：已完善，支持直接登录
```sql
-- 现有employee表结构（无需修改）
employee_id INT PRIMARY KEY AUTO_INCREMENT
name VARCHAR(50) NOT NULL                    -- 姓名
phone VARCHAR(20)                           -- 手机号
email VARCHAR(100)                          -- 邮箱（登录账号）
password VARCHAR(100) NOT NULL              -- 密码（MD5加密）
role VARCHAR(20) NOT NULL DEFAULT 'employee' -- 角色（admin/manager/employee）
status VARCHAR(10) NOT NULL DEFAULT 'Active' -- 状态
-- 其他字段...
```

#### 3.1.2 客户表调整 (company_management_system.client)
**需要调整**：添加密码字段支持登录
```sql
-- 为client表添加password字段
ALTER TABLE company_management_system.client
ADD COLUMN password VARCHAR(255) COMMENT '登录密码(加密)' AFTER email;

-- 添加登录相关索引
ALTER TABLE company_management_system.client
ADD INDEX idx_client_email (email);

-- 现有client表结构
client_id INT PRIMARY KEY AUTO_INCREMENT
name VARCHAR(50) NOT NULL                    -- 客户名称
contact_person VARCHAR(50) DEFAULT ''       -- 联系人
employee_id INT                             -- 负责员工ID
operator_id INT                             -- 操作员ID
category VARCHAR(20) NOT NULL DEFAULT '海运' -- 客户分类
status VARCHAR(20) NOT NULL DEFAULT '未审核' -- 审核状态
client_status VARCHAR(20) NOT NULL DEFAULT '报价中' -- 合作状态
email VARCHAR(100) NOT NULL                 -- 客户邮箱
password VARCHAR(255)                       -- 登录密码（新增）
phone VARCHAR(100) NOT NULL                 -- 客户电话
-- 其他字段...
```

### 3.2 用户映射表设计

#### 3.2.1 用户映射表 (logistics_platform.user_mapping)
**作用**：缓存和统一用户信息，提高查询性能
```sql
CREATE TABLE user_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '映射ID',
    platform_user_id VARCHAR(20) NOT NULL UNIQUE COMMENT '平台用户ID (E123/C456)',
    source_table ENUM('employee', 'client') NOT NULL COMMENT '来源表',
    source_id INT NOT NULL COMMENT '来源表ID',
    user_type ENUM('admin', 'manager', 'employee', 'client') NOT NULL COMMENT '用户类型',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    mobile VARCHAR(20) COMMENT '手机号',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
    department_id INT COMMENT '部门ID（仅员工）',
    employee_id INT COMMENT '负责员工ID（仅客户）',
    last_sync_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_source (source_table, source_id),
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_platform_user_id (platform_user_id),
    INDEX idx_status (status)
) COMMENT '用户映射表';
```

### 3.3 用户视图设计

#### 3.3.1 统一用户视图 (logistics_platform.v_platform_users)
```sql
CREATE VIEW v_platform_users AS
SELECT
    CONCAT('E', employee_id) as platform_user_id,
    'employee' as source_table,
    employee_id as source_id,
    CASE
        WHEN role = 'admin' THEN 'admin'
        WHEN role = 'manager' THEN 'manager'
        ELSE 'employee'
    END as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE WHEN status = 'Active' THEN 'active' ELSE 'inactive' END as status,
    department_id,
    NULL as employee_id,
    update_time as last_sync_time
FROM company_management_system.employee
WHERE status = 'Active'

UNION ALL

SELECT
    CONCAT('C', client_id) as platform_user_id,
    'client' as source_table,
    client_id as source_id,
    'client' as user_type,
    name as real_name,
    email,
    phone as mobile,
    CASE WHEN status = '审核通过' THEN 'active' ELSE 'inactive' END as status,
    NULL as department_id,
    employee_id,
    update_time as last_sync_time
FROM company_management_system.client
WHERE status IN ('审核通过', '已合作') AND password IS NOT NULL;
```

## 4. 物流业务表设计（优化版）

### 4.1 基础数据表

#### 4.1.1 轮播图表 (banner)
```sql
CREATE TABLE banner (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '轮播图ID',
    title VARCHAR(200) COMMENT '标题',
    image_url VARCHAR(500) NOT NULL COMMENT '图片URL',
    link_url VARCHAR(500) COMMENT '链接URL',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_banner_status_sort (status, sort_order)
) COMMENT '轮播图表';
```

#### 4.1.2 国家表 (country)
```sql
CREATE TABLE country (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '国家ID',
    country_code VARCHAR(10) NOT NULL UNIQUE COMMENT '国家代码',
    country_name_en VARCHAR(100) NOT NULL COMMENT '国家英文名',
    country_name_cn VARCHAR(100) NOT NULL COMMENT '国家中文名',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_country_code (country_code)
) COMMENT '国家表';
```

#### 4.1.3 港口表 (port)
```sql
CREATE TABLE port (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '港口ID',
    port_code VARCHAR(10) NOT NULL UNIQUE COMMENT '港口代码',
    port_name_en VARCHAR(100) NOT NULL COMMENT '港口英文名',
    port_name_cn VARCHAR(100) NOT NULL COMMENT '港口中文名',
    country_id BIGINT NOT NULL COMMENT '所属国家ID',
    is_hot TINYINT NOT NULL DEFAULT 0 COMMENT '是否热门港口:0-否,1-是',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY fk_port_country_id (country_id) REFERENCES country(id),
    INDEX idx_port_country_id (country_id),
    INDEX idx_port_hot (is_hot),
    INDEX idx_port_name_en (port_name_en),
    INDEX idx_port_name_cn (port_name_cn)
) COMMENT '港口表';
```

#### 4.1.4 船司表 (shipping_company)
```sql
CREATE TABLE shipping_company (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '船司ID',
    company_code VARCHAR(20) NOT NULL UNIQUE COMMENT '船司代码',
    company_name_en VARCHAR(200) NOT NULL COMMENT '船司英文名',
    company_name_cn VARCHAR(200) NOT NULL COMMENT '船司中文名',
    logo_url VARCHAR(500) COMMENT 'Logo图片URL',
    contact_info TEXT COMMENT '联系信息',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_shipping_company_code (company_code)
) COMMENT '船司表';
```

### 4.2 航线运价表

#### 4.2.1 航线表 (route)
```sql
CREATE TABLE route (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '航线ID',
    route_code VARCHAR(50) NOT NULL COMMENT '航线代码',
    shipping_company_id BIGINT NOT NULL COMMENT '船司ID',
    origin_port_id BIGINT NOT NULL COMMENT '起运港ID',
    destination_port_id BIGINT NOT NULL COMMENT '目的港ID',
    transit_port_id BIGINT COMMENT '中转港ID',
    voyage_days INT NOT NULL COMMENT '航程天数',
    is_direct TINYINT NOT NULL DEFAULT 1 COMMENT '是否直达:0-中转,1-直达',
    is_special TINYINT NOT NULL DEFAULT 0 COMMENT '是否特惠航线:0-否,1-是',
    cut_off_time TIME COMMENT '截关时间',
    etd_time DATETIME COMMENT '预计开船时间',
    atd_time DATETIME COMMENT '实际开船时间',
    valid_from DATE NOT NULL COMMENT '有效期开始',
    valid_to DATE NOT NULL COMMENT '有效期结束',
    route_remark TEXT COMMENT '航线特别说明',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY fk_route_shipping_company_id (shipping_company_id) REFERENCES shipping_company(id),
    FOREIGN KEY fk_route_origin_port_id (origin_port_id) REFERENCES port(id),
    FOREIGN KEY fk_route_destination_port_id (destination_port_id) REFERENCES port(id),
    FOREIGN KEY fk_route_transit_port_id (transit_port_id) REFERENCES port(id),
    INDEX idx_route_ports (origin_port_id, destination_port_id),
    INDEX idx_route_company (shipping_company_id),
    INDEX idx_route_valid (valid_from, valid_to)
) COMMENT '航线表';
```

#### 4.2.2 集装箱类型表 (container_type)
```sql
CREATE TABLE container_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '箱型ID',
    type_code VARCHAR(10) NOT NULL UNIQUE COMMENT '箱型代码',
    type_name VARCHAR(50) NOT NULL COMMENT '箱型名称',
    description VARCHAR(200) COMMENT '描述',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '集装箱类型表';

-- 初始化集装箱类型数据
INSERT INTO container_type (type_code, type_name, description) VALUES
('20GP', '20英尺普通柜', '20英尺标准集装箱'),
('40GP', '40英尺普通柜', '40英尺标准集装箱'),
('40HQ', '40英尺高柜', '40英尺高箱集装箱'),
('40NOR', '40英尺开顶柜', '40英尺开顶集装箱'),
('45HQ', '45英尺高柜', '45英尺高箱集装箱');
```

#### 4.2.3 费用类型表 (fee_type)
```sql
CREATE TABLE fee_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '费用类型ID',
    fee_code VARCHAR(50) NOT NULL UNIQUE COMMENT '费用代码',
    fee_name VARCHAR(100) NOT NULL COMMENT '费用名称',
    fee_category TINYINT NOT NULL COMMENT '费用类别:1-海运费,2-附加费,3-港口费,4-其他费用',
    charge_unit TINYINT NOT NULL COMMENT '计费单位:1-按箱型,2-按箱,3-按票',
    payment_method TINYINT NOT NULL DEFAULT 1 COMMENT '付款方式:1-预付,2-到付',
    description VARCHAR(500) COMMENT '费用描述',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_fee_type_category (fee_category)
) COMMENT '费用类型表';

-- 初始化费用类型数据
INSERT INTO fee_type (fee_code, fee_name, fee_category, charge_unit, payment_method, description) VALUES
('OCEAN_FREIGHT', '海运费', 1, 1, 1, '基础海运运输费用'),
('DG_SURCHARGE', '海运危险品附加费', 2, 1, 1, '危险品货物附加费用'),
('DOC_FEE', '起运港文件费', 3, 3, 1, '起运港单据处理费'),
('SECURITY_FEE', '安保费', 2, 2, 1, '安全检查费用'),
('THC_ORIGIN', '起运港码头费', 3, 1, 1, '起运港码头操作费'),
('EIR_FEE', '设备交接单费', 4, 2, 1, '设备交接单处理费'),
('SEAL_FEE', '封条费', 4, 2, 1, '集装箱封条费用');
```

<!-- 企业认证表 -->
```sql
CREATE TABLE company_auth (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '认证ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    company_name VARCHAR(200) NOT NULL COMMENT '公司名称',
    auth_status TINYINT NOT NULL DEFAULT 0 COMMENT '认证状态:0-待认证,1-认证中,2-已认证,3-认证失败',
    industry VARCHAR(100) COMMENT '所属行业',
    business_license VARCHAR(500) COMMENT '营业执照图片URL',
    company_address VARCHAR(500) COMMENT '公司地址',
    social_credit_code VARCHAR(50) COMMENT '社会信用代码',
    company_scale TINYINT COMMENT '公司规模:1-1-50人,2-51-200人,3-201-500人,4-500人以上',
    business_card VARCHAR(500) COMMENT '名片图片URL',
    auth_remark TEXT COMMENT '认证备注',
    auth_time DATETIME COMMENT '认证通过时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY fk_company_auth_user_id (user_id) REFERENCES user_info(id),
    INDEX idx_company_auth_user_id (user_id),
    INDEX idx_company_auth_status (auth_status)
) COMMENT '企业认证表';
```

### 2.2 基础数据相关表

#### 2.2.1 轮播图表 (banner)
```sql
CREATE TABLE banner (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '轮播图ID',
    title VARCHAR(200) COMMENT '标题',
    image_url VARCHAR(500) NOT NULL COMMENT '图片URL',
    link_url VARCHAR(500) COMMENT '链接URL',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_banner_status_sort (status, sort_order)
) COMMENT '轮播图表';
```

#### 2.2.2 国家表 (country)
```sql
CREATE TABLE country (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '国家ID',
    country_code VARCHAR(10) NOT NULL UNIQUE COMMENT '国家代码',
    country_name_en VARCHAR(100) NOT NULL COMMENT '国家英文名',
    country_name_cn VARCHAR(100) NOT NULL COMMENT '国家中文名',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_country_code (country_code)
) COMMENT '国家表';
```

#### 2.2.3 港口表 (port)
```sql
CREATE TABLE port (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '港口ID',
    port_code VARCHAR(10) NOT NULL UNIQUE COMMENT '港口代码',
    port_name_en VARCHAR(100) NOT NULL COMMENT '港口英文名',
    port_name_cn VARCHAR(100) NOT NULL COMMENT '港口中文名',
    country_id BIGINT NOT NULL COMMENT '所属国家ID',
    is_hot TINYINT NOT NULL DEFAULT 0 COMMENT '是否热门港口:0-否,1-是',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY fk_port_country_id (country_id) REFERENCES country(id),
    INDEX idx_port_country_id (country_id),
    INDEX idx_port_hot (is_hot),
    INDEX idx_port_name_en (port_name_en),
    INDEX idx_port_name_cn (port_name_cn)
) COMMENT '港口表';
```

#### 2.2.4 船司表 (shipping_company)
```sql
CREATE TABLE shipping_company (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '船司ID',
    company_code VARCHAR(20) NOT NULL UNIQUE COMMENT '船司代码',
    company_name_en VARCHAR(200) NOT NULL COMMENT '船司英文名',
    company_name_cn VARCHAR(200) NOT NULL COMMENT '船司中文名',
    logo_url VARCHAR(500) COMMENT 'Logo图片URL',
    contact_info TEXT COMMENT '联系信息',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_shipping_company_code (company_code)
) COMMENT '船司表';
```

### 2.3 航线运价相关表

#### 2.3.1 航线表 (route)
```sql
CREATE TABLE route (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '航线ID',
    route_code VARCHAR(50) NOT NULL COMMENT '航线代码',
    shipping_company_id BIGINT NOT NULL COMMENT '船司ID',
    origin_port_id BIGINT NOT NULL COMMENT '起运港ID',
    destination_port_id BIGINT NOT NULL COMMENT '目的港ID',
    transit_port_id BIGINT COMMENT '中转港ID',
    voyage_days INT NOT NULL COMMENT '航程天数',
    is_direct TINYINT NOT NULL DEFAULT 1 COMMENT '是否直达:0-中转,1-直达',
    is_special TINYINT NOT NULL DEFAULT 0 COMMENT '是否特惠航线:0-否,1-是',
    cut_off_time TIME COMMENT '截关时间',
    etd_time DATETIME COMMENT '预计开船时间',
    atd_time DATETIME COMMENT '实际开船时间',
    valid_from DATE NOT NULL COMMENT '有效期开始',
    valid_to DATE NOT NULL COMMENT '有效期结束',
    route_remark TEXT COMMENT '航线特别说明',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY fk_route_shipping_company_id (shipping_company_id) REFERENCES shipping_company(id),
    FOREIGN KEY fk_route_origin_port_id (origin_port_id) REFERENCES port(id),
    FOREIGN KEY fk_route_destination_port_id (destination_port_id) REFERENCES port(id),
    FOREIGN KEY fk_route_transit_port_id (transit_port_id) REFERENCES port(id),
    INDEX idx_route_ports (origin_port_id, destination_port_id),
    INDEX idx_route_company (shipping_company_id),
    INDEX idx_route_valid (valid_from, valid_to)
) COMMENT '航线表';
```

#### 2.3.2 集装箱类型表 (container_type)
```sql
CREATE TABLE container_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '箱型ID',
    type_code VARCHAR(10) NOT NULL UNIQUE COMMENT '箱型代码',
    type_name VARCHAR(50) NOT NULL COMMENT '箱型名称',
    description VARCHAR(200) COMMENT '描述',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '集装箱类型表';

-- 初始化集装箱类型数据
INSERT INTO container_type (type_code, type_name, description) VALUES
('20GP', '20英尺普通柜', '20英尺标准集装箱'),
('40GP', '40英尺普通柜', '40英尺标准集装箱'),
('40HQ', '40英尺高柜', '40英尺高箱集装箱'),
('40NOR', '40英尺开顶柜', '40英尺开顶集装箱'),
('45HQ', '45英尺高柜', '45英尺高箱集装箱');
```

#### 2.3.3 费用类型表 (fee_type)
```sql
CREATE TABLE fee_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '费用类型ID',
    fee_code VARCHAR(50) NOT NULL UNIQUE COMMENT '费用代码',
    fee_name VARCHAR(100) NOT NULL COMMENT '费用名称',
    fee_category TINYINT NOT NULL COMMENT '费用类别:1-海运费,2-附加费,3-港口费,4-其他费用',
    charge_unit TINYINT NOT NULL COMMENT '计费单位:1-按箱型,2-按箱,3-按票',
    payment_method TINYINT NOT NULL DEFAULT 1 COMMENT '付款方式:1-预付,2-到付',
    description VARCHAR(500) COMMENT '费用描述',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_fee_type_category (fee_category)
) COMMENT '费用类型表';

-- 初始化费用类型数据
INSERT INTO fee_type (fee_code, fee_name, fee_category, charge_unit, payment_method, description) VALUES
('OCEAN_FREIGHT', '海运费', 1, 1, 1, '基础海运运输费用'),
('DG_SURCHARGE', '海运危险品附加费', 2, 1, 1, '危险品货物附加费用'),
('DOC_FEE', '起运港文件费', 3, 3, 1, '起运港单据处理费'),
('SECURITY_FEE', '安保费', 2, 2, 1, '安全检查费用'),
('THC_ORIGIN', '起运港码头费', 3, 1, 1, '起运港码头操作费'),
('EIR_FEE', '设备交接单费', 4, 2, 1, '设备交接单处理费'),
('SEAL_FEE', '封条费', 4, 2, 1, '集装箱封条费用');
```

#### 2.3.4 运价表 (pricing)
```sql
CREATE TABLE pricing (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '运价ID',
    route_id BIGINT NOT NULL COMMENT '航线ID',
    fee_type_id BIGINT NOT NULL COMMENT '费用类型ID',
    container_type_id BIGINT COMMENT '集装箱类型ID(按箱型计费时必填)',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    currency VARCHAR(10) NOT NULL DEFAULT 'USD' COMMENT '币种',
    remark VARCHAR(500) COMMENT '备注',
    valid_from DATE NOT NULL COMMENT '有效期开始',
    valid_to DATE NOT NULL COMMENT '有效期结束',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY fk_pricing_route_id (route_id) REFERENCES route(id),
    FOREIGN KEY fk_pricing_fee_type_id (fee_type_id) REFERENCES fee_type(id),
    FOREIGN KEY fk_pricing_container_type_id (container_type_id) REFERENCES container_type(id),
    INDEX idx_pricing_route (route_id),
    INDEX idx_pricing_valid (valid_from, valid_to),
    UNIQUE KEY uk_pricing_route_fee_container (route_id, fee_type_id, container_type_id, valid_from)
) COMMENT '运价表';
```

#### 2.3.5 运价历史表 (pricing_history)
```sql
CREATE TABLE pricing_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史ID',
    route_id BIGINT NOT NULL COMMENT '航线ID',
    fee_type_id BIGINT NOT NULL COMMENT '费用类型ID',
    container_type_id BIGINT COMMENT '集装箱类型ID',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    currency VARCHAR(10) NOT NULL COMMENT '币种',
    record_date DATE NOT NULL COMMENT '记录日期',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_pricing_history_route_date (route_id, record_date),
    INDEX idx_pricing_history_date (record_date)
) COMMENT '运价历史表';
```

### 2.4 订单相关表

#### 2.4.1 订单表 (order_info)
```sql
CREATE TABLE order_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    route_id BIGINT NOT NULL COMMENT '航线ID',
    order_status TINYINT NOT NULL DEFAULT 1 COMMENT '订单状态:1-待确认,2-已确认,3-进行中,4-已完成,5-已取消',
    logistics_status TINYINT NOT NULL DEFAULT 1 COMMENT '物流状态:1-订舱,2-装箱,3-开船,4-在途,5-到港,6-清关,7-提货,8-完成',
    total_amount DECIMAL(12,2) COMMENT '订单总金额',
    currency VARCHAR(10) DEFAULT 'USD' COMMENT '币种',
    customer_ref_no VARCHAR(100) COMMENT '客户委托号',
    so_no VARCHAR(100) COMMENT 'SO号',
    hbl_no VARCHAR(100) COMMENT 'HBL号',
    mbl_no VARCHAR(100) COMMENT 'MBL号',
    vessel_name VARCHAR(100) COMMENT '船名',
    voyage VARCHAR(50) COMMENT '航次',
    order_source TINYINT NOT NULL DEFAULT 1 COMMENT '订单来源:1-网站,2-API,3-客服',
    booking_remark TEXT COMMENT '订舱委托备注',
    door_to_door_ref VARCHAR(100) COMMENT '门到门报价方案单号',
    contact_name VARCHAR(50) COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    contact_email VARCHAR(100) COMMENT '联系人邮箱',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY fk_order_user_id (user_id) REFERENCES user_info(id),
    FOREIGN KEY fk_order_route_id (route_id) REFERENCES route(id),
    INDEX idx_order_user_id (user_id),
    INDEX idx_order_status (order_status),
    INDEX idx_order_create_time (create_time)
) COMMENT '订单表';
```

#### 2.4.2 订单集装箱表 (order_container)
```sql
CREATE TABLE order_container (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单集装箱ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    container_type_id BIGINT NOT NULL COMMENT '集装箱类型ID',
    quantity INT NOT NULL COMMENT '箱量',
    weight DECIMAL(8,2) COMMENT '单柜重量(吨)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY fk_order_container_order_id (order_id) REFERENCES order_info(id),
    FOREIGN KEY fk_order_container_type_id (container_type_id) REFERENCES container_type(id),
    INDEX idx_order_container_order_id (order_id)
) COMMENT '订单集装箱表';
```

#### 2.4.3 订单货物表 (order_cargo)
```sql
CREATE TABLE order_cargo (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单货物ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_container_id BIGINT NOT NULL COMMENT '订单集装箱ID',
    hs_code VARCHAR(20) COMMENT 'HS编码',
    cargo_name_cn VARCHAR(200) NOT NULL COMMENT '中文品名',
    cargo_name_en VARCHAR(200) NOT NULL COMMENT '英文品名',
    cargo_nature TINYINT NOT NULL DEFAULT 1 COMMENT '货物性质:1-普通货物,2-危险品,3-冷藏货物',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY fk_order_cargo_order_id (order_id) REFERENCES order_info(id),
    FOREIGN KEY fk_order_cargo_container_id (order_container_id) REFERENCES order_container(id),
    INDEX idx_order_cargo_order_id (order_id),
    INDEX idx_order_cargo_container_id (order_container_id)
) COMMENT '订单货物表';
```

#### 2.4.4 订单附件表 (order_attachment)
```sql
CREATE TABLE order_attachment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '附件ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_url VARCHAR(500) NOT NULL COMMENT '文件URL',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY fk_order_attachment_order_id (order_id) REFERENCES order_info(id),
    INDEX idx_order_attachment_order_id (order_id)
) COMMENT '订单附件表';
```

### 2.5 个人中心相关表

#### 2.5.1 联系人表 (contact)
```sql
CREATE TABLE contact (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '联系人ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    contact_email VARCHAR(100) NOT NULL COMMENT '联系人邮箱',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    company_name VARCHAR(200) COMMENT '公司名称',
    remark VARCHAR(500) COMMENT '备注信息',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY fk_contact_user_id (user_id) REFERENCES user_info(id),
    INDEX idx_contact_user_id (user_id)
) COMMENT '联系人表';
```

#### 2.5.2 卡券表 (coupon)
```sql
CREATE TABLE coupon (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '卡券ID',
    coupon_name VARCHAR(100) NOT NULL COMMENT '卡券名称',
    coupon_type TINYINT NOT NULL COMMENT '卡券类型:1-运费优惠券,2-新用户优惠券,3-节日活动券,4-积分兑换券',
    discount_type TINYINT NOT NULL COMMENT '折扣类型:1-固定金额,2-百分比折扣',
    discount_value DECIMAL(10,2) NOT NULL COMMENT '折扣值',
    min_amount DECIMAL(10,2) COMMENT '最低使用金额',
    valid_from DATETIME NOT NULL COMMENT '有效期开始',
    valid_to DATETIME NOT NULL COMMENT '有效期结束',
    total_quantity INT NOT NULL COMMENT '发放总量',
    used_quantity INT NOT NULL DEFAULT 0 COMMENT '已使用数量',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_coupon_valid (valid_from, valid_to),
    INDEX idx_coupon_type (coupon_type)
) COMMENT '卡券表';
```

#### 2.5.3 用户卡券表 (user_coupon)
```sql
CREATE TABLE user_coupon (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户卡券ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    coupon_id BIGINT NOT NULL COMMENT '卡券ID',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-未使用,2-已使用,3-已过期',
    used_time DATETIME COMMENT '使用时间',
    order_id BIGINT COMMENT '使用订单ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY fk_user_coupon_user_id (user_id) REFERENCES user_info(id),
    FOREIGN KEY fk_user_coupon_coupon_id (coupon_id) REFERENCES coupon(id),
    FOREIGN KEY fk_user_coupon_order_id (order_id) REFERENCES order_info(id),
    INDEX idx_user_coupon_user_id (user_id),
    INDEX idx_user_coupon_status (status)
) COMMENT '用户卡券表';
```

#### 2.5.4 消息通知表 (message)
```sql
CREATE TABLE message (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    message_title VARCHAR(200) NOT NULL COMMENT '消息标题',
    message_content TEXT NOT NULL COMMENT '消息内容',
    message_type TINYINT NOT NULL COMMENT '消息类型:1-系统通知,2-订单通知,3-营销通知,4-认证通知',
    priority TINYINT NOT NULL DEFAULT 1 COMMENT '重要程度:1-普通,2-重要,3-紧急',
    read_status TINYINT NOT NULL DEFAULT 0 COMMENT '阅读状态:0-未读,1-已读',
    read_time DATETIME COMMENT '阅读时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY fk_message_user_id (user_id) REFERENCES user_info(id),
    INDEX idx_message_user_id (user_id),
    INDEX idx_message_read_status (read_status),
    INDEX idx_message_type (message_type)
) COMMENT '消息通知表';
```

### 2.6 系统相关表

#### 2.6.1 搜索历史表 (search_history)
```sql
CREATE TABLE search_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '搜索历史ID',
    user_id BIGINT COMMENT '用户ID(可为空,支持游客)',
    search_type TINYINT NOT NULL COMMENT '搜索类型:1-港口搜索,2-航线搜索',
    search_keyword VARCHAR(200) NOT NULL COMMENT '搜索关键词',
    search_result_count INT COMMENT '搜索结果数量',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_search_history_user_id (user_id),
    INDEX idx_search_history_type (search_type),
    INDEX idx_search_history_time (create_time)
) COMMENT '搜索历史表';
```

#### 2.6.2 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_desc VARCHAR(500) COMMENT '配置描述',
    config_type VARCHAR(50) NOT NULL COMMENT '配置类型',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_system_config_key (config_key)
) COMMENT '系统配置表';
```

## 3. 数据关系图

### 3.1 核心实体关系
```mermaid
erDiagram
    user_info ||--o| company_auth : "1:1"
    user_info ||--o{ order_info : "1:N"
    user_info ||--o{ contact : "1:N"
    user_info ||--o{ user_coupon : "1:N"
    user_info ||--o{ message : "1:N"

    country ||--o{ port : "1:N"
    shipping_company ||--o{ route : "1:N"
    route ||--o{ pricing : "1:N"
    route ||--o{ order_info : "1:N"

    container_type ||--o{ pricing : "1:N"
    container_type ||--o{ order_container : "1:N"
    fee_type ||--o{ pricing : "1:N"

    order_info ||--o{ order_container : "1:N"
    order_info ||--o{ order_cargo : "1:N"
    order_info ||--o{ order_attachment : "1:N"
    order_container ||--o{ order_cargo : "1:N"

    coupon ||--o{ user_coupon : "1:N"
```

## 4. 索引优化策略

### 4.1 查询优化索引
- **用户查询**：手机号、邮箱唯一索引
- **港口搜索**：港口名称、国家ID复合索引
- **航线查询**：起运港+目的港复合索引
- **运价查询**：航线ID+有效期复合索引
- **订单查询**：用户ID+创建时间复合索引

### 4.2 性能优化建议
- 对大表进行分区（按时间分区）
- 定期清理历史数据
- 使用读写分离
- 合理使用缓存策略
```
```
