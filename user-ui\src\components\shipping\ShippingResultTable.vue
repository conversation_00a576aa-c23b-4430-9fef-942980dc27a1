<!--
  运价结果表格组件
  
  @description 以表格形式展示运价查询结果列表
  <AUTHOR>
  @date 2025-07-21 15:00:00 +08:00
-->

<template>
  <div class="bg-white rounded-lg border">
    <!-- 表格头部 -->
    <div class="grid grid-cols-12 gap-3 px-4 py-2 bg-gray-50 border-b text-xs font-medium text-gray-700">
      <div class="col-span-3">船司/航线</div>
      <div class="col-span-2">起运港 → 目的港</div>
      <div class="col-span-1 text-center">时效</div>
      <div class="col-span-4 text-center">价格 (USD)</div>
      <div class="col-span-2 text-center">操作</div>
    </div>

    <!-- 表格内容 -->
    <div class="divide-y divide-gray-100">
      <div
        v-for="result in results"
        :key="result.id"
        class="grid grid-cols-12 gap-3 px-4 py-3 hover:bg-gray-50 transition-colors"
      >
        <!-- 船司/航线信息 -->
        <div class="col-span-3">
          <div class="flex items-center space-x-2">
            <!-- 船司Logo -->
            <div class="w-8 h-8 bg-gradient-to-br from-blue-50 to-blue-100 rounded flex items-center justify-center border flex-shrink-0">
              <span class="text-xs font-bold text-blue-600">
                {{ result.carrier.substring(0, 2) }}
              </span>
            </div>

            <!-- 船司信息 -->
            <div class="min-w-0 flex-1">
              <div class="font-medium text-gray-900 text-xs truncate">{{ result.carrier }}</div>
              <div class="flex items-center gap-1 mt-0.5">
                <!-- 特性标签 -->
                <Badge
                  v-for="feature in result.features"
                  :key="feature"
                  :variant="getBadgeVariant(feature)"
                  class="text-xs px-1 py-0"
                >
                  {{ feature }}
                </Badge>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 起运港 → 目的港 -->
        <div class="col-span-2">
          <div class="flex items-center justify-between text-xs">
            <!-- 起运港 -->
            <span class="font-medium text-gray-900 flex-shrink-0">{{ result.origin }}</span>

            <!-- 运输类型指示器 -->
            <div class="flex-1 mx-2 flex flex-col items-center">
              <div class="flex items-center w-full">
                <div class="flex-1 h-px bg-gray-300"></div>
                <span class="px-2 text-xs font-medium text-blue-600 bg-blue-50 rounded-full whitespace-nowrap">
                  {{ result.route.isDirect ? '直达' : '中转' }}
                </span>
                <div class="flex-1 h-px bg-gray-300"></div>
              </div>

              <!-- 中转港口信息 -->
              <div v-if="!result.route.isDirect && result.route.transitPorts.length > 0"
                   class="text-xs text-gray-500 mt-1 text-center">
                <span class="text-gray-400">经</span>
                <span class="font-medium">{{ result.route.transitPorts.join(', ') }}</span>
              </div>
            </div>

            <!-- 目的港 -->
            <span class="font-medium text-gray-900 flex-shrink-0">{{ result.destination }}</span>
          </div>
        </div>
        
        <!-- 时效 -->
        <div class="col-span-1 text-center">
          <div class="flex items-center justify-center text-xs">
            <Clock class="w-3 h-3 mr-1 text-gray-400" />
            <span class="font-medium">{{ result.route.transitDays }}</span>
            <span class="text-gray-500 ml-0.5">天</span>
          </div>
        </div>

        <!-- 价格 -->
        <div class="col-span-4">
          <div class="grid grid-cols-4 gap-1">
            <div
              v-for="(price, containerType) in result.prices"
              :key="containerType"
              class="text-center"
            >
              <div class="text-xs text-gray-500">{{ containerType }}</div>
              <div class="text-xs font-bold text-orange-600">***</div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="col-span-2">
          <div class="flex items-center justify-center gap-2">
            <!-- 前四个操作按钮 - 2x2网格布局 -->
            <div class="grid grid-cols-2 gap-1">
              <!-- 第一行 -->
              <Button variant="outline" size="sm" class="text-xs h-7 px-2 flex items-center gap-1">
                <DollarSign class="w-3 h-3" />
                <span>报价</span>
              </Button>
              <Button variant="outline" size="sm" class="text-xs h-7 px-2 flex items-center gap-1">
                <Info class="w-3 h-3" />
                <span>详情</span>
              </Button>
              <!-- 第二行 -->
              <Button variant="outline" size="sm" class="text-xs h-7 px-2 flex items-center gap-1">
                <Copy class="w-3 h-3" />
                <span>复制</span>
              </Button>
              <Button variant="outline" size="sm" class="text-xs h-7 px-2 flex items-center gap-1">
                <TrendingUp class="w-3 h-3" />
                <span>趋势</span>
              </Button>
            </div>

            <!-- 订舱按钮 - 高度与前四个按钮的2行总高度相等 -->
            <Button size="sm" class="h-[3.75rem] px-3 text-xs flex flex-col items-center justify-center gap-1" @click="handleBooking(result)">
              <Anchor class="w-4 h-4" />
              <span>订舱</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="results.length === 0" class="text-center py-8 text-gray-500">
      <Package class="w-8 h-8 mx-auto mb-2 text-gray-300" />
      <p class="text-sm">暂无运价数据</p>
    </div>
  </div>
</template>

<script setup>
import { 
  Clock, Navigation, DollarSign, Info, Copy, TrendingUp, 
  Anchor, Package
} from 'lucide-vue-next'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

// Props
const props = defineProps({
  results: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['booking'])

// 方法
const getBadgeVariant = (feature) => {
  const variants = {
    '特惠': 'destructive',
    '直达': 'default',
    '中转': 'secondary'
  }
  return variants[feature] || 'outline'
}

const handleBooking = (result) => {
  emit('booking', result)
}
</script>
