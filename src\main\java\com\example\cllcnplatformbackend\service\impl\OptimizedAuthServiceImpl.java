package com.example.cllcnplatformbackend.service.impl;

import com.example.cllcnplatformbackend.dto.LoginRequest;
import com.example.cllcnplatformbackend.dto.LoginResponse;
import com.example.cllcnplatformbackend.entity.PlatformUser;
import com.example.cllcnplatformbackend.mapper.PlatformUserMapper;
import com.example.cllcnplatformbackend.service.AuthService;
import com.example.cllcnplatformbackend.service.LoginAttemptService;
import com.example.cllcnplatformbackend.utils.JwtUtil;
import com.example.cllcnplatformbackend.utils.PasswordEncoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 优化版认证服务实现类
 * 使用统一用户视图 v_platform_users 进行用户认证
 * 
 * <AUTHOR>
 * @date 2025-08-02 12:15:00 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */
@Slf4j
@Service("optimizedAuthService")
@RequiredArgsConstructor
public class OptimizedAuthServiceImpl implements AuthService {

    private final PlatformUserMapper platformUserMapper;
    private final JwtUtil jwtUtil;
    private final LoginAttemptService loginAttemptService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        String account = loginRequest.getAccount().trim();
        String password = loginRequest.getPassword().trim();

        log.info("用户登录尝试 - 账号: {}", account);

        try {
            // 1. 检查账户是否被锁定（临时禁用）
            log.debug("跳过锁定检查，继续登录流程 - 账号: {}", account);

            // 2. 统一查询用户（先尝试邮箱，再尝试手机号）
            PlatformUser user = null;
            
            // 尝试通过邮箱查找
            log.debug("开始通过邮箱查找用户 - 邮箱: {}", account);
            user = platformUserMapper.findByEmailWithPassword(account);
            
            if (user == null) {
                // 尝试通过手机号查找
                log.debug("开始通过手机号查找用户 - 手机号: {}", account);
                user = platformUserMapper.findByMobileWithPassword(account);
            }

            // 3. 验证用户和密码
            if (user != null && user.getPassword() != null) {
                log.info("找到用户 - 平台用户ID: {}, 姓名: {}, 用户类型: {}, 状态: {}, 密码是否存在: {}", 
                    user.getPlatformUserId(), user.getRealName(), user.getUserType(), 
                    user.getStatus(), user.getPassword() != null ? "是" : "否");

                // 验证密码
                log.debug("开始验证用户密码 - 平台用户ID: {}", user.getPlatformUserId());
                if (verifyPassword(user, password)) {
                    log.info("用户密码验证成功 - 平台用户ID: {}", user.getPlatformUserId());
                    
                    // 登录成功，清除失败记录
                    // loginAttemptService.clearFailedAttempts(account);
                    
                    log.info("用户登录成功 - 平台用户ID: {}, 姓名: {}, 用户类型: {}", 
                        user.getPlatformUserId(), user.getRealName(), user.getUserType());
                    
                    return createLoginResponse(user);
                } else {
                    log.warn("用户密码验证失败 - 平台用户ID: {}", user.getPlatformUserId());
                }
            } else {
                log.debug("未找到用户或密码为空 - 账号: {}", account);
            }

            // 登录失败
            // loginAttemptService.recordFailedAttempt(account);
            log.warn("登录失败 - 账号: {}", account);
            throw new RuntimeException("账号或密码错误");

        } catch (Exception e) {
            if (e.getMessage().contains("账户已被锁定")) {
                throw e; // 重新抛出锁定异常
            }

            log.error("登录过程中发生系统错误 - 账号: {}", account, e);
            throw new RuntimeException("账号或密码错误");
        }
    }

    @Override
    public LoginResponse adminLogin(LoginRequest loginRequest) {
        String account = loginRequest.getAccount().trim();
        String password = loginRequest.getPassword().trim();

        log.info("管理后台登录尝试 - 账号: {}", account);

        try {
            // 1. 跳过锁定检查
            log.debug("管理后台跳过锁定检查，继续登录流程 - 账号: {}", account);

            // 2. 统一查询用户
            PlatformUser user = null;
            
            // 尝试通过邮箱查找
            user = platformUserMapper.findByEmailWithPassword(account);
            if (user == null) {
                // 尝试通过手机号查找
                user = platformUserMapper.findByMobileWithPassword(account);
            }

            // 3. 验证用户、密码和管理员权限
            if (user != null && user.getPassword() != null) {
                // 检查是否为管理员
                if (!"admin".equals(user.getUserType())) {
                    log.warn("非管理员尝试登录管理后台 - 平台用户ID: {}, 用户类型: {}", 
                        user.getPlatformUserId(), user.getUserType());
                    throw new RuntimeException("权限不足，只有管理员才能登录管理后台");
                }

                // 验证密码
                if (verifyPassword(user, password)) {
                    log.info("管理员登录成功 - 平台用户ID: {}, 姓名: {}", 
                        user.getPlatformUserId(), user.getRealName());
                    
                    return createLoginResponse(user);
                } else {
                    log.warn("管理员密码验证失败 - 平台用户ID: {}", user.getPlatformUserId());
                }
            }

            log.warn("管理后台登录失败 - 账号: {}", account);
            throw new RuntimeException("账号或密码错误");

        } catch (Exception e) {
            if (e.getMessage().contains("权限不足")) {
                throw e;
            }
            log.error("管理后台登录过程中发生系统错误 - 账号: {}", account, e);
            throw new RuntimeException("账号或密码错误");
        }
    }

    @Override
    public LoginResponse.UserInfo getCurrentUser(String platformUserId) {
        log.info("获取用户信息 - 平台用户ID: {}", platformUserId);
        
        PlatformUser user = platformUserMapper.findByPlatformUserId(platformUserId);
        if (user == null) {
            throw new RuntimeException("用户不存在或已被禁用");
        }
        
        return createUserInfo(user);
    }

    @Override
    public String refreshToken(String refreshToken) {
        // 实现令牌刷新逻辑
        // 这里可以复用原有的实现
        throw new RuntimeException("令牌刷新功能待实现");
    }

    @Override
    public void logout(String platformUserId) {
        log.info("用户登出 - 平台用户ID: {}", platformUserId);
        // 实现登出逻辑
    }

    /**
     * 验证密码
     */
    private boolean verifyPassword(PlatformUser user, String rawPassword) {
        if (user == null || user.getPassword() == null) {
            log.debug("用户密码验证失败 - 用户对象或密码为空");
            return false;
        }

        log.debug("开始密码匹配验证 - 平台用户ID: {}, 存储密码长度: {}, 输入密码长度: {}", 
            user.getPlatformUserId(), user.getPassword().length(), rawPassword.length());
        
        boolean matches = passwordEncoder.matches(rawPassword, user.getPassword());
        
        log.debug("密码匹配结果 - 平台用户ID: {}, 匹配结果: {}", user.getPlatformUserId(), matches);

        return matches;
    }

    /**
     * 创建登录响应
     */
    private LoginResponse createLoginResponse(PlatformUser user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("platformUserId", user.getPlatformUserId());
        claims.put("userType", user.getUserType());
        claims.put("realName", user.getRealName());
        claims.put("email", user.getEmail());
        claims.put("mobile", user.getMobile());

        String token = jwtUtil.generateToken(claims);
        Long expiresIn = JwtUtil.getExpirationTime();

        LoginResponse.UserInfo userInfo = createUserInfo(user);
        
        return LoginResponse.success(token, expiresIn, userInfo);
    }

    /**
     * 创建用户信息
     */
    private LoginResponse.UserInfo createUserInfo(PlatformUser user) {
        return new LoginResponse.UserInfo(
            user.getPlatformUserId(),
            user.getUserType(),
            user.getRealName(),
            user.getEmail(),
            user.getMobile(),
            user.getStatus()
        );
    }
}
