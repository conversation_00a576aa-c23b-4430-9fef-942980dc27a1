package com.example.cllcnplatformbackend.constants;

/**
 * 通用常量类
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
public class CommonConstants {
    
    /**
     * 响应状态码
     */
    public static class ResponseCode {
        public static final int SUCCESS = 200;
        public static final int BAD_REQUEST = 400;
        public static final int UNAUTHORIZED = 401;
        public static final int FORBIDDEN = 403;
        public static final int NOT_FOUND = 404;
        public static final int INTERNAL_ERROR = 500;
    }
    
    /**
     * 响应消息
     */
    public static class ResponseMessage {
        public static final String SUCCESS = "操作成功";
        public static final String FAILED = "操作失败";
        public static final String UNAUTHORIZED = "未授权访问";
        public static final String FORBIDDEN = "没有相关权限";
        public static final String NOT_FOUND = "资源不存在";
        public static final String INTERNAL_ERROR = "系统内部错误";
        public static final String VALIDATE_FAILED = "参数验证失败";
    }
    
    /**
     * 用户类型
     */
    public static class UserType {
        public static final String EMPLOYEE = "employee";  // 员工
        public static final String CLIENT = "client";      // 客户
    }
    
    /**
     * 用户角色
     */
    public static class UserRole {
        public static final String ADMIN = "ADMIN";        // 管理员
        public static final String USER = "USER";          // 普通用户
        public static final String MANAGER = "MANAGER";    // 经理
        public static final String OPERATOR = "OPERATOR";  // 操作员
    }
    
    /**
     * 用户状态
     */
    public static class UserStatus {
        public static final String ACTIVE = "ACTIVE";      // 激活
        public static final String INACTIVE = "INACTIVE";  // 未激活
        public static final String LOCKED = "LOCKED";      // 锁定
        public static final String DELETED = "DELETED";    // 已删除
    }
    
    /**
     * 订单状态
     */
    public static class OrderStatus {
        public static final String DRAFT = "DRAFT";                    // 草稿
        public static final String PENDING = "PENDING";                // 待处理
        public static final String CONFIRMED = "CONFIRMED";            // 已确认
        public static final String IN_TRANSIT = "IN_TRANSIT";          // 运输中
        public static final String DELIVERED = "DELIVERED";            // 已送达
        public static final String COMPLETED = "COMPLETED";            // 已完成
        public static final String CANCELLED = "CANCELLED";            // 已取消
    }
    
    /**
     * 支付状态
     */
    public static class PaymentStatus {
        public static final String UNPAID = "UNPAID";      // 未支付
        public static final String PAID = "PAID";          // 已支付
        public static final String REFUNDED = "REFUNDED";  // 已退款
        public static final String CANCELLED = "CANCELLED"; // 已取消
    }
    
    /**
     * 货物类型
     */
    public static class CargoType {
        public static final String GENERAL = "GENERAL";    // 普通货物
        public static final String DANGEROUS = "DANGEROUS"; // 危险品
        public static final String FRAGILE = "FRAGILE";    // 易碎品
        public static final String LIQUID = "LIQUID";      // 液体
        public static final String FROZEN = "FROZEN";      // 冷冻品
    }
    
    /**
     * 运输方式
     */
    public static class TransportMode {
        public static final String SEA = "SEA";            // 海运
        public static final String AIR = "AIR";            // 空运
        public static final String LAND = "LAND";          // 陆运
        public static final String RAIL = "RAIL";          // 铁路
        public static final String MULTIMODAL = "MULTIMODAL"; // 多式联运
    }
    
    /**
     * 容器类型
     */
    public static class ContainerType {
        public static final String FCL_20 = "FCL_20";      // 20尺整箱
        public static final String FCL_40 = "FCL_40";      // 40尺整箱
        public static final String FCL_40HC = "FCL_40HC";  // 40尺高箱
        public static final String LCL = "LCL";            // 拼箱
    }
    
    /**
     * 文件类型
     */
    public static class FileType {
        public static final String IMAGE = "IMAGE";        // 图片
        public static final String DOCUMENT = "DOCUMENT";  // 文档
        public static final String EXCEL = "EXCEL";        // Excel文件
        public static final String PDF = "PDF";            // PDF文件
    }
    
    /**
     * 缓存键前缀
     */
    public static class CacheKey {
        public static final String USER_INFO = "user:info:";
        public static final String USER_PERMISSIONS = "user:permissions:";
        public static final String ROUTE_INFO = "route:info:";
        public static final String PORT_INFO = "port:info:";
        public static final String RATE_INFO = "rate:info:";
    }
    
    /**
     * 默认值
     */
    public static class DefaultValue {
        public static final int PAGE_SIZE = 10;            // 默认分页大小
        public static final int MAX_PAGE_SIZE = 100;       // 最大分页大小
        public static final String DEFAULT_SORT = "id";    // 默认排序字段
        public static final String DEFAULT_ORDER = "desc"; // 默认排序方向
    }
    
    /**
     * 正则表达式
     */
    public static class Regex {
        public static final String PHONE = "^1[3-9]\\d{9}$";
        public static final String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        public static final String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    }
}
