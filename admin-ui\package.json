{"name": "cllcn-platform-admin", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3001", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview --port 3001", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"vue": "^3.5.17", "vue-router": "^4.5.1", "pinia": "^3.0.3", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "echarts": "^5.4.3", "xlsx": "^0.18.5", "dayjs": "^1.11.10", "lodash-es": "^4.17.21"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@types/lodash-es": "^4.17.12", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}