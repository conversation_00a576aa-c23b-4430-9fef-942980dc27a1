package com.example.cllcnplatformbackend.utils;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 统一响应结果类
 * 
 * @param <T> 数据类型
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> {

    private Integer code;

    private String message;

    private T data;
    
    /**
     * 私有构造方法
     */
    private Result() {
    }
    
    /**
     * 私有构造方法
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     */
    private Result(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功返回结果
     * @param <T> 数据类型
     * @return 成功结果
     */
    public static <T> Result<T> success() {
        return new Result<>(200, "操作成功", null);
    }
    
    /**
     * 成功返回结果
     * @param data 数据
     * @param <T> 数据类型
     * @return 成功结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(200, "操作成功", data);
    }
    
    /**
     * 成功返回结果
     * @param message 消息
     * @param data 数据
     * @param <T> 数据类型
     * @return 成功结果
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(200, message, data);
    }
    
    /**
     * 失败返回结果
     * @param message 错误信息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(500, message, null);
    }
    
    /**
     * 失败返回结果
     * @param code 状态码
     * @param message 错误信息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null);
    }
    
    /**
     * 参数验证失败返回结果
     * @param message 提示信息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> validateFailed(String message) {
        return new Result<>(400, message, null);
    }
    
    /**
     * 未授权返回结果
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> unauthorized() {
        return new Result<>(401, "暂未登录或token已过期", null);
    }
    
    /**
     * 未授权返回结果
     * @param message 提示信息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(401, message, null);
    }
    
    /**
     * 禁止访问返回结果
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> forbidden() {
        return new Result<>(403, "没有相关权限", null);
    }
    
    /**
     * 禁止访问返回结果
     * @param message 提示信息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(403, message, null);
    }
    
    /**
     * 资源不存在返回结果
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> notFound() {
        return new Result<>(404, "资源不存在", null);
    }
    
    /**
     * 资源不存在返回结果
     * @param message 提示信息
     * @param <T> 数据类型
     * @return 失败结果
     */
    public static <T> Result<T> notFound(String message) {
        return new Result<>(404, message, null);
    }
    
    /**
     * 判断是否成功
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return this.code != null && this.code == 200;
    }
    
    // Getter and Setter methods
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
}
