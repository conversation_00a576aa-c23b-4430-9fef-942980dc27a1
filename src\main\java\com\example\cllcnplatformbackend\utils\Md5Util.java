package com.example.cllcnplatformbackend.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5加密工具类
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Slf4j
public class Md5Util {
    
    /**
     * MD5加密
     *
     * @param str 待加密字符串
     * @return 加密后的字符串
     */
    public static String getMD5(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(str.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5加密失败", e);
            throw new RuntimeException("MD5加密失败", e);
        }
    }
    
    /**
     * MD5加密（带盐值）
     *
     * @param str 待加密字符串
     * @param salt 盐值
     * @return 加密后的字符串
     */
    public static String getMD5WithSalt(String str, String salt) {
        if (str == null || str.isEmpty()) {
            return null;
        }
        
        String saltedStr = str + (salt != null ? salt : "");
        return getMD5(saltedStr);
    }
    
    /**
     * 验证MD5
     *
     * @param str 原始字符串
     * @param md5 MD5值
     * @return 是否匹配
     */
    public static boolean verify(String str, String md5) {
        if (str == null || md5 == null) {
            return false;
        }
        
        String calculatedMd5 = getMD5(str);
        return md5.equalsIgnoreCase(calculatedMd5);
    }
    
    /**
     * 验证MD5（带盐值）
     *
     * @param str 原始字符串
     * @param salt 盐值
     * @param md5 MD5值
     * @return 是否匹配
     */
    public static boolean verifyWithSalt(String str, String salt, String md5) {
        if (str == null || md5 == null) {
            return false;
        }
        
        String calculatedMd5 = getMD5WithSalt(str, salt);
        return md5.equalsIgnoreCase(calculatedMd5);
    }
}
