# CLLCN国际物流平台 - 架构集成方案

## 1. 架构方案分析

### 1.1 现状分析

#### 1.1.1 公司管理系统现状
- **数据库名称**: `company_management_system`
- **核心用户表**: 
  - `employee` 表：包含admin、manager、employee三种角色，已有完整认证字段（email、password）
  - `client` 表：客户信息表，**缺少password字段**，无法直接登录
- **业务复杂度**: 包含部门、职位、工资、业绩、推广、会议、股票等多个业务模块
- **数据完整性**: 表结构完善，外键约束完整

#### 1.1.2 物流平台需求
- **用户类型**: 客户、员工、管理员三类用户
- **权限需求**: 
  - 客户：查询航线、在线订舱、查看自己的订单
  - 员工：处理订单、更新物流状态、客户服务
  - 管理员：管理所有基础数据、用户管理、订单管理
- **业务复杂度**: 海运整箱业务，包含复杂的运价计算和订单管理

### 1.2 架构方案对比

#### 1.2.1 方案一：完全独立数据库

**架构描述**：
- 物流平台使用完全独立的数据库
- 通过API或消息队列同步用户数据
- 两个系统完全解耦

**优点**：
- ✅ 系统完全解耦，独立发展
- ✅ 数据隔离性好，安全性高
- ✅ 技术栈可以独立选择和升级
- ✅ 部署和维护相对独立
- ✅ 性能影响相互隔离

**缺点**：
- ❌ 数据同步复杂，一致性难以保证
- ❌ 用户需要维护两套账号体系
- ❌ 开发和维护成本高
- ❌ 实时性要求高的场景处理困难
- ❌ 跨系统查询和报表复杂

#### 1.2.2 方案二：完全共享数据库

**架构描述**：
- 两个系统使用同一个数据库
- 所有表都在同一个数据库中
- 通过应用层权限控制访问

**优点**：
- ✅ 数据一致性好，无同步问题
- ✅ 用户统一认证，体验好
- ✅ 开发效率高，查询方便
- ✅ 事务处理简单
- ✅ 跨系统数据分析容易

**缺点**：
- ❌ 系统耦合度高，影响独立发展
- ❌ 数据库压力集中，性能瓶颈
- ❌ 权限管理复杂，安全风险高
- ❌ 扩展性受限，难以水平扩展
- ❌ 故障影响范围大

#### 1.2.3 方案三：混合架构（推荐）

**架构描述**：
- 用户认证数据共享（employee、client表）
- 业务数据独立（物流业务表独立数据库）
- 通过数据库视图或API同步必要信息

**优点**：
- ✅ 用户统一认证，无需两套账号
- ✅ 业务数据独立，系统适度解耦
- ✅ 开发效率高，维护成本适中
- ✅ 扩展性好，可独立优化
- ✅ 数据一致性可控
- ✅ 权限管理相对简单

**缺点**：
- ⚠️ 需要设计跨数据库关联机制
- ⚠️ 部分数据同步仍需处理
- ⚠️ 架构复杂度适中

## 2. 推荐架构方案

### 2.1 混合架构设计

#### 2.1.1 数据库架构
```mermaid
graph TB
    subgraph "公司管理数据库 (company_management_system)"
        A[employee 员工表]
        B[client 客户表]
        C[department 部门表]
        D[其他管理业务表...]
    end
    
    subgraph "物流平台数据库 (logistics_platform)"
        E[country 国家表]
        F[port 港口表]
        G[route 航线表]
        H[pricing 运价表]
        I[order_info 订单表]
        J[其他物流业务表...]
    end
    
    subgraph "用户认证层"
        K[统一认证API]
        L[权限管理]
    end
    
    A --> K
    B --> K
    K --> L
    L --> I
    L --> H
```

#### 2.1.2 用户类型映射
- **客户用户**: `client` 表 → 物流平台客户权限
- **员工用户**: `employee` 表 (role='employee') → 物流平台员工权限  
- **管理员用户**: `employee` 表 (role='admin') → 物流平台管理员权限

### 2.2 技术实现方案

#### 2.2.1 跨数据库关联策略
1. **数据库视图方式**（推荐）
```sql
-- 在物流平台数据库中创建用户视图
CREATE VIEW v_platform_users AS
SELECT 
    CONCAT('E', employee_id) as user_id,
    'employee' as user_type,
    name as real_name,
    email,
    phone as mobile,
    role,
    status
FROM company_management_system.employee
WHERE status = 'Active'
UNION ALL
SELECT 
    CONCAT('C', client_id) as user_id,
    'client' as user_type,
    name as real_name,
    email,
    phone as mobile,
    'client' as role,
    CASE WHEN status = '审核通过' THEN 'Active' ELSE 'Inactive' END as status
FROM company_management_system.client
WHERE status IN ('审核通过', '已合作');
```

2. **API同步方式**（备选）
- 通过定时任务或事件触发同步用户信息
- 在物流平台维护用户信息的副本
- 保证数据最终一致性

#### 2.2.2 认证授权机制
```java
// 统一用户认证服务
@Service
public class UnifiedAuthService {
    
    public AuthResult authenticate(String email, String password) {
        // 1. 先尝试员工表认证
        Employee employee = employeeRepository.findByEmail(email);
        if (employee != null && passwordEncoder.matches(password, employee.getPassword())) {
            return AuthResult.success(employee, UserType.EMPLOYEE);
        }
        
        // 2. 再尝试客户表认证
        Client client = clientRepository.findByEmail(email);
        if (client != null && passwordEncoder.matches(password, client.getPassword())) {
            return AuthResult.success(client, UserType.CLIENT);
        }
        
        return AuthResult.failure("用户名或密码错误");
    }
}
```

## 3. 数据库结构调整

### 3.1 公司管理系统调整

#### 3.1.1 客户表添加密码字段
```sql
-- 为client表添加password字段
ALTER TABLE company_management_system.client 
ADD COLUMN password VARCHAR(255) COMMENT '登录密码(加密)' AFTER email;

-- 添加索引
ALTER TABLE company_management_system.client 
ADD INDEX idx_client_email (email);

-- 为现有客户设置默认密码（需要通知客户修改）
UPDATE company_management_system.client 
SET password = '$2a$10$defaultHashedPassword' 
WHERE password IS NULL;
```

#### 3.1.2 统一用户状态管理
```sql
-- 标准化用户状态
-- employee.status: 'Active', 'Inactive'
-- client.status: '审核通过' → 'Active', 其他 → 'Inactive'
```

### 3.2 物流平台数据库创建

#### 3.2.1 数据库初始化
```sql
-- 创建物流平台数据库
CREATE DATABASE IF NOT EXISTS `logistics_platform` 
DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE logistics_platform;
```

#### 3.2.2 用户关联表设计
```sql
-- 用户映射表（可选，用于缓存用户信息）
CREATE TABLE user_mapping (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    platform_user_id VARCHAR(20) NOT NULL UNIQUE COMMENT '平台用户ID (E123/C456)',
    source_table ENUM('employee', 'client') NOT NULL COMMENT '来源表',
    source_id INT NOT NULL COMMENT '来源表ID',
    user_type ENUM('admin', 'employee', 'client') NOT NULL COMMENT '用户类型',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    mobile VARCHAR(20) COMMENT '手机号',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    last_sync_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_source (source_table, source_id),
    INDEX idx_email (email),
    INDEX idx_user_type (user_type)
) COMMENT '用户映射表';
```

## 4. 权限管理设计

### 4.1 权限矩阵

| 功能模块 | 客户(Client) | 员工(Employee) | 管理员(Admin) |
|---------|-------------|---------------|--------------|
| 航线查询 | ✅ 查看 | ✅ 查看 | ✅ 全部权限 |
| 运价查询 | ✅ 查看 | ✅ 查看 | ✅ 全部权限 |
| 在线订舱 | ✅ 自己的 | ✅ 协助客户 | ✅ 全部权限 |
| 订单管理 | ✅ 自己的 | ✅ 处理订单 | ✅ 全部权限 |
| 物流跟踪 | ✅ 自己的 | ✅ 更新状态 | ✅ 全部权限 |
| 基础数据 | ❌ 无权限 | ❌ 无权限 | ✅ 全部权限 |
| 用户管理 | ❌ 无权限 | ❌ 无权限 | ✅ 全部权限 |

### 4.2 权限实现
```java
@PreAuthorize("hasRole('ADMIN') or (hasRole('CLIENT') and #orderId == authentication.principal.userId)")
public OrderDetail getOrderDetail(Long orderId) {
    // 订单详情查询逻辑
}
```

## 5. 数据同步机制

### 5.1 实时同步（推荐）
- 使用数据库视图实现实时数据访问
- 无需额外同步机制
- 数据一致性最好

### 5.2 定时同步（备选）
```java
@Scheduled(fixedRate = 300000) // 5分钟同步一次
public void syncUserData() {
    // 同步员工数据
    List<Employee> employees = employeeRepository.findByUpdateTimeAfter(lastSyncTime);
    // 更新物流平台用户映射表
    
    // 同步客户数据  
    List<Client> clients = clientRepository.findByUpdateTimeAfter(lastSyncTime);
    // 更新物流平台用户映射表
}
```

## 6. 实施步骤

### 6.1 第一阶段：数据库结构调整（1-2天）
1. 为client表添加password字段
2. 创建物流平台数据库
3. 创建用户视图或映射表
4. 数据库连接配置

### 6.2 第二阶段：统一认证开发（3-5天）
1. 开发统一认证API
2. 实现JWT Token机制
3. 建立权限管理系统
4. 用户登录接口开发

### 6.3 第三阶段：物流业务表创建（2-3天）
1. 根据优化后的数据模型创建物流业务表
2. 建立与用户表的关联关系
3. 初始化基础数据
4. 数据完整性测试

### 6.4 第四阶段：API开发和测试（5-7天）
1. 物流业务API开发
2. 权限控制实现
3. 接口测试和优化
4. 前端集成测试

## 7. 风险控制

### 7.1 数据安全
- 敏感数据加密存储
- 数据库访问权限严格控制
- API接口安全认证

### 7.2 性能优化
- 合理使用数据库索引
- 查询语句优化
- 缓存机制设计

### 7.3 故障恢复
- 数据备份策略
- 故障切换机制
- 监控告警系统
