<!--
  发票管理页面
  
  @description 发票管理页面，包含发票列表、开票申请和下载功能
  <AUTHOR>
  @date 2025-07-25 15:55:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="invoices-page">
    <!-- 页面标题和操作区 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-foreground">发票管理</h1>
        <p class="text-muted-foreground mt-1">管理您的发票和开票申请</p>
      </div>
      <div class="flex space-x-2 mt-4 sm:mt-0">
        <Button variant="outline">
          <Download class="h-4 w-4 mr-2" />
          批量下载
        </Button>
        <Button @click="showCreateDialog = true">
          <Plus class="h-4 w-4 mr-2" />
          申请开票
        </Button>
      </div>
    </div>

    <!-- 搜索和筛选区 -->
    <Card class="mb-6">
      <CardContent class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 搜索框 -->
          <div class="md:col-span-2">
            <Label for="search">搜索发票</Label>
            <div class="relative mt-1">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                v-model="searchQuery"
                placeholder="输入发票号或账单号..."
                class="pl-10"
              />
            </div>
          </div>

          <!-- 状态筛选 -->
          <div>
            <Label for="status">发票状态</Label>
            <Select v-model="selectedStatus">
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="pending">待开票</SelectItem>
                <SelectItem value="issued">已开票</SelectItem>
                <SelectItem value="sent">已发送</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-end">
            <Button variant="outline" @click="resetFilters" class="w-full">
              <RotateCcw class="h-4 w-4 mr-2" />
              重置
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <FileText class="h-6 w-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">总发票</p>
              <p class="text-2xl font-bold">{{ totalInvoices }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <CheckCircle class="h-6 w-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">已开票</p>
              <p class="text-2xl font-bold">{{ issuedInvoices }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <Clock class="h-6 w-6 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">待开票</p>
              <p class="text-2xl font-bold">{{ pendingInvoices }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <Send class="h-6 w-6 text-purple-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm text-muted-foreground">已发送</p>
              <p class="text-2xl font-bold">{{ sentInvoices }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 发票列表 -->
    <Card>
      <CardContent class="p-0">
        <!-- 桌面端表格 -->
        <div class="hidden md:block">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>发票号</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>开票日期</TableHead>
                <TableHead>关联账单</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="invoice in paginatedInvoices" :key="invoice.id">
                <TableCell class="font-medium">{{ invoice.invoiceNumber }}</TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(invoice.status)">
                    {{ getStatusText(invoice.status) }}
                  </Badge>
                </TableCell>
                <TableCell>{{ formatCurrency(invoice.amount, invoice.currency) }}</TableCell>
                <TableCell>{{ formatDate(invoice.issueDate) }}</TableCell>
                <TableCell>{{ getBillNumber(invoice.billId) }}</TableCell>
                <TableCell>
                  <div class="flex space-x-2">
                    <Button variant="ghost" size="sm">
                      <Eye class="h-4 w-4" />
                    </Button>
                    <Button 
                      v-if="invoice.downloadUrl"
                      variant="ghost" 
                      size="sm"
                      @click="downloadInvoice(invoice)"
                    >
                      <Download class="h-4 w-4" />
                    </Button>
                    <Button 
                      v-if="invoice.status === 'issued'"
                      variant="ghost" 
                      size="sm"
                      @click="sendInvoice(invoice)"
                    >
                      <Send class="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- 移动端卡片列表 -->
        <div class="md:hidden p-4 space-y-4">
          <Card v-for="invoice in paginatedInvoices" :key="invoice.id" class="p-4">
            <div class="flex justify-between items-start mb-2">
              <div>
                <h3 class="font-medium">{{ invoice.invoiceNumber }}</h3>
                <Badge :variant="getStatusVariant(invoice.status)" class="mt-1">
                  {{ getStatusText(invoice.status) }}
                </Badge>
              </div>
              <div class="text-right">
                <div class="font-medium">{{ formatCurrency(invoice.amount, invoice.currency) }}</div>
                <div class="text-sm text-muted-foreground">{{ formatDate(invoice.issueDate) }}</div>
              </div>
            </div>
            <div class="space-y-1 text-sm text-muted-foreground">
              <div>关联账单: {{ getBillNumber(invoice.billId) }}</div>
            </div>
            <div class="flex space-x-2 mt-3">
              <Button variant="outline" size="sm" class="flex-1">
                <Eye class="h-4 w-4 mr-2" />
                查看
              </Button>
              <Button 
                v-if="invoice.downloadUrl"
                variant="outline" 
                size="sm" 
                class="flex-1"
                @click="downloadInvoice(invoice)"
              >
                <Download class="h-4 w-4 mr-2" />
                下载
              </Button>
            </div>
          </Card>
        </div>

        <!-- 分页 -->
        <div class="p-4 border-t">
          <div class="flex items-center justify-between">
            <div class="text-sm text-muted-foreground">
              显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredInvoices.length) }} 
              共 {{ filteredInvoices.length }} 条记录
            </div>
            <div class="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === 1"
                @click="currentPage--"
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 开票申请对话框 -->
    <Dialog v-model:open="showCreateDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>申请开票</DialogTitle>
          <DialogDescription>
            请选择需要开票的账单
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label for="bill">选择账单</Label>
            <Select v-model="selectedBillId">
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="选择账单" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem 
                  v-for="bill in availableBills" 
                  :key="bill.id" 
                  :value="bill.id"
                >
                  {{ bill.billNumber }} - {{ formatCurrency(bill.amount, bill.currency) }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showCreateDialog = false">
            取消
          </Button>
          <Button @click="createInvoice" :disabled="!selectedBillId">
            申请开票
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { 
  Search, 
  RotateCcw, 
  Eye, 
  Download, 
  Plus,
  Send,
  FileText, 
  CheckCircle, 
  Clock
} from 'lucide-vue-next'
import { mockInvoices, mockBills } from '@/data/mockData'
import { InvoiceStatus, BillStatus } from '@/types/workspace'
import type { Invoice } from '@/types/workspace'

// 响应式数据
const searchQuery = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showCreateDialog = ref(false)
const selectedBillId = ref('')

// 计算属性
const filteredInvoices = computed(() => {
  let invoices = mockInvoices

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    invoices = invoices.filter(invoice => 
      invoice.invoiceNumber.toLowerCase().includes(query) ||
      invoice.billId.toLowerCase().includes(query)
    )
  }

  // 状态筛选
  if (selectedStatus.value) {
    invoices = invoices.filter(invoice => invoice.status === selectedStatus.value)
  }

  return invoices
})

const totalPages = computed(() => Math.ceil(filteredInvoices.value.length / pageSize.value))

const paginatedInvoices = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredInvoices.value.slice(start, end)
})

// 统计数据
const totalInvoices = computed(() => mockInvoices.length)
const issuedInvoices = computed(() => mockInvoices.filter(invoice => invoice.status === InvoiceStatus.ISSUED).length)
const pendingInvoices = computed(() => mockInvoices.filter(invoice => invoice.status === InvoiceStatus.PENDING).length)
const sentInvoices = computed(() => mockInvoices.filter(invoice => invoice.status === InvoiceStatus.SENT).length)

// 可开票的账单（已付款的账单）
const availableBills = computed(() => 
  mockBills.filter(bill => bill.status === BillStatus.PAID)
)

// 方法
const getStatusVariant = (status: InvoiceStatus) => {
  switch (status) {
    case InvoiceStatus.PENDING: return 'secondary'
    case InvoiceStatus.ISSUED: return 'default'
    case InvoiceStatus.SENT: return 'default'
    case InvoiceStatus.CANCELLED: return 'destructive'
    default: return 'secondary'
  }
}

const getStatusText = (status: InvoiceStatus) => {
  switch (status) {
    case InvoiceStatus.PENDING: return '待开票'
    case InvoiceStatus.ISSUED: return '已开票'
    case InvoiceStatus.SENT: return '已发送'
    case InvoiceStatus.CANCELLED: return '已取消'
    default: return '未知'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

const getBillNumber = (billId: string) => {
  const bill = mockBills.find(b => b.id === billId)
  return bill ? bill.billNumber : `BILL-${billId}`
}

const downloadInvoice = (invoice: Invoice) => {
  // 模拟下载功能
  console.log('下载发票:', invoice.invoiceNumber)
  // 实际实现中这里会触发文件下载
}

const sendInvoice = (invoice: Invoice) => {
  // 模拟发送功能
  console.log('发送发票:', invoice.invoiceNumber)
  // 实际实现中这里会发送发票给客户
}

const createInvoice = () => {
  // 模拟创建发票
  console.log('创建发票，账单ID:', selectedBillId.value)
  showCreateDialog.value = false
  selectedBillId.value = ''
  // 实际实现中这里会调用API创建发票
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
}
</script>

<style scoped>
.invoices-page {
  /* 页面样式 */
}
</style>
