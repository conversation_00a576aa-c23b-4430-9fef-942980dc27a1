<!--
  账号管理页面
  
  @description 账号管理页面，包含账号设置、安全设置等功能
  <AUTHOR>
  @date 2025-07-25 15:55:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="account-page">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-foreground">账号管理</h1>
      <p class="text-muted-foreground mt-1">管理您的账号设置和安全配置</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧导航 -->
      <div class="lg:col-span-1">
        <Card>
          <CardContent class="p-0">
            <nav class="space-y-1">
              <button
                v-for="tab in tabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                class="w-full flex items-center px-4 py-3 text-left text-sm font-medium rounded-none border-0 transition-colors"
                :class="activeTab === tab.id 
                  ? 'bg-accent text-accent-foreground' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'"
              >
                <component :is="tab.icon" class="h-4 w-4 mr-3" />
                {{ tab.label }}
              </button>
            </nav>
          </CardContent>
        </Card>
      </div>

      <!-- 右侧内容 -->
      <div class="lg:col-span-2">
        <!-- 基本信息 -->
        <Card v-if="activeTab === 'basic'" class="mb-6">
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>更新您的账号基本信息</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label for="company">公司名称</Label>
                <Input
                  id="company"
                  v-model="basicForm.company"
                  placeholder="请输入公司名称"
                />
              </div>
              <div>
                <Label for="contact">联系人</Label>
                <Input
                  id="contact"
                  v-model="basicForm.contact"
                  placeholder="请输入联系人姓名"
                />
              </div>
              <div>
                <Label for="email">邮箱地址</Label>
                <Input
                  id="email"
                  v-model="basicForm.email"
                  type="email"
                  placeholder="请输入邮箱地址"
                />
              </div>
              <div>
                <Label for="phone">联系电话</Label>
                <Input
                  id="phone"
                  v-model="basicForm.phone"
                  placeholder="请输入联系电话"
                />
              </div>
              <div class="md:col-span-2">
                <Label for="address">公司地址</Label>
                <Input
                  id="address"
                  v-model="basicForm.address"
                  placeholder="请输入公司地址"
                />
              </div>
            </div>
            <div class="flex justify-end">
              <Button @click="saveBasicInfo">
                <Save class="h-4 w-4 mr-2" />
                保存更改
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- 安全设置 -->
        <Card v-if="activeTab === 'security'" class="mb-6">
          <CardHeader>
            <CardTitle>安全设置</CardTitle>
            <CardDescription>管理您的账号安全配置</CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <!-- 修改密码 -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">修改密码</h3>
              <div class="grid grid-cols-1 gap-4 max-w-md">
                <div>
                  <Label for="current-password">当前密码</Label>
                  <Input
                    id="current-password"
                    v-model="securityForm.currentPassword"
                    type="password"
                    placeholder="请输入当前密码"
                  />
                </div>
                <div>
                  <Label for="new-password">新密码</Label>
                  <Input
                    id="new-password"
                    v-model="securityForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                  />
                </div>
                <div>
                  <Label for="confirm-password">确认新密码</Label>
                  <Input
                    id="confirm-password"
                    v-model="securityForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                  />
                </div>
                <Button @click="changePassword" class="w-fit">
                  <Lock class="h-4 w-4 mr-2" />
                  更新密码
                </Button>
              </div>
            </div>

            <Separator />

            <!-- 两步验证 -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium">两步验证</h3>
                  <p class="text-sm text-muted-foreground">为您的账号添加额外的安全保护</p>
                </div>
                <Switch v-model:checked="securitySettings.twoFactorEnabled" />
              </div>
            </div>

            <Separator />

            <!-- 登录通知 -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-medium">登录通知</h3>
                  <p class="text-sm text-muted-foreground">当有新设备登录时发送邮件通知</p>
                </div>
                <Switch v-model:checked="securitySettings.loginNotification" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 通知设置 -->
        <Card v-if="activeTab === 'notifications'" class="mb-6">
          <CardHeader>
            <CardTitle>通知设置</CardTitle>
            <CardDescription>管理您的通知偏好</CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-base font-medium">邮件通知</h3>
                  <p class="text-sm text-muted-foreground">接收重要更新的邮件通知</p>
                </div>
                <Switch v-model:checked="notificationSettings.email" />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-base font-medium">短信通知</h3>
                  <p class="text-sm text-muted-foreground">接收紧急事件的短信通知</p>
                </div>
                <Switch v-model:checked="notificationSettings.sms" />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-base font-medium">订单状态更新</h3>
                  <p class="text-sm text-muted-foreground">当订单状态发生变化时通知我</p>
                </div>
                <Switch v-model:checked="notificationSettings.orderUpdates" />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-base font-medium">账单提醒</h3>
                  <p class="text-sm text-muted-foreground">账单到期前提醒我付款</p>
                </div>
                <Switch v-model:checked="notificationSettings.billReminders" />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-base font-medium">营销推广</h3>
                  <p class="text-sm text-muted-foreground">接收产品更新和优惠信息</p>
                </div>
                <Switch v-model:checked="notificationSettings.marketing" />
              </div>
            </div>

            <div class="flex justify-end">
              <Button @click="saveNotificationSettings">
                <Save class="h-4 w-4 mr-2" />
                保存设置
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- API设置 -->
        <Card v-if="activeTab === 'api'" class="mb-6">
          <CardHeader>
            <CardTitle>API设置</CardTitle>
            <CardDescription>管理您的API密钥和访问权限</CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 class="text-base font-medium">API密钥</h3>
                  <p class="text-sm text-muted-foreground">用于访问我们的API服务</p>
                  <code class="text-xs bg-muted px-2 py-1 rounded mt-2 inline-block">
                    {{ maskedApiKey }}
                  </code>
                </div>
                <div class="flex space-x-2">
                  <Button variant="outline" size="sm" @click="regenerateApiKey">
                    <RefreshCw class="h-4 w-4 mr-2" />
                    重新生成
                  </Button>
                  <Button variant="outline" size="sm" @click="copyApiKey">
                    <Copy class="h-4 w-4 mr-2" />
                    复制
                  </Button>
                </div>
              </div>

              <div class="space-y-2">
                <h3 class="text-base font-medium">API权限</h3>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox id="read-orders" v-model:checked="apiPermissions.readOrders" />
                    <Label for="read-orders">读取订单信息</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox id="write-orders" v-model:checked="apiPermissions.writeOrders" />
                    <Label for="write-orders">创建和修改订单</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox id="read-bills" v-model:checked="apiPermissions.readBills" />
                    <Label for="read-bills">读取账单信息</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox id="read-invoices" v-model:checked="apiPermissions.readInvoices" />
                    <Label for="read-invoices">读取发票信息</Label>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end">
              <Button @click="saveApiSettings">
                <Save class="h-4 w-4 mr-2" />
                保存权限
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { 
  User, 
  Lock, 
  Bell, 
  Key, 
  Save, 
  RefreshCw, 
  Copy 
} from 'lucide-vue-next'

// 标签页配置
const tabs = [
  { id: 'basic', label: '基本信息', icon: User },
  { id: 'security', label: '安全设置', icon: Lock },
  { id: 'notifications', label: '通知设置', icon: Bell },
  { id: 'api', label: 'API设置', icon: Key }
]

// 响应式数据
const activeTab = ref('basic')

// 基本信息表单
const basicForm = ref({
  company: '深圳市中航物流有限公司',
  contact: '张三',
  email: '<EMAIL>',
  phone: '150****7715',
  address: '深圳市南山区科技南路26号大冲商务中心A座18楼'
})

// 安全设置表单
const securityForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const securitySettings = ref({
  twoFactorEnabled: false,
  loginNotification: true
})

// 通知设置
const notificationSettings = ref({
  email: true,
  sms: false,
  orderUpdates: true,
  billReminders: true,
  marketing: false
})

// API设置
const apiKey = ref('sk_live_1234567890abcdef1234567890abcdef')
const apiPermissions = ref({
  readOrders: true,
  writeOrders: false,
  readBills: true,
  readInvoices: true
})

// 计算属性
const maskedApiKey = computed(() => {
  const key = apiKey.value
  return key.substring(0, 8) + '****' + key.substring(key.length - 8)
})

// 方法
const saveBasicInfo = () => {
  console.log('保存基本信息:', basicForm.value)
  // 实际实现中这里会调用API保存数据
}

const changePassword = () => {
  if (securityForm.value.newPassword !== securityForm.value.confirmPassword) {
    alert('新密码和确认密码不匹配')
    return
  }
  console.log('修改密码')
  // 实际实现中这里会调用API修改密码
  securityForm.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
}

const saveNotificationSettings = () => {
  console.log('保存通知设置:', notificationSettings.value)
  // 实际实现中这里会调用API保存设置
}

const regenerateApiKey = () => {
  // 模拟重新生成API密钥
  apiKey.value = 'sk_live_' + Math.random().toString(36).substring(2, 34)
  console.log('重新生成API密钥:', apiKey.value)
}

const copyApiKey = () => {
  navigator.clipboard.writeText(apiKey.value)
  console.log('API密钥已复制到剪贴板')
}

const saveApiSettings = () => {
  console.log('保存API设置:', apiPermissions.value)
  // 实际实现中这里会调用API保存权限设置
}
</script>

<style scoped>
.account-page {
  /* 页面样式 */
}
</style>
