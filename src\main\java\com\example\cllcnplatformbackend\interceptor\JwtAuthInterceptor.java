package com.example.cllcnplatformbackend.interceptor;

import com.example.cllcnplatformbackend.utils.JwtUtil;
import com.example.cllcnplatformbackend.utils.ThreadLocalUtil;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证拦截器
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthInterceptor implements HandlerInterceptor {

    private final JwtUtil jwtUtil;

    private static final String AUTH_HEADER = "Authorization";
    private static final String TOKEN_PREFIX = "Bearer ";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }
        
        // 获取请求路径
        String requestURI = request.getRequestURI();
        
        // 白名单路径，无需认证
        if (isWhiteListPath(requestURI)) {
            return true;
        }
        
        // 从请求头中获取token
        String token = request.getHeader(AUTH_HEADER);

        // 如果请求头中没有token，尝试从请求参数中获取
        if (token == null || token.isEmpty()) {
            token = request.getParameter("token");
        }

        // 如果token存在且以Bearer 开头，去掉前缀
        if (token != null && token.startsWith(TOKEN_PREFIX)) {
            token = token.substring(TOKEN_PREFIX.length());
        }

        // 如果token为空，返回401未授权状态码
        if (token == null || token.isEmpty()) {
            log.warn("请求路径: {} 缺少token", requestURI);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"未登录或token已过期\"}");
            return false;
        }
        
        // 验证token是否有效
        if (!jwtUtil.validateToken(token)) {
            log.warn("请求路径: {} token验证失败", requestURI);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"token无效或已过期\"}");
            return false;
        }

        // 从token中获取用户信息并存入ThreadLocal
        try {
            Claims claims = jwtUtil.parseToken(token);
            String platformUserId = claims.get("platformUserId", String.class);
            String username = claims.getSubject();
            String role = claims.get("role", String.class);
            String userType = claims.get("userType", String.class);
            String realName = claims.get("realName", String.class);

            // 只存储必要的用户信息到ThreadLocal
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userId", platformUserId); // 保持key为userId，但值为platformUserId格式
            userInfo.put("username", username);
            userInfo.put("role", role);
            userInfo.put("userType", userType);
            userInfo.put("realName", realName);

            // 检查管理后台权限
            if (isAdminPath(requestURI)) {
                if (!"admin".equals(userType) || !platformUserId.startsWith("E")) {
                    log.warn("非管理员尝试访问管理后台 - 路径: {}, 用户: {}, 类型: {}", requestURI, platformUserId, userType);
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"code\":403,\"message\":\"权限不足，只有管理员才能访问管理后台\"}");
                    return false;
                }
            }

            // 将用户信息存储到ThreadLocal中
            ThreadLocalUtil.set("userInfo", userInfo);
            // 存储token
            ThreadLocalUtil.set("token", token);

            log.debug("用户认证成功: platformUserId={}, username={}, userType={}", platformUserId, username, userType);
            return true;

        } catch (Exception e) {
            log.error("JWT token解析失败: {}", e.getMessage());
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"token解析失败\"}");
            return false;
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 请求结束后清除ThreadLocal中的数据，防止内存泄漏
        ThreadLocalUtil.clear();
    }
    
    /**
     * 判断是否为白名单路径
     * 优化后的白名单配置，仅保留必要的无需认证路径
     */
    private boolean isWhiteListPath(String path) {
        // 白名单路径配置
        String[] whiteList = {
                "/auth/login",          // 客户端登录接口
                "/auth/admin/login",    // 管理后台登录接口
                "/auth/register",       // 注册接口
                "/auth/unlock",         // 临时解锁接口（调试用）
                "/favicon.ico",         // 网站图标
                "/error",              // 错误页面
                "/uploads/**"           // 静态资源文件访问路径
        };

        for (String whitePath : whiteList) {
            if (whitePath.endsWith("/**")) {
                String prefix = whitePath.substring(0, whitePath.length() - 3);
                if (path.startsWith(prefix)) {
                    return true;
                }
            } else if (path.equals(whitePath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否为管理后台路径
     * @param path 请求路径
     * @return 是否为管理后台路径
     */
    private boolean isAdminPath(String path) {
        // 定义管理后台路径模式
        String[] adminPaths = {
                "/admin/**"             // 管理后台相关路径
        };

        for (String adminPath : adminPaths) {
            if (adminPath.endsWith("/**")) {
                String prefix = adminPath.substring(0, adminPath.length() - 3);
                if (path.startsWith(prefix)) {
                    return true;
                }
            } else if (path.equals(adminPath)) {
                return true;
            }
        }

        return false;
    }
}
