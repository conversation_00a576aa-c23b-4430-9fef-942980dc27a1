package com.example.cllcnplatformbackend.controller;

import com.example.cllcnplatformbackend.utils.Result;
import com.example.cllcnplatformbackend.dto.LoginRequest;
import com.example.cllcnplatformbackend.dto.LoginResponse;
import com.example.cllcnplatformbackend.service.AuthService;
import com.example.cllcnplatformbackend.utils.UserContext;
import com.example.cllcnplatformbackend.utils.ThreadLocalUtil;
import com.example.cllcnplatformbackend.utils.TokenBlacklistUtil;
import com.example.cllcnplatformbackend.utils.TokenManagerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 客户端认证控制器
 * 提供客户端用户登录、令牌刷新等认证功能
 * 支持员工和客户登录（管理后台请使用AdminAuthController）
 *
 * <AUTHOR> Platform
 * @since 2025-07-31
 * @updated 2025-08-02 (分离管理后台认证)
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthService authService;
    private final com.example.cllcnplatformbackend.service.LoginAttemptService loginAttemptService;
    private final TokenBlacklistUtil tokenBlacklistUtil;
    private final TokenManagerUtil tokenManagerUtil;

    /**
     * 客户端用户登录
     * 支持员工和客户通过邮箱或手机号登录
     * 注意：管理后台登录请使用 /admin/auth/login
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody @Valid LoginRequest loginRequest) {
        try {
            log.debug("用户登录请求 - 账号: {}", loginRequest.getAccount());

            LoginResponse response = authService.login(loginRequest);

            log.debug("用户登录成功 - 平台用户ID: {}", response.getUserInfo().getPlatformUserId());
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("用户登录失败 - 账号: {}, 错误: {}", loginRequest.getAccount(), e.getMessage());
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 管理后台登录
     * 只允许员工且role为admin的用户登录
     */
    @PostMapping("/admin/login")
    public Result<LoginResponse> adminLogin(@RequestBody @Valid LoginRequest loginRequest) {
        try {
            log.debug("管理后台登录请求 - 账号: {}", loginRequest.getAccount());

            LoginResponse response = authService.adminLogin(loginRequest);

            log.info("管理后台登录成功 - 平台用户ID: {}", response.getUserInfo().getPlatformUserId());
            return Result.success(response);

        } catch (Exception e) {
            log.error("管理后台登录失败 - 账号: {}, 错误: {}", loginRequest.getAccount(), e.getMessage());
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     * 需要JWT认证
     */
    @GetMapping("/me")
    public Result<LoginResponse.UserInfo> getCurrentUser() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            log.debug("获取用户信息请求 - 用户ID: {}", currentUserId);
            
            LoginResponse.UserInfo userInfo = authService.getUserInfo(currentUserId);
            
            return Result.success(userInfo);
            
        } catch (Exception e) {
            log.error("获取用户信息失败 - 错误: {}", e.getMessage());
            return Result.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refreshToken(
            @RequestBody Map<String, String> request) {
        try {
            String refreshToken = request.get("refreshToken");
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                return Result.validateFailed("刷新令牌不能为空");
            }
            
            log.info("刷新令牌请求");
            
            String newAccessToken = authService.refreshToken(refreshToken);
            
            Map<String, Object> response = Map.of(
                "accessToken", newAccessToken,
                "tokenType", "Bearer",
                "expiresIn", 7200L
            );
            
            log.info("令牌刷新成功");
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("刷新令牌失败 - 错误: {}", e.getMessage());
            return Result.error("刷新令牌失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * 注意：这个接口会使当前令牌失效
     * 客户端应该清除本地存储的令牌
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            String currentToken = ThreadLocalUtil.get("token", String.class);

            log.info("用户登出 - 用户ID: {}", currentUserId);

            // 检查Token是否存在
            if (currentToken == null || currentToken.trim().isEmpty()) {
                log.warn("用户登出时未找到Token - 用户ID: {}", currentUserId);
                // 即使没有Token也返回成功，保证幂等性
                return Result.success();
            }

            // 执行Token失效操作
            invalidateToken(currentToken, currentUserId);

            log.info("用户登出成功 - 用户ID: {}", currentUserId);
            return Result.success();

        } catch (Exception e) {
            log.error("用户登出失败 - 错误: {}", e.getMessage());
            // 即使出现异常也返回成功，避免客户端重复请求
            return Result.success();
        }
    }

    /**
     * 使Token失效
     *
     * @param token JWT Token值
     * @param userId 用户ID
     */
    private void invalidateToken(String token, String userId) {
        try {
            // 1. 将Token加入黑名单
            boolean blacklisted = tokenBlacklistUtil.addToBlacklist(token);
            if (blacklisted) {
                log.debug("Token已加入黑名单 - 用户ID: {}", userId);
            } else {
                log.warn("Token加入黑名单失败 - 用户ID: {}", userId);
            }

            // 2. 从Redis存储中移除Token
            boolean removed = tokenManagerUtil.removeToken(token);
            if (removed) {
                log.debug("Token已从存储中移除 - 用户ID: {}", userId);
            } else {
                log.warn("Token从存储中移除失败 - 用户ID: {}", userId);
            }

        } catch (Exception e) {
            // Redis操作失败时记录日志但不抛出异常
            log.error("Token失效操作异常 - 用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }
}
