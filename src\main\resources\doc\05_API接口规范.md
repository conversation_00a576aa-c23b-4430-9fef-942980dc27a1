# CLLCN国际物流平台 - API接口规范

## 1. 接口设计原则

### 1.1 RESTful设计
- 使用标准HTTP方法：GET、POST、PUT、DELETE
- 资源导向的URL设计
- 统一的响应格式
- 合理的HTTP状态码使用

### 1.2 接口规范
- **Base URL**: `https://api.cllcn.com/v1`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **认证方式**: JWT Token
- **API版本**: 通过URL路径版本控制

### 1.3 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid"
}
```

### 1.4 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "详细错误信息",
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid"
}
```

## 2. 认证授权接口

### 2.1 用户注册
**接口地址**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "string",
  "realName": "string",
  "mobile": "string",
  "email": "string",
  "password": "string",
  "verifyCode": "string"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": 123,
    "username": "testuser"
  }
}
```

### 2.2 用户登录
**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "string", // 手机号或邮箱
  "password": "string"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_string",
    "refreshToken": "refresh_token_string",
    "userInfo": {
      "userId": 123,
      "username": "testuser",
      "realName": "张三",
      "userType": 1,
      "authStatus": 2
    }
  }
}
```

### 2.3 刷新Token
**接口地址**: `POST /auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "string"
}
```

### 2.4 用户登出
**接口地址**: `POST /auth/logout`

**请求头**: `Authorization: Bearer {token}`

## 3. 用户管理接口

### 3.1 获取用户信息
**接口地址**: `GET /user/profile`

**请求头**: `Authorization: Bearer {token}`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "userId": 123,
    "username": "testuser",
    "realName": "张三",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "userType": 1,
    "status": 1,
    "lastLoginTime": "2024-01-01T00:00:00Z",
    "createTime": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 更新用户信息
**接口地址**: `PUT /user/profile`

**请求参数**:
```json
{
  "realName": "string",
  "mobile": "string",
  "email": "string"
}
```

### 3.3 修改密码
**接口地址**: `PUT /user/password`

**请求参数**:
```json
{
  "oldPassword": "string",
  "newPassword": "string"
}
```

## 4. 企业认证接口

### 4.1 提交企业认证
**接口地址**: `POST /company/auth`

**请求参数**:
```json
{
  "companyName": "string",
  "industry": "string",
  "businessLicense": "string", // 图片URL
  "companyAddress": "string",
  "socialCreditCode": "string",
  "companyScale": 1,
  "businessCard": "string" // 图片URL
}
```

### 4.2 获取企业认证信息
**接口地址**: `GET /company/auth`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "authId": 123,
    "companyName": "测试公司",
    "authStatus": 2,
    "industry": "物流运输",
    "companyAddress": "上海市浦东新区",
    "socialCreditCode": "91310000000000000X",
    "companyScale": 2,
    "authRemark": "认证通过",
    "authTime": "2024-01-01T00:00:00Z"
  }
}
```

### 4.3 更新企业认证信息
**接口地址**: `PUT /company/auth`

**请求参数**: 同提交企业认证

## 5. 基础数据接口

### 5.1 获取国家列表
**接口地址**: `GET /basic/countries`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "countryId": 1,
      "countryCode": "CN",
      "countryNameEn": "China",
      "countryNameCn": "中国"
    }
  ]
}
```

### 5.2 获取港口列表
**接口地址**: `GET /basic/ports`

**请求参数**:
- `countryId`: 国家ID（可选）
- `keyword`: 搜索关键词（可选）
- `isHot`: 是否热门港口（可选）
- `page`: 页码，默认1
- `size`: 每页大小，默认20

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "total": 100,
    "page": 1,
    "size": 20,
    "list": [
      {
        "portId": 1,
        "portCode": "CNSHA",
        "portNameEn": "Shanghai",
        "portNameCn": "上海",
        "countryId": 1,
        "countryName": "中国",
        "isHot": true
      }
    ]
  }
}
```

### 5.3 获取船司列表
**接口地址**: `GET /basic/shipping-companies`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "companyId": 1,
      "companyCode": "MSK",
      "companyNameEn": "Maersk",
      "companyNameCn": "马士基",
      "logoUrl": "https://example.com/logo.png"
    }
  ]
}
```

### 5.4 获取集装箱类型
**接口地址**: `GET /basic/container-types`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "typeId": 1,
      "typeCode": "20GP",
      "typeName": "20英尺普通柜",
      "description": "20英尺标准集装箱"
    }
  ]
}
```

## 6. 航线运价接口

### 6.1 搜索航线
**接口地址**: `GET /routes/search`

**请求参数**:
- `originPortId`: 起运港ID
- `destinationPortId`: 目的港ID
- `shippingCompanyIds`: 船司ID列表（可选）
- `isDirect`: 是否直达（可选）
- `containerType`: 箱型优先级（可选）
- `departureDate`: 出发日期（可选）
- `page`: 页码，默认1
- `size`: 每页大小，默认10

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "total": 50,
    "page": 1,
    "size": 10,
    "list": [
      {
        "routeId": 1,
        "routeCode": "MEX5",
        "shippingCompany": {
          "companyId": 1,
          "companyName": "马士基",
          "logoUrl": "https://example.com/logo.png"
        },
        "originPort": {
          "portId": 1,
          "portName": "上海"
        },
        "destinationPort": {
          "portId": 2,
          "portName": "洛杉矶"
        },
        "transitPort": {
          "portId": 3,
          "portName": "釜山"
        },
        "voyageDays": 15,
        "isDirect": false,
        "isSpecial": true,
        "cutOffTime": "18:00:00",
        "etdTime": "2024-01-15T00:00:00Z",
        "validFrom": "2024-01-01",
        "validTo": "2024-12-31",
        "pricing": [
          {
            "containerType": "20GP",
            "oceanFreight": 2010.00,
            "totalAmount": 2220.00,
            "currency": "USD"
          }
        ]
      }
    ]
  }
}
```

### 6.2 获取航线详细运价
**接口地址**: `GET /routes/{routeId}/pricing`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "routeInfo": {
      "routeId": 1,
      "routeCode": "MEX5",
      "shippingCompany": "马士基",
      "originPort": "上海",
      "destinationPort": "洛杉矶",
      "voyageDays": 15
    },
    "pricingDetails": [
      {
        "containerType": "20GP",
        "fees": [
          {
            "feeType": "海运费",
            "amount": 2010.00,
            "currency": "USD",
            "chargeUnit": "箱型",
            "paymentMethod": "预付"
          },
          {
            "feeType": "安保费",
            "amount": 10.00,
            "currency": "USD",
            "chargeUnit": "箱",
            "paymentMethod": "预付"
          }
        ],
        "totalAmount": 2220.00,
        "estimatedTotal": {
          "usd": 2220.00,
          "cny": 15540.00
        }
      }
    ]
  }
}
```

### 6.3 获取运价历史
**接口地址**: `GET /routes/{routeId}/pricing-history`

**请求参数**:
- `containerType`: 集装箱类型
- `dateRange`: 时间范围（7d, 30d, 90d, 1y）
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "chartData": [
      {
        "date": "2024-01-01",
        "price": 2010.00
      }
    ],
    "statistics": {
      "maxPrice": 2500.00,
      "minPrice": 1800.00,
      "avgPrice": 2010.00,
      "trend": "stable" // up, down, stable
    }
  }
}
```

## 7. 订舱订单接口

### 7.1 提交订舱申请
**接口地址**: `POST /booking/submit`

**请求参数**:
```json
{
  "routeId": 1,
  "containers": [
    {
      "containerTypeId": 1,
      "quantity": 2,
      "weight": 25.5,
      "cargos": [
        {
          "hsCode": "1234567890",
          "cargoNameCn": "测试货物",
          "cargoNameEn": "Test Cargo",
          "cargoNature": 1
        }
      ]
    }
  ],
  "bookingRemark": "订舱备注",
  "doorToDoorRef": "DTD123456",
  "contactInfo": {
    "contactName": "张三",
    "contactPhone": "13800138000",
    "contactEmail": "<EMAIL>",
    "saveAsContact": true
  },
  "attachments": [
    {
      "fileName": "invoice.pdf",
      "fileUrl": "https://example.com/files/invoice.pdf"
    }
  ]
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "订舱申请提交成功",
  "data": {
    "orderId": 123,
    "orderNo": "ORD202401010001"
  }
}
```

### 7.2 获取订单列表
**接口地址**: `GET /orders`

**请求参数**:
- `orderStatus`: 订单状态（可选）
- `logisticsStatus`: 物流状态（可选）
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）
- `keyword`: 搜索关键词（可选）
- `page`: 页码，默认1
- `size`: 每页大小，默认20

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "total": 100,
    "page": 1,
    "size": 20,
    "list": [
      {
        "orderId": 123,
        "orderNo": "ORD202401010001",
        "orderStatus": 2,
        "orderStatusText": "已确认",
        "logisticsStatus": 3,
        "logisticsStatusText": "开船",
        "routeInfo": {
          "originPort": "上海",
          "destinationPort": "洛杉矶",
          "transitPort": "釜山",
          "shippingCompany": "马士基"
        },
        "cutOffTime": "2024-01-15T18:00:00Z",
        "etdTime": "2024-01-16T00:00:00Z",
        "containerSummary": "20GP*2, 40HQ*1",
        "vesselName": "MAERSK ESSEX",
        "voyage": "424E",
        "hblNo": "HBL123456",
        "mblNo": "MBL123456",
        "customerRefNo": "REF123456",
        "soNo": "SO123456",
        "orderSource": 1,
        "orderSourceText": "网站",
        "createTime": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 7.3 获取订单详情
**接口地址**: `GET /orders/{orderId}`

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "orderInfo": {
      "orderId": 123,
      "orderNo": "ORD202401010001",
      "orderStatus": 2,
      "logisticsStatus": 3,
      "totalAmount": 4440.00,
      "currency": "USD",
      "customerRefNo": "REF123456",
      "soNo": "SO123456",
      "hblNo": "HBL123456",
      "mblNo": "MBL123456",
      "vesselName": "MAERSK ESSEX",
      "voyage": "424E",
      "bookingRemark": "订舱备注",
      "doorToDoorRef": "DTD123456",
      "contactInfo": {
        "contactName": "张三",
        "contactPhone": "13800138000",
        "contactEmail": "<EMAIL>"
      },
      "createTime": "2024-01-01T00:00:00Z"
    },
    "routeInfo": {
      "routeCode": "MEX5",
      "shippingCompany": "马士基",
      "originPort": "上海",
      "destinationPort": "洛杉矶",
      "transitPort": "釜山",
      "voyageDays": 15,
      "cutOffTime": "18:00:00",
      "etdTime": "2024-01-16T00:00:00Z"
    },
    "containers": [
      {
        "containerType": "20GP",
        "quantity": 2,
        "weight": 25.5,
        "cargos": [
          {
            "hsCode": "1234567890",
            "cargoNameCn": "测试货物",
            "cargoNameEn": "Test Cargo",
            "cargoNature": 1,
            "cargoNatureText": "普通货物"
          }
        ]
      }
    ],
    "attachments": [
      {
        "fileName": "invoice.pdf",
        "fileUrl": "https://example.com/files/invoice.pdf",
        "fileSize": 1024000
      }
    ]
  }
}
```

### 7.4 取消订单
**接口地址**: `PUT /orders/{orderId}/cancel`

**请求参数**:
```json
{
  "cancelReason": "string"
}
```

### 7.5 下载SO单据
**接口地址**: `GET /orders/{orderId}/so-download`

**响应**: 文件流

## 8. 个人中心接口

### 8.1 联系人管理

#### 8.1.1 获取联系人列表
**接口地址**: `GET /contacts`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "contactId": 1,
      "contactName": "张三",
      "contactEmail": "<EMAIL>",
      "contactPhone": "13800138000",
      "companyName": "测试公司",
      "remark": "常用联系人",
      "createTime": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### 8.1.2 新增联系人
**接口地址**: `POST /contacts`

**请求参数**:
```json
{
  "contactName": "string",
  "contactEmail": "string",
  "contactPhone": "string",
  "companyName": "string",
  "remark": "string"
}
```

#### 8.1.3 更新联系人
**接口地址**: `PUT /contacts/{contactId}`

#### 8.1.4 删除联系人
**接口地址**: `DELETE /contacts/{contactId}`

### 8.2 卡券管理

#### 8.2.1 获取用户卡券
**接口地址**: `GET /coupons`

**请求参数**:
- `status`: 卡券状态（可选）
- `page`: 页码，默认1
- `size`: 每页大小，默认20

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "total": 10,
    "page": 1,
    "size": 20,
    "list": [
      {
        "userCouponId": 1,
        "couponName": "新用户优惠券",
        "couponType": 2,
        "discountType": 1,
        "discountValue": 100.00,
        "minAmount": 1000.00,
        "validFrom": "2024-01-01T00:00:00Z",
        "validTo": "2024-12-31T23:59:59Z",
        "status": 1,
        "statusText": "未使用"
      }
    ]
  }
}
```

### 8.3 消息通知

#### 8.3.1 获取消息列表
**接口地址**: `GET /messages`

**请求参数**:
- `messageType`: 消息类型（可选）
- `readStatus`: 阅读状态（可选）
- `page`: 页码，默认1
- `size`: 每页大小，默认20

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "total": 50,
    "unreadCount": 5,
    "page": 1,
    "size": 20,
    "list": [
      {
        "messageId": 1,
        "messageTitle": "订单状态更新",
        "messageContent": "您的订单ORD202401010001已开船",
        "messageType": 2,
        "messageTypeText": "订单通知",
        "priority": 2,
        "priorityText": "重要",
        "readStatus": 0,
        "readStatusText": "未读",
        "createTime": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 8.3.2 标记消息已读
**接口地址**: `PUT /messages/{messageId}/read`

#### 8.3.3 批量标记已读
**接口地址**: `PUT /messages/batch-read`

**请求参数**:
```json
{
  "messageIds": [1, 2, 3]
}
```

#### 8.3.4 删除消息
**接口地址**: `DELETE /messages/{messageId}`

## 9. 文件上传接口

### 9.1 文件上传
**接口地址**: `POST /upload`

**请求参数**: multipart/form-data
- `file`: 文件
- `fileType`: 文件类型（avatar, license, attachment等）

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "fileName": "original_name.pdf",
    "fileUrl": "https://example.com/files/uuid.pdf",
    "fileSize": 1024000,
    "fileType": "application/pdf"
  }
}
```

## 10. 系统接口

### 10.1 获取首页轮播图
**接口地址**: `GET /system/banners`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "bannerId": 1,
      "title": "新年优惠活动",
      "imageUrl": "https://example.com/banner1.jpg",
      "linkUrl": "https://example.com/promotion",
      "sortOrder": 1
    }
  ]
}
```

### 10.2 搜索历史
**接口地址**: `GET /system/search-history`

**请求参数**:
- `searchType`: 搜索类型
- `limit`: 返回数量，默认10

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "searchKeyword": "上海",
      "searchCount": 5,
      "lastSearchTime": "2024-01-01T00:00:00Z"
    }
  ]
}
```

## 11. 状态码说明

### 11.1 HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 11.2 业务状态码
- `200`: 成功
- `400`: 参数错误
- `401`: 未登录
- `403`: 权限不足
- `404`: 数据不存在
- `409`: 数据冲突
- `422`: 业务逻辑错误
- `500`: 系统错误

## 12. 接口安全

### 12.1 认证机制
- JWT Token认证
- Token有效期：2小时
- Refresh Token有效期：7天
- 支持Token自动刷新

### 12.2 接口限流
- 登录接口：每分钟5次
- 注册接口：每分钟3次
- 其他接口：每分钟100次
- 文件上传：每分钟10次

### 12.3 数据加密
- 密码使用BCrypt加密
- 敏感数据传输使用HTTPS
- 文件上传支持病毒扫描
