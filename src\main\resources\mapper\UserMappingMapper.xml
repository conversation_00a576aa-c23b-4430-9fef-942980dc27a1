<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.cllcnplatformbackend.mapper.UserMappingMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.cllcnplatformbackend.entity.logistics.UserMapping">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="original_user_id" property="originalUserId" jdbcType="BIGINT"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"
                typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="platform_user_id" property="platformUserId" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"
                typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="role" property="role" jdbcType="VARCHAR"
                typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="department_id" property="departmentId" jdbcType="BIGINT"/>
        <result column="position_id" property="positionId" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, original_user_id, user_type, platform_user_id, username, email, phone, name, status, role,
        department_id, position_id, company_id, created_at, updated_at
    </sql>

    <!-- 根据原始用户ID和用户类型查找用户映射 -->
    <select id="findByOriginalUserIdAndUserType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE original_user_id = #{originalUserId} AND user_type = #{userType}
    </select>

    <!-- 根据平台用户ID查找用户映射 -->
    <select id="findByPlatformUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE platform_user_id = #{platformUserId}
    </select>

    <!-- 根据用户名查找用户映射 -->
    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE username = #{username}
    </select>

    <!-- 根据邮箱查找用户映射 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE email = #{email}
    </select>

    <!-- 根据手机号查找用户映射 -->
    <select id="findByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE phone = #{phone}
    </select>

    <!-- 根据用户类型查找所有激活用户 -->
    <select id="findActiveByUserType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE user_type = #{userType} AND status = 'ACTIVE'
    </select>

    <!-- 根据部门ID查找员工用户映射 -->
    <select id="findEmployeesByDepartmentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE user_type = 'EMPLOYEE' AND department_id = #{departmentId} AND status = 'ACTIVE'
    </select>

    <!-- 根据角色查找用户映射 -->
    <select id="findByRole" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_mapping
        WHERE role = #{role} AND status = 'ACTIVE'
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_mapping
        WHERE username = #{username}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_mapping
        WHERE email = #{email}
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_mapping
        WHERE phone = #{phone}
    </select>

    <!-- 插入用户映射 -->
    <insert id="insert" parameterType="com.example.cllcnplatformbackend.entity.logistics.UserMapping"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_mapping (
            original_user_id, user_type, platform_user_id, username, email, phone, name, status, role,
            department_id, position_id, company_id, created_at, updated_at
        ) VALUES (
            #{originalUserId}, #{userType}, #{platformUserId}, #{username}, #{email}, #{phone}, #{name}, #{status}, #{role},
            #{departmentId}, #{positionId}, #{companyId}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 更新用户映射 -->
    <update id="update" parameterType="com.example.cllcnplatformbackend.entity.logistics.UserMapping">
        UPDATE user_mapping
        <set>
            <if test="originalUserId != null">original_user_id = #{originalUserId},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="platformUserId != null">platform_user_id = #{platformUserId},</if>
            <if test="username != null">username = #{username},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="role != null">role = #{role},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="positionId != null">position_id = #{positionId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除用户映射 -->
    <delete id="deleteById">
        DELETE FROM user_mapping WHERE id = #{id}
    </delete>

    <!-- 根据原始用户ID和用户类型删除用户映射 -->
    <delete id="deleteByOriginalUserIdAndUserType">
        DELETE FROM user_mapping 
        WHERE original_user_id = #{originalUserId} AND user_type = #{userType}
    </delete>

</mapper>
