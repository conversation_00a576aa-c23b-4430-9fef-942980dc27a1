# CLLCN国际物流平台 - 项目概述

## 1. 项目简介

### 1.1 项目名称
CLLCN国际物流平台 (CLLCN International Logistics Platform)

### 1.2 项目定位
面向国际物流行业的B2B SaaS平台，专注于海运整箱业务，为货主企业提供在线询价、订舱、订单管理等一站式物流服务。

### 1.3 项目目标
- 构建标准化的海运整箱业务流程
- 提供透明化的运价查询和比较服务
- 实现在线订舱和订单全程跟踪
- 建立完善的企业客户管理体系
- 打造高效的物流服务数字化平台

## 2. 业务背景

### 2.1 行业痛点
- 运价信息不透明，询价效率低
- 订舱流程复杂，纸质化操作多
- 物流跟踪信息滞后，缺乏实时性
- 费用结构复杂，计算容易出错
- 客户管理分散，服务标准化程度低

### 2.2 解决方案
- 建立统一的运价展示和查询系统
- 提供在线订舱和电子化单据管理
- 实现订单状态实时跟踪和通知
- 构建智能化费用计算引擎
- 建立完善的客户关系管理系统

## 3. 核心功能模块

### 3.1 用户管理系统
- **用户注册登录**：支持手机号/邮箱注册，密码登录
- **企业认证**：营业执照认证，企业信息管理
- **权限管理**：多角色权限控制（普通用户、企业用户、员工、管理员）

### 3.2 基础数据管理
- **国家港口管理**：全球港口信息，热门港口标识
- **船司管理**：船公司信息，logo展示
- **首页轮播图**：营销内容管理

### 3.3 海运整箱核心业务
- **航线运价管理**：多维度运价展示，实时价格更新
- **费用计算引擎**：复杂费用结构，多币种支持
- **订舱服务**：在线订舱，货物信息管理
- **运价历史**：价格趋势分析，历史数据查询

### 3.4 订单管理系统
- **订单全生命周期管理**：从订舱到完成的全程跟踪
- **单据管理**：HBL、MBL、SO等单据生成和下载
- **物流跟踪**：实时状态更新，关键节点通知

### 3.5 个人工作台
- **个人中心**：账号信息，企业认证状态
- **联系人管理**：常用联系人维护
- **消息通知**：系统消息，业务通知
- **卡券中心**：优惠券，积分管理

## 4. 技术架构

### 4.1 后端技术栈
- **框架**：Spring Boot 2.x
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **消息队列**：RabbitMQ
- **文件存储**：阿里云OSS
- **API文档**：Swagger/OpenAPI

### 4.2 前端技术栈
- **框架**：Vue 3 + TypeScript
- **构建工具**：Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia

### 4.3 部署架构
- **容器化**：Docker + Docker Compose
- **负载均衡**：Nginx
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack

## 5. 项目特色

### 5.1 业务特色
- **复杂定价体系**：支持多种箱型、多种费用类型的灵活定价
- **智能推荐**：基于历史数据的航线推荐
- **一站式服务**：从询价到订单完成的全流程服务
- **企业级功能**：完善的企业认证和权限管理

### 5.2 技术特色
- **微服务架构**：模块化设计，易于扩展
- **高性能**：缓存优化，数据库优化
- **高可用**：集群部署，故障自动恢复
- **安全性**：多层次安全防护，数据加密

## 6. 项目里程碑

### 6.1 第一阶段（MVP）
- 用户管理和企业认证
- 基础数据管理
- 航线运价查询
- 简单订舱功能

### 6.2 第二阶段
- 完整订单管理
- 物流跟踪
- 消息通知
- 个人工作台

### 6.3 第三阶段
- 高级分析功能
- 移动端适配
- 第三方系统集成
- 国际化支持

## 7. 成功指标

### 7.1 业务指标
- 注册用户数
- 企业认证率
- 订单转化率
- 客户满意度

### 7.2 技术指标
- 系统响应时间 < 2秒
- 系统可用性 > 99.9%
- 并发用户数 > 1000
- 数据准确性 > 99.99%
