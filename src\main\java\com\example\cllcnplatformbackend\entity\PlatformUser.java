package com.example.cllcnplatformbackend.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 平台统一用户实体
 * 对应 v_platform_users 视图
 * 
 * <AUTHOR>
 * @date 2025-08-02 12:10:00 +08:00
 * @reference 基于 project_document/architecture/data_structures_final.md
 */
@Data
public class PlatformUser {
    
    /**
     * 平台用户ID（如：E123, C456）
     */
    private String platformUserId;
    
    /**
     * 源表类型（employee/client）
     */
    private String sourceTable;
    
    /**
     * 源表ID
     */
    private Long sourceId;
    
    /**
     * 用户类型（admin/manager/employee/client）
     */
    private String userType;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 状态（active/inactive）
     */
    private String status;
    
    /**
     * 部门ID（仅员工有）
     */
    private Long departmentId;
    
    /**
     * 员工ID（仅客户有，表示负责的员工）
     */
    private Long employeeId;
    
    /**
     * 原始角色
     */
    private String originalRole;
    
    /**
     * 最后同步时间
     */
    private LocalDateTime lastSyncTime;
    
    /**
     * 密码（需要单独查询）
     */
    private String password;
}
