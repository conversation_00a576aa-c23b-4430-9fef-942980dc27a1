/**
 * 用户状态管理
 * 
 * @description 用户认证和权限状态管理
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/data_structures_final.md
 * @note 与admin-ui共享相同架构，但独立实现
 */

import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import type { UserInfo, LoginCredentials, BackendLoginResponse } from '@/types/api'
import { login, logout, getUserInfo, refreshToken } from '@/api/auth'
import { storage, token } from '@/utils'
import { toast } from 'vue-sonner'

/**
 * 根据用户类型获取权限列表
 */
const getUserPermissions = (userType: string): string[] => {
  switch (userType) {
    case 'admin':
      return ['user:read', 'user:write', 'shipping:query', 'shipping:manage', 'order:manage', 'system:admin']
    case 'manager':
      return ['user:read', 'shipping:query', 'shipping:manage', 'order:manage']
    case 'employee':
      return ['user:read', 'shipping:query', 'order:read']
    case 'client':
      return ['user:read', 'shipping:query', 'order:create', 'order:read']
    default:
      return ['user:read', 'shipping:query']
  }
}

// 用户状态接口
interface UserState {
  userInfo: UserInfo | null
  token: string | null
  refreshToken: string | null
  permissions: string[]
  roles: string[]
  loading: boolean
  error: string | null
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const state = reactive<UserState>({
    userInfo: null,
    token: storage.getLocal('user_token'),
    refreshToken: storage.getLocal('user_refresh_token'),
    permissions: [],
    roles: [],
    loading: false,
    error: null
  })

  // 计算属性
  const isAuthenticated = computed(() => !!state.token)
  const userName = computed(() => state.userInfo?.username || '')
  const userRoles = computed(() => state.userInfo?.roles || [])
  const userPermissions = computed(() => state.userInfo?.permissions || [])

  /**
   * 用户登录
   */
  const loginUser = async (credentials: LoginCredentials): Promise<void> => {
    try {
      state.loading = true
      state.error = null

      const response = await login(credentials)

      // 后端返回的数据结构：{code, data: {accessToken, tokenType, expiresIn, userInfo}, message, timestamp}
      const loginData: BackendLoginResponse = response.data

      // 保存认证信息
      state.token = loginData.accessToken
      state.refreshToken = loginData.accessToken + '_refresh' // 临时方案，后续可实现真正的refresh token

      // 直接使用登录响应中的用户信息，无需再次调用getUserInfo
      const backendUserInfo = loginData.userInfo

      // 将后端用户信息映射到前端UserInfo结构
      state.userInfo = {
        id: backendUserInfo.platformUserId,
        username: backendUserInfo.email || backendUserInfo.mobile,
        email: backendUserInfo.email,
        phone: backendUserInfo.mobile,
        name: backendUserInfo.realName,
        avatar: '',
        role: backendUserInfo.userType,
        permissions: getUserPermissions(backendUserInfo.userType),
        roles: [backendUserInfo.userType],
        status: backendUserInfo.status as 'active' | 'inactive',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      state.permissions = state.userInfo.permissions
      state.roles = state.userInfo.roles

      // 持久化存储
      storage.setLocal('user_token', state.token)
      storage.setLocal('user_refresh_token', state.refreshToken)
      storage.setLocal('user_info', state.userInfo)

      toast.success('登录成功')

    } catch (error: any) {
      state.error = error.message || '登录失败'
      toast.error(error.message || '登录失败')
      throw error
    } finally {
      state.loading = false
    }
  }

  // 注册功能暂未开放

  /**
   * 用户登出
   */
  const logoutUser = async (): Promise<void> => {
    try {
      // 调用登出API
      if (state.token) {
        await logout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除状态
      clearUserData()
      toast.success('已退出登录')
    }
  }

  /**
   * 刷新Token
   */
  const refreshUserToken = async (): Promise<void> => {
    if (!state.refreshToken) {
      throw new Error('没有可用的刷新令牌')
    }

    try {
      state.loading = true
      state.error = null

      const response = await refreshToken({ refreshToken: state.refreshToken })

      // 后端返回的数据结构：{code, data: {accessToken, tokenType, expiresIn}, message, timestamp}
      const refreshData = response.data

      // 更新token
      state.token = refreshData.accessToken

      // 持久化存储新token
      storage.setLocal('user_token', state.token)

      console.log('Token刷新成功')

    } catch (error: any) {
      state.error = error.message || 'Token刷新失败'
      console.error('Token刷新失败:', error)

      // 如果刷新失败，清除用户数据并跳转到登录页
      clearUserData()
      toast.error('登录已过期，请重新登录')

      throw error
    } finally {
      state.loading = false
    }
  }

  /**
   * 获取当前用户信息
   */
  const getCurrentUserInfo = async (): Promise<void> => {
    if (!state.token) {
      throw new Error('未登录')
    }

    try {
      state.loading = true
      const response = await getUserInfo()
      // 后端/auth/me接口返回：{code: 200, data: {platformUserId, userType, realName, email, mobile, status}, message, timestamp}
      const backendUserInfo = response.data

      // 将后端用户信息映射到前端UserInfo结构
      state.userInfo = {
        id: backendUserInfo.platformUserId,
        username: backendUserInfo.email || backendUserInfo.mobile,
        email: backendUserInfo.email,
        phone: backendUserInfo.mobile,
        name: backendUserInfo.realName,
        avatar: '',
        role: backendUserInfo.userType,
        permissions: getUserPermissions(backendUserInfo.userType),
        roles: [backendUserInfo.userType],
        status: backendUserInfo.status as 'active' | 'inactive',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      state.permissions = state.userInfo.permissions
      state.roles = state.userInfo.roles

      // 更新本地存储
      storage.setLocal('user_info', state.userInfo)

    } catch (error: any) {
      state.error = error.message || '获取用户信息失败'
      throw error
    } finally {
      state.loading = false
    }
  }

  /**
   * 检查是否有指定权限
   */
  const hasPermission = (permission: string): boolean => {
    return state.permissions.includes(permission)
  }

  /**
   * 检查是否有任一权限
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => state.permissions.includes(permission))
  }

  /**
   * 检查是否有所有权限
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => state.permissions.includes(permission))
  }

  /**
   * 检查是否有指定角色
   */
  const hasRole = (role: string): boolean => {
    return state.roles.includes(role)
  }

  /**
   * 检查是否有任一角色
   */
  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => state.roles.includes(role))
  }

  /**
   * 检查是否有所有角色
   */
  const hasAllRoles = (roles: string[]): boolean => {
    return roles.every(role => state.roles.includes(role))
  }

  /**
   * 清除用户数据
   */
  const clearUserData = (): void => {
    state.userInfo = null
    state.token = null
    state.refreshToken = null
    state.permissions = []
    state.roles = []
    state.error = null

    // 清除本地存储
    storage.removeLocal('user_token')
    storage.removeLocal('user_refresh_token')
    storage.removeLocal('user_info')
  }

  /**
   * 初始化用户状态
   */
  const initUserState = (): void => {
    // 从本地存储恢复用户信息
    const savedUserInfo = storage.getLocal<UserInfo>('user_info')
    if (savedUserInfo) {
      state.userInfo = savedUserInfo
      state.permissions = savedUserInfo.permissions
      state.roles = savedUserInfo.roles
    }
  }

  // 初始化
  initUserState()

  return {
    // 状态
    ...state,
    
    // 计算属性
    isAuthenticated,
    userName,
    userRoles,
    userPermissions,
    
    // 方法
    login: loginUser,
    logout: logoutUser,
    refreshToken: refreshUserToken,
    getCurrentUser: getCurrentUserInfo,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    clearUserData,
    initUserState
  }
})
