<!--
  首页组件
  
  @description 用户端首页，展示主要功能和信息
  <AUTHOR>
  @date 2025-07-18 09:21:13 +08:00
  @reference 基于shadcn-vue设计系统
-->

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ChevronLeft, ChevronRight, Search, Zap, Shield, Globe, Users, ArrowRight, Ship, Package, Plane, Train, ChevronDown } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { carouselData, serviceFeatures, ports, searchHistory, shippingModes } from '@/data/mockData'

// 特惠运价数据
const specialOffers = ref([
  {
    id: 1,
    origin: '广州',
    destination: '雅加达',
    price: '90',
    currency: 'USD',
    containerType: '20GP',
    validUntil: '2025-07-26',
    isSpecial: true
  },
  {
    id: 2,
    origin: '广州',
    destination: '海防',
    price: '30',
    currency: 'USD',
    containerType: '20GP',
    validUntil: '2025-07-26',
    isSpecial: true
  },
  {
    id: 3,
    origin: '上海',
    destination: '达曼',
    price: '270',
    currency: 'USD',
    containerType: '20GP',
    validUntil: '2025-07-21',
    isSpecial: true
  },
  {
    id: 4,
    origin: '深圳',
    destination: '海防',
    price: '30',
    currency: 'USD',
    containerType: '20GP',
    validUntil: '2025-07-26',
    isSpecial: true
  },
  {
    id: 5,
    origin: '上海',
    destination: '曼谷',
    price: '100',
    currency: 'USD',
    containerType: '20GP',
    validUntil: '2025-07-25',
    isSpecial: true
  },
  {
    id: 6,
    origin: '上海',
    destination: '林查班',
    price: '90',
    currency: 'USD',
    containerType: '20GP',
    validUntil: '2025-07-25',
    isSpecial: true
  }
])

// 关于平台数据
const aboutPlatform = ref({
  title: '关于中航网平台',
  introduction: '中航网（CLLCN）平台是深圳中航物流有限公司研发。',
  history: '公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍公司平台信息介绍',
  services: [
    {
      title: '全网超低航运价',
      description: '价格直接对接船司，全网超低航运价，每日推出特惠运价，让您的物流成本降低30%'
    },
    {
      title: '运价实时更新',
      description: '航线费用明细一目了然，拒绝隐性收费，保证每一笔费用支出详细可查'
    },
    {
      title: '便捷高效的客户服务',
      description: '港口信息全面，24小时报价、运价、订舱信息一键复制，不做重复工作'
    },
    {
      title: '物流全程追踪',
      description: '精准船期、船舶航行轨迹、货柜状态一目了然，为您提供最真实有效、更智能的物流管理体验'
    }
  ]
})

// 轮播图状态
const currentSlide = ref(0)
const carouselItems = carouselData

// 搜索表单状态
const searchForm = ref({
  origin: '',
  destination: '',
  mode: 'fcl'
})

// 运输方式状态
const selectedMode = ref('fcl')
const modes = shippingModes

// 港口搜索状态
const originSearch = ref('')
const destinationSearch = ref('')
const originOpen = ref(false)
const destinationOpen = ref(false)

// 搜索历史状态
const showHistory = ref(false)
const recentSearches = ref(searchHistory)

// 自动轮播
let carouselInterval

// 港口过滤
const filteredOriginPorts = computed(() => {
  if (!originSearch.value) return ports
  return ports.filter(port =>
    port.name.toLowerCase().includes(originSearch.value.toLowerCase()) ||
    port.code.toLowerCase().includes(originSearch.value.toLowerCase())
  )
})

const filteredDestinationPorts = computed(() => {
  if (!destinationSearch.value) return ports
  return ports.filter(port =>
    port.name.toLowerCase().includes(destinationSearch.value.toLowerCase()) ||
    port.code.toLowerCase().includes(destinationSearch.value.toLowerCase())
  )
})

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % carouselItems.length
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? carouselItems.length - 1 : currentSlide.value - 1
}

const goToSlide = (index) => {
  currentSlide.value = index
}

const startCarousel = () => {
  carouselInterval = setInterval(nextSlide, 5000)
}

const stopCarousel = () => {
  if (carouselInterval) {
    clearInterval(carouselInterval)
  }
}

// 运输方式选择
const selectMode = (modeId) => {
  selectedMode.value = modeId
  searchForm.value.mode = modeId
}

// 港口选择
const selectOriginPort = (port) => {
  searchForm.value.origin = port.code
  originSearch.value = port.name
  originOpen.value = false
}

const selectDestinationPort = (port) => {
  searchForm.value.destination = port.code
  destinationSearch.value = port.name
  destinationOpen.value = false
}

// 搜索处理
const handleSearch = () => {
  if (searchForm.value.origin && searchForm.value.destination) {
    console.log('搜索运价:', searchForm.value)
    // 添加到搜索历史
    const newSearch = {
      id: Date.now(),
      origin: searchForm.value.origin,
      destination: searchForm.value.destination,
      mode: searchForm.value.mode,
      searchTime: new Date().toLocaleString('zh-CN')
    }
    recentSearches.value.unshift(newSearch)
    // 只保留最近5条记录
    if (recentSearches.value.length > 5) {
      recentSearches.value = recentSearches.value.slice(0, 5)
    }
    // 这里可以跳转到运价查询页面
  }
}

// 使用历史搜索
const useHistorySearch = (history) => {
  searchForm.value.origin = history.origin
  searchForm.value.destination = history.destination
  searchForm.value.mode = history.mode
  selectedMode.value = history.mode

  // 更新搜索框显示
  originSearch.value = getPortName(history.origin)
  destinationSearch.value = getPortName(history.destination)

  showHistory.value = false
}

// 清除搜索历史
const clearHistory = () => {
  recentSearches.value = []
  showHistory.value = false
}

// 获取港口名称
const getPortName = (code) => {
  const port = ports.find(p => p.code === code)
  return port ? port.name : code
}

// 获取运输方式名称
const getModeName = (modeId) => {
  const mode = modes.find(m => m.id === modeId)
  return mode ? mode.name : modeId
}

// 生命周期
onMounted(() => {
  startCarousel()
})

// 清理
onUnmounted(() => {
  stopCarousel()
})
</script>

<template>
  <div class="min-h-screen">
    <!-- 轮播图区域 -->
    <section class="relative h-[267px] md:h-[333px] overflow-hidden group">
      <div
        class="flex transition-transform duration-500 ease-in-out h-full"
        :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
      >
        <div
          v-for="(item, index) in carouselItems"
          :key="item.id"
          class="w-full h-full flex-shrink-0 relative"
        >
          <img
            :src="item.image"
            :alt="item.title"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 bg-black/40 flex items-center justify-center">
            <div class="text-center text-white max-w-2xl px-4">
              <h1 class="text-3xl md:text-5xl font-bold mb-4">{{ item.title }}</h1>
              <p class="text-lg md:text-xl mb-6 opacity-90">{{ item.description }}</p>
              <Button size="lg" class="bg-white text-black hover:bg-gray-100">
                了解更多
                <ArrowRight class="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 轮播控制按钮 - 只在悬停时显示 -->
      <Button
        variant="ghost"
        size="icon"
        class="absolute left-4 top-1/2 -translate-y-1/2 bg-black/20 backdrop-blur-sm border border-white/30 text-white hover:bg-black/40 hover:border-white/50 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10 shadow-lg"
        @click="prevSlide"
      >
        <ChevronLeft class="h-5 w-5" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        class="absolute right-4 top-1/2 -translate-y-1/2 bg-black/20 backdrop-blur-sm border border-white/30 text-white hover:bg-black/40 hover:border-white/50 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10 shadow-lg"
        @click="nextSlide"
      >
        <ChevronRight class="h-5 w-5" />
      </Button>

      <!-- 轮播指示器 -->
      <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        <button
          v-for="(_, index) in carouselItems"
          :key="index"
          class="w-3 h-3 rounded-full transition-colors"
          :class="currentSlide === index ? 'bg-white' : 'bg-white/50'"
          @click="goToSlide(index)"
        />
      </div>
    </section>

    <!-- 搜索功能区域 -->
    <section class="py-8 md:py-10 lg:py-12 bg-muted/50">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="text-center mb-6 md:mb-8">
          <h2 class="text-2xl md:text-3xl font-bold mb-3 md:mb-4">快速运价查询</h2>
          <p class="text-muted-foreground text-sm md:text-base px-4">选择运输方式，输入起运港和目的港，获取实时运价报价</p>
        </div>

        <Card class="p-4">
          <!-- 运输方式标签 -->
          <div class="mb-4">
            <div class="flex flex-wrap gap-2 justify-center">
              <Button
                v-for="mode in modes"
                :key="mode.id"
                :variant="selectedMode === mode.id ? 'default' : 'outline'"
                class="h-8 px-3 text-sm rounded-full transition-all duration-200"
                @click="selectMode(mode.id)"
              >
                <Ship v-if="mode.icon === 'ship'" class="mr-1.5 h-3.5 w-3.5" />
                <Package v-else-if="mode.icon === 'package'" class="mr-1.5 h-3.5 w-3.5" />
                <Plane v-else-if="mode.icon === 'plane'" class="mr-1.5 h-3.5 w-3.5" />
                <Train v-else-if="mode.icon === 'train'" class="mr-1.5 h-3.5 w-3.5" />
                {{ mode.name }}
              </Button>
            </div>
          </div>

          <!-- 搜索表单 - 响应式布局 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-3 mb-4">
            <!-- 起运港 -->
            <div class="md:col-span-1 lg:col-span-5 space-y-1.5">
              <label class="text-sm font-medium">起运港</label>
              <Popover v-model:open="originOpen">
                <PopoverTrigger as-child>
                  <Button
                    variant="outline"
                    role="combobox"
                    :aria-expanded="originOpen"
                    class="w-full h-10 justify-between text-sm"
                  >
                    {{ originSearch || "请输入（支持中/英文港口名）" }}
                    <ChevronDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent class="w-full p-0">
                  <Command>
                    <CommandInput
                      v-model="originSearch"
                      placeholder="搜索港口..."
                      class="h-9"
                    />
                    <CommandEmpty>未找到港口</CommandEmpty>
                    <CommandGroup>
                      <CommandList>
                        <CommandItem
                          v-for="port in filteredOriginPorts.slice(0, 10)"
                          :key="port.code"
                          :value="port.code"
                          @select="selectOriginPort(port)"
                        >
                          {{ port.name }}
                        </CommandItem>
                      </CommandList>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <!-- 目的港 -->
            <div class="md:col-span-1 lg:col-span-5 space-y-1.5">
              <label class="text-sm font-medium">目的港</label>
              <Popover v-model:open="destinationOpen">
                <PopoverTrigger as-child>
                  <Button
                    variant="outline"
                    role="combobox"
                    :aria-expanded="destinationOpen"
                    class="w-full h-10 justify-between text-sm"
                  >
                    {{ destinationSearch || "请输入（支持中/英文港口名）" }}
                    <ChevronDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent class="w-full p-0">
                  <Command>
                    <CommandInput
                      v-model="destinationSearch"
                      placeholder="搜索港口..."
                      class="h-9"
                    />
                    <CommandEmpty>未找到港口</CommandEmpty>
                    <CommandGroup>
                      <CommandList>
                        <CommandItem
                          v-for="port in filteredDestinationPorts.slice(0, 10)"
                          :key="port.code"
                          :value="port.code"
                          @select="selectDestinationPort(port)"
                        >
                          {{ port.name }}
                        </CommandItem>
                      </CommandList>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <!-- 查询按钮 -->
            <div class="md:col-span-2 lg:col-span-2 flex items-end">
              <Button
                @click="handleSearch"
                class="w-full h-10 text-sm bg-primary hover:bg-primary/90"
                :disabled="!searchForm.origin || !searchForm.destination"
              >
                <Search class="mr-1.5 h-4 w-4" />
                查询
              </Button>
            </div>
          </div>

            <!-- 搜索历史 -->
            <div v-if="recentSearches.length > 0" class="border-t pt-4">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-sm font-medium text-muted-foreground">搜索历史</h3>
                <Button variant="ghost" size="sm" @click="clearHistory" class="text-xs h-7">
                  清除历史
                </Button>
              </div>
              <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
                <div
                  v-for="history in recentSearches.slice(0, 6)"
                  :key="history.id"
                  class="p-2 border rounded-lg cursor-pointer hover:bg-accent transition-colors"
                  @click="useHistorySearch(history)"
                >
                  <div class="flex items-center gap-1 mb-1">
                    <Badge variant="secondary" class="text-xs px-1.5 py-0.5">
                      {{ getModeName(history.mode) }}
                    </Badge>
                  </div>
                  <div class="text-xs font-medium leading-tight">
                    {{ getPortName(history.origin) }} → {{ getPortName(history.destination) }}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
    </section>

    <!-- 特惠运价 -->
    <section class="py-12 md:py-16 lg:py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="text-center mb-8 md:mb-12 lg:mb-16">
          <h2 class="text-2xl md:text-3xl font-bold mb-4 md:mb-6">特惠运价</h2>
          <p class="text-muted-foreground max-w-2xl mx-auto text-base md:text-lg px-4">
            精选优质航线，超值运价等您来抢
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
          <Card
            v-for="offer in specialOffers.slice(0, 6)"
            :key="offer.id"
            class="relative overflow-hidden hover:shadow-xl transition-all duration-300 group border-2 hover:border-primary/20"
          >
            <!-- 特惠标签 -->
            <div class="absolute top-3 left-3 z-10">
              <Badge variant="destructive" class="text-xs font-medium">
                特惠
              </Badge>
            </div>

            <CardHeader class="pb-4">
              <div class="flex items-center justify-between mb-2">
                <div class="text-sm text-muted-foreground">
                  特价有效期至: {{ offer.validUntil }}
                </div>
              </div>
              <CardTitle class="text-lg md:text-xl flex items-center gap-2">
                <span class="text-primary font-bold">{{ offer.origin }}</span>
                <ArrowRight class="h-4 w-4 text-muted-foreground" />
                <span class="text-primary font-bold">{{ offer.destination }}</span>
              </CardTitle>
            </CardHeader>

            <CardContent class="pt-0">
              <div class="space-y-3">
                <!-- 价格信息 -->
                <div class="flex items-baseline gap-2">
                  <span class="text-2xl md:text-3xl font-bold text-primary">
                    {{ offer.currency }} {{ offer.price }}
                  </span>
                  <span class="text-sm text-muted-foreground">
                    / {{ offer.containerType }}
                  </span>
                </div>

                <!-- 集装箱类型 -->
                <div class="flex items-center gap-2">
                  <Package class="h-4 w-4 text-muted-foreground" />
                  <span class="text-sm text-muted-foreground">
                    集装箱类型: {{ offer.containerType }}
                  </span>
                </div>

                <!-- 查询按钮 -->
                <Button
                  class="w-full mt-4 group-hover:bg-primary/90 transition-colors"
                  size="sm"
                >
                  立即查询
                  <ArrowRight class="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 查看更多按钮 -->
        <div class="text-center mt-8 md:mt-10 lg:mt-12">
          <Button variant="outline" size="lg" class="px-8">
            查看更多特惠运价
            <ArrowRight class="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>

    <!-- 服务特色 -->
    <section class="py-12 md:py-16 lg:py-20">
      <div class="container mx-auto max-w-8xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="text-center mb-8 md:mb-12 lg:mb-16">
          <h2 class="text-2xl md:text-3xl font-bold mb-4 md:mb-6">服务特色</h2>
          <p class="text-muted-foreground max-w-2xl mx-auto text-base md:text-lg px-4">
            我们致力于为客户提供专业、安全、高效的国际物流服务
          </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
          <Card v-for="feature in serviceFeatures" :key="feature.id" class="text-center p-4 md:p-6 lg:p-8 hover:shadow-lg transition-shadow">
            <CardHeader class="pb-3 md:pb-4">
              <div class="mx-auto w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-4 md:mb-5 lg:mb-6">
                <Zap v-if="feature.icon === 'zap'" class="h-6 w-6 md:h-7 md:w-7 lg:h-8 lg:w-8 text-primary" />
                <Shield v-else-if="feature.icon === 'shield'" class="h-6 w-6 md:h-7 md:w-7 lg:h-8 lg:w-8 text-primary" />
                <Globe v-else-if="feature.icon === 'globe'" class="h-6 w-6 md:h-7 md:w-7 lg:h-8 lg:w-8 text-primary" />
                <Users v-else-if="feature.icon === 'users'" class="h-6 w-6 md:h-7 md:w-7 lg:h-8 lg:w-8 text-primary" />
              </div>
              <CardTitle class="text-lg md:text-xl mb-2 md:mb-3">{{ feature.title }}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription class="text-sm md:text-base">{{ feature.description }}</CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>

    <!-- 关于中航网平台 -->
    <section class="py-12 md:py-16 lg:py-20 bg-primary text-primary-foreground">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="text-center mb-8 md:mb-10 lg:mb-12">
          <h2 class="text-2xl md:text-3xl font-bold mb-3 md:mb-4 text-primary-foreground">{{ aboutPlatform.title }}</h2>
          <p class="text-primary-foreground/80 text-base md:text-lg px-4 max-w-4xl mx-auto">
            {{ aboutPlatform.introduction }}
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-10 lg:gap-12 items-center">
          <!-- 左侧：平台介绍 -->
          <div class="space-y-6 md:space-y-8">
            <div class="bg-primary-foreground/10 rounded-xl p-6 md:p-8">
              <h3 class="text-xl md:text-2xl font-bold mb-4 text-primary-foreground">中航网（CLLCN）平台</h3>
              <p class="text-primary-foreground/90 text-sm md:text-base leading-relaxed">
                {{ aboutPlatform.history }}
              </p>
            </div>
          </div>

          <!-- 右侧：服务优势 -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
            <div
              v-for="service in aboutPlatform.services"
              :key="service.title"
              class="bg-primary-foreground/10 rounded-lg p-4 md:p-6 hover:bg-primary-foreground/15 transition-colors"
            >
              <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-primary-foreground/20 rounded-lg flex items-center justify-center mr-3">
                  <Zap v-if="service.title.includes('航运价')" class="h-4 w-4 text-primary-foreground" />
                  <Shield v-else-if="service.title.includes('更新')" class="h-4 w-4 text-primary-foreground" />
                  <Users v-else-if="service.title.includes('客户')" class="h-4 w-4 text-primary-foreground" />
                  <Globe v-else class="h-4 w-4 text-primary-foreground" />
                </div>
                <h4 class="font-semibold text-primary-foreground text-sm md:text-base">{{ service.title }}</h4>
              </div>
              <p class="text-primary-foreground/80 text-xs md:text-sm leading-relaxed">
                {{ service.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速入口 -->
    <section class="py-12 md:py-16 lg:py-20">
      <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">
        <div class="text-center mb-8 md:mb-12 lg:mb-16">
          <h2 class="text-2xl md:text-3xl font-bold mb-4 md:mb-6">快速入口</h2>
          <p class="text-muted-foreground text-base md:text-lg px-4">选择您需要的服务，开启便捷物流体验</p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 max-w-5xl mx-auto">
          <Card class="p-4 md:p-6 lg:p-8 hover:shadow-xl transition-all duration-300 cursor-pointer group border-2 hover:border-primary/20">
            <CardHeader class="pb-4 md:pb-5 lg:pb-6">
              <div class="w-10 h-10 md:w-12 md:h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 md:mb-4 group-hover:bg-primary/20 transition-colors">
                <Search class="h-5 w-5 md:h-6 md:w-6 text-primary" />
              </div>
              <CardTitle class="text-lg md:text-xl">运价查询</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription class="mb-4 md:mb-5 lg:mb-6 text-sm md:text-base">
                快速获取海运、空运运价信息，支持多种运输方式对比
              </CardDescription>
              <Button variant="outline" class="w-full h-9 md:h-10 lg:h-11 text-sm md:text-base group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                立即查询
              </Button>
            </CardContent>
          </Card>

          <Card class="p-4 md:p-6 lg:p-8 hover:shadow-xl transition-all duration-300 cursor-pointer group border-2 hover:border-primary/20">
            <CardHeader class="pb-4 md:pb-5 lg:pb-6">
              <div class="w-10 h-10 md:w-12 md:h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 md:mb-4 group-hover:bg-primary/20 transition-colors">
                <Globe class="h-5 w-5 md:h-6 md:w-6 text-primary" />
              </div>
              <CardTitle class="text-lg md:text-xl">关于我们</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription class="mb-4 md:mb-5 lg:mb-6 text-sm md:text-base">
                了解中航网平台的发展历程、服务理念和企业文化
              </CardDescription>
              <Button variant="outline" class="w-full h-9 md:h-10 lg:h-11 text-sm md:text-base group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                了解更多
              </Button>
            </CardContent>
          </Card>

          <Card class="p-4 md:p-6 lg:p-8 hover:shadow-xl transition-all duration-300 cursor-pointer group border-2 hover:border-primary/20 sm:col-span-2 lg:col-span-1">
            <CardHeader class="pb-4 md:pb-5 lg:pb-6">
              <div class="w-10 h-10 md:w-12 md:h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 md:mb-4 group-hover:bg-primary/20 transition-colors">
                <Users class="h-5 w-5 md:h-6 md:w-6 text-primary" />
              </div>
              <CardTitle class="text-lg md:text-xl">在线客服</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription class="mb-4 md:mb-5 lg:mb-6 text-sm md:text-base">
                专业客服团队7×24小时为您提供贴心咨询服务
              </CardDescription>
              <Button variant="outline" class="w-full h-9 md:h-10 lg:h-11 text-sm md:text-base group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                联系客服
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  </div>
</template>
