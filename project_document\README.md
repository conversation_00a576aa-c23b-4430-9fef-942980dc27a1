# CLLCN国际物流平台 - 后端基础架构搭建项目

## 项目概览

本项目旨在基于公司管理平台项目的成熟架构模式，为CLLCN国际物流平台构建完善的后端基础架构，实现混合架构设计下的统一用户认证和独立业务数据管理。

## 项目目标

1. **依赖管理优化**：参考公司管理项目的pom.xml配置，添加物流平台所需的核心依赖
2. **配置文件完善**：配置双数据库连接和中间件集成
3. **统一异常处理**：复用并适配异常处理机制
4. **工具类封装**：迁移通用工具类并适配物流业务
5. **基础架构组件**：建立标准化的响应格式、分页查询等基础组件

## 技术栈

- **框架**: Spring Boot 3.5.3
- **语言**: Java 21
- **数据库**: MySQL 8.0 (双库架构)
- **缓存**: Redis 7.0+
- **消息队列**: 预留扩展（支持RabbitMQ/Kafka/Redis Pub/Sub）
- **文档**: SpringDoc OpenAPI
- **安全**: Spring Security + JWT

## 混合架构设计

### 数据库架构
```
company_management_system (公司管理数据库)
├── employee (员工表) - 认证共享
├── client (客户表) - 认证共享
└── 其他管理业务表...

logistics_platform (物流平台数据库)
├── user_mapping (用户映射表) - 缓存用户信息
├── country (国家表)
├── port (港口表)
├── route (航线表)
└── 其他物流业务表...
```

### 集成策略
- **用户认证共享**：直接使用公司管理系统的用户体系
- **业务数据独立**：物流业务数据使用独立数据库
- **适度解耦**：既保证用户体验统一，又实现业务系统独立发展

## 项目结构

```
cllcn-platform-backend/
├── project_document/           # 项目文档和分析记录
├── src/main/java/com/cllcn/logistics/
│   ├── config/                 # 配置类
│   ├── controller/             # 控制层
│   ├── service/               # 业务逻辑层
│   ├── repository/            # 数据访问层
│   ├── entity/                # 实体类
│   ├── dto/                   # 数据传输对象
│   ├── exception/             # 异常处理
│   ├── utils/                 # 工具类
│   └── constants/             # 常量定义
└── src/main/resources/
    ├── application.yml        # 主配置文件
    ├── application-dev.yml    # 开发环境配置
    ├── application-prod.yml   # 生产环境配置
    └── mapper/               # MyBatis映射文件
```

## 快速导航

- [需求分析](analysis.md) - 详细需求分析和业务流程理解
- [架构设计](architecture/) - 系统架构和数据结构设计
- [实施计划](plan.md) - 详细实施计划和检查清单
- [团队协作日志](logs/team_collaboration_log.md) - 团队协作记录
- [项目进度](logs/progress_log.md) - 项目进度跟踪

## 开发规范

- 遵循KISS、YAGNI、DRY等核心编程原则
- 统一异常处理和错误响应格式
- 标准化API设计和文档注释
- 完善的单元测试和集成测试

**更新时间**: 2025-07-31 15:10:51 +08:00
**项目状态**: 实施阶段 - 基础架构搭建完成
