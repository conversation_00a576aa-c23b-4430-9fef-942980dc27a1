import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

/**
 * Vite配置文件 - admin-ui
 *
 * @description 管理后台的Vite构建配置，包含代理、构建优化等
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),
      vueDevTools(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    server: {
      port: 3001,
      host: '0.0.0.0',
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''), // 路径重写：/api/xxx → /xxx
          configure: (proxy, options) => {
            proxy.on('error', (err) => {
              console.error('代理错误:', err)
            })
            proxy.on('proxyReq', (proxyReq, req) => {
              console.log('代理请求:', req.method, req.url, '→', options.target + proxyReq.path)
            })
          }
        }
      }
    },
    build: {
      target: 'es2020',
      chunkSizeWarningLimit: 1000, // 允许更大的chunk，适合管理后台
      rollupOptions: {
        output: {
          manualChunks: {
            'element-plus': ['element-plus'],
            'vendor': ['vue', 'vue-router', 'pinia'],
            'utils': ['axios', 'dayjs', 'lodash-es']
          }
        }
      },
      // 生产环境优化
      minify: mode === 'production' ? 'terser' : false
    },
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString())
    }
  }
})
