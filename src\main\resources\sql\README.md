# CLLCN国际物流平台 - 数据库脚本说明

## 📋 脚本概览

本目录包含CLLCN国际物流平台的所有数据库初始化和更新脚本，支持混合架构模式。

## 🏗️ 架构模式：混合架构

- **用户认证共享**：使用`company_management_system`数据库的用户表
- **业务数据独立**：使用`logistics_platform`数据库存储物流业务数据
- **实时数据访问**：通过跨库查询实现用户认证和业务数据关联

## 📁 脚本文件说明

### 1. 基础初始化脚本

| 文件名 | 描述 | 执行顺序 | 状态 |
|--------|------|----------|------|
| `init-1.sql` | 公司管理系统数据库初始化 | 1 | ✅ 已执行 |
| `init-1-supplement.sql` | 公司管理系统补充脚本（支持混合架构） | 2 | ✅ 已执行 |
| `init-2.sql` | 物流平台数据库初始化 | 3 | ✅ 已执行 |

### 2. 混合架构更新脚本

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `update-for-hybrid-architecture.sql` | 混合架构数据库更新脚本 | 调整现有数据库支持混合架构 |
| `execute-hybrid-architecture-update.sql` | 一键执行脚本 | **推荐使用** |

## 🚀 快速执行指南

### 方案一：一键执行（推荐）

```sql
-- 在MySQL客户端中执行
SOURCE /path/to/execute-hybrid-architecture-update.sql;
```

### 方案二：分步执行

```sql
-- 1. 执行混合架构更新
SOURCE /path/to/update-for-hybrid-architecture.sql;

-- 2. 验证执行结果
SELECT '混合架构配置完成' as status;
```

## 📊 数据库结构

### company_management_system（用户认证共享）

```
employee (员工表)
├── employee_id (主键)
├── name (姓名)
├── email (邮箱) - 登录凭证
├── phone (手机号) - 登录凭证  
├── password (密码) - MD5加密
├── role (角色) - ADMIN/MANAGER/USER
└── status (状态) - Active/Inactive

client (客户表)
├── client_id (主键)
├── name (姓名)
├── email (邮箱) - 登录凭证
├── phone (手机号) - 登录凭证
├── password (密码) - MD5加密
├── status (状态) - 审核通过/已合作/待审核/审核拒绝
└── employee_id (负责员工ID)
```

### logistics_platform（业务数据独立）

```
user_mapping (用户映射表) - 可选，用于性能优化
├── platform_user_id (平台用户ID) - E123/C456
├── source_table (来源表) - employee/client
├── source_id (来源ID)
├── user_type (用户类型) - admin/manager/employee/client
└── ... (其他映射字段)

order_info (订单表)
├── order_id (主键)
├── platform_user_id (关联用户) - 引用user_mapping
├── ... (业务字段)

v_platform_users (统一用户视图)
└── 实时查询company_management_system用户数据
```

## 🔐 认证机制

### 统一认证流程

1. **登录凭证**：支持邮箱或手机号登录
2. **密码验证**：MD5加密存储和验证
3. **用户类型识别**：
   - `E123` - 员工用户（来自employee表）
   - `C456` - 客户用户（来自client表）
4. **权限映射**：
   - `ADMIN` → 系统管理员权限
   - `MANAGER` → 部门管理员权限
   - `USER` → 普通员工权限
   - `CLIENT` → 客户权限

### 认证存储过程

```sql
-- 调用混合架构认证存储过程
CALL HybridAuthenticateUser(
    '<EMAIL>',  -- 登录标识（邮箱或手机号）
    'password_md5',      -- MD5密码
    @platform_user_id,  -- 输出：平台用户ID
    @user_type,          -- 输出：用户类型
    @real_name,          -- 输出：真实姓名
    @status,             -- 输出：状态
    @auth_result         -- 输出：认证结果
);
```

## 🧪 测试数据

脚本会自动创建以下测试用户（仅开发环境）：

| 用户类型 | 邮箱 | 密码 | 角色 |
|----------|------|------|------|
| 员工 | <EMAIL> | admin123 | ADMIN |
| 客户 | <EMAIL> | client123 | CLIENT |

## ✅ 验证检查

执行完成后，脚本会自动进行以下验证：

1. **数据库连接验证** - 确认两个数据库都可访问
2. **用户数据统计** - 统计员工和客户数量
3. **用户映射验证** - 验证映射表数据一致性
4. **存储过程验证** - 确认认证存储过程创建成功
5. **索引验证** - 确认性能优化索引已创建

## 🔧 故障排除

### 常见问题

1. **权限不足**
   ```sql
   -- 确保MySQL用户有足够权限
   GRANT ALL PRIVILEGES ON company_management_system.* TO 'your_user'@'%';
   GRANT ALL PRIVILEGES ON logistics_platform.* TO 'your_user'@'%';
   FLUSH PRIVILEGES;
   ```

2. **字符集问题**
   ```sql
   -- 确保数据库使用UTF8MB4字符集
   ALTER DATABASE company_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ALTER DATABASE logistics_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **外键约束错误**
   ```sql
   -- 临时禁用外键检查
   SET FOREIGN_KEY_CHECKS = 0;
   -- 执行脚本
   -- 重新启用外键检查
   SET FOREIGN_KEY_CHECKS = 1;
   ```

### 回滚操作

如果需要回滚到单数据库模式：

```sql
-- 1. 备份当前数据
-- 2. 删除跨库视图
DROP VIEW IF EXISTS logistics_platform.v_platform_users;
-- 3. 删除存储过程
DROP PROCEDURE IF EXISTS logistics_platform.HybridAuthenticateUser;
-- 4. 调整应用配置为单数据源模式
```

## 📞 技术支持

如果在执行过程中遇到问题，请检查：

1. MySQL版本是否为8.0+
2. 用户权限是否足够
3. 数据库字符集是否正确
4. 网络连接是否正常

**下一步**：执行完数据库脚本后，启动Spring Boot应用程序，测试混合架构的JWT认证功能。
