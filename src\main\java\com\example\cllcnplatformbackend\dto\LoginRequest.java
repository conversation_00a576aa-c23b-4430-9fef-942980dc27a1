package com.example.cllcnplatformbackend.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 用户登录请求DTO
 * 支持邮箱或手机号登录
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
@Data
public class LoginRequest {

    @NotBlank(message = "登录账号不能为空")
    private String account;

    @NotBlank(message = "密码不能为空")
    private String password;

    private Boolean rememberMe = false;
}
