<!--
  收发通页面
  
  @description 消息通知设置和历史记录
  <AUTHOR>
  @date 2025-07-25 16:05:00 +08:00
  @reference 基于 project_document/architecture/code_architecture_final.md
-->

<template>
  <div class="notifications-page">
    <!-- 页面头部 -->
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-foreground">收发通</h2>
      <p class="text-muted-foreground mt-1">管理您的消息通知和历史记录</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 通知设置 -->
      <div class="lg:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle>通知设置</CardTitle>
            <CardDescription>配置您的消息接收偏好</CardDescription>
          </CardHeader>
          <CardContent class="space-y-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium">订单通知</h4>
                  <p class="text-xs text-muted-foreground">订单状态变化时通知</p>
                </div>
                <Switch v-model:checked="notificationSettings.order" />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium">账单通知</h4>
                  <p class="text-xs text-muted-foreground">账单到期提醒</p>
                </div>
                <Switch v-model:checked="notificationSettings.bill" />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium">系统通知</h4>
                  <p class="text-xs text-muted-foreground">系统维护和更新</p>
                </div>
                <Switch v-model:checked="notificationSettings.system" />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium">营销推广</h4>
                  <p class="text-xs text-muted-foreground">优惠活动和产品推荐</p>
                </div>
                <Switch v-model:checked="notificationSettings.promotion" />
              </div>
            </div>

            <Separator />

            <div class="space-y-4">
              <h4 class="text-sm font-medium">接收方式</h4>
              <div class="space-y-3">
                <div class="flex items-center space-x-2">
                  <Checkbox id="email" v-model:checked="receiveChannels.email" />
                  <Label for="email" class="text-sm">邮件通知</Label>
                </div>
                <div class="flex items-center space-x-2">
                  <Checkbox id="sms" v-model:checked="receiveChannels.sms" />
                  <Label for="sms" class="text-sm">短信通知</Label>
                </div>
                <div class="flex items-center space-x-2">
                  <Checkbox id="push" v-model:checked="receiveChannels.push" />
                  <Label for="push" class="text-sm">推送通知</Label>
                </div>
              </div>
            </div>

            <Button @click="saveSettings" class="w-full">
              <Save class="h-4 w-4 mr-2" />
              保存设置
            </Button>
          </CardContent>
        </Card>
      </div>

      <!-- 消息列表 -->
      <div class="lg:col-span-2">
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <div>
                <CardTitle>消息记录</CardTitle>
                <CardDescription>查看您的消息历史</CardDescription>
              </div>
              <div class="flex space-x-2">
                <Button variant="outline" size="sm" @click="markAllAsRead">
                  <CheckCircle class="h-4 w-4 mr-2" />
                  全部已读
                </Button>
                <Button variant="outline" size="sm" @click="clearAllMessages">
                  <Trash2 class="h-4 w-4 mr-2" />
                  清空消息
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <!-- 筛选器 -->
            <div class="mb-4">
              <Select v-model="selectedType">
                <SelectTrigger class="w-48">
                  <SelectValue placeholder="筛选消息类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部消息</SelectItem>
                  <SelectItem value="order">订单通知</SelectItem>
                  <SelectItem value="bill">账单通知</SelectItem>
                  <SelectItem value="system">系统通知</SelectItem>
                  <SelectItem value="promotion">营销推广</SelectItem>
                  <SelectItem value="invoice">发票通知</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 消息列表 -->
            <div class="space-y-3 max-h-96 overflow-y-auto">
              <div
                v-for="message in filteredMessages"
                :key="message.id"
                class="flex items-start space-x-3 p-3 rounded-lg border transition-colors"
                :class="message.isRead ? 'bg-background' : 'bg-accent/50'"
              >
                <div class="flex-shrink-0 mt-1">
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="message.isRead ? 'bg-muted-foreground' : 'bg-primary'"
                  ></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between mb-1">
                    <h4 class="text-sm font-medium text-foreground">{{ message.title }}</h4>
                    <div class="flex items-center space-x-2">
                      <Badge :variant="getMessageTypeVariant(message.type)">
                        {{ getMessageTypeText(message.type) }}
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal class="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem @click="toggleReadStatus(message)">
                            {{ message.isRead ? '标为未读' : '标为已读' }}
                          </DropdownMenuItem>
                          <DropdownMenuItem @click="deleteMessage(message.id)" class="text-destructive">
                            删除消息
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  <p class="text-sm text-muted-foreground mb-2">{{ message.content }}</p>
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-muted-foreground">{{ formatDate(message.createTime) }}</span>
                    <Button
                      v-if="message.relatedId"
                      variant="ghost"
                      size="sm"
                      @click="viewRelated(message)"
                    >
                      查看详情
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredMessages.length === 0" class="text-center py-8">
              <Mail class="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 class="text-lg font-medium text-foreground mb-2">暂无消息</h3>
              <p class="text-muted-foreground">
                {{ selectedType ? '没有找到该类型的消息' : '您还没有收到任何消息' }}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { 
  Save, 
  CheckCircle, 
  Trash2, 
  Mail, 
  MoreHorizontal 
} from 'lucide-vue-next'
import { mockMessages } from '@/data/mockData'
import { MessageType } from '@/types/workspace'
import type { Message } from '@/types/workspace'

// 响应式数据
const selectedType = ref('')
const messages = ref([...mockMessages])

// 通知设置
const notificationSettings = ref({
  order: true,
  bill: true,
  system: true,
  promotion: false
})

// 接收方式
const receiveChannels = ref({
  email: true,
  sms: false,
  push: true
})

// 计算属性
const filteredMessages = computed(() => {
  if (!selectedType.value) {
    return messages.value
  }
  return messages.value.filter(message => message.type === selectedType.value)
})

// 方法
const getMessageTypeVariant = (type: MessageType) => {
  switch (type) {
    case MessageType.ORDER: return 'default'
    case MessageType.BILL: return 'secondary'
    case MessageType.SYSTEM: return 'outline'
    case MessageType.PROMOTION: return 'default'
    case MessageType.INVOICE: return 'default'
    default: return 'secondary'
  }
}

const getMessageTypeText = (type: MessageType) => {
  switch (type) {
    case MessageType.ORDER: return '订单'
    case MessageType.BILL: return '账单'
    case MessageType.SYSTEM: return '系统'
    case MessageType.PROMOTION: return '推广'
    case MessageType.INVOICE: return '发票'
    default: return '其他'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const toggleReadStatus = (message: Message) => {
  const index = messages.value.findIndex(m => m.id === message.id)
  if (index > -1) {
    messages.value[index].isRead = !messages.value[index].isRead
  }
}

const deleteMessage = (messageId: string) => {
  const index = messages.value.findIndex(m => m.id === messageId)
  if (index > -1) {
    messages.value.splice(index, 1)
  }
}

const markAllAsRead = () => {
  messages.value.forEach(message => {
    message.isRead = true
  })
}

const clearAllMessages = () => {
  if (confirm('确定要清空所有消息吗？此操作不可恢复。')) {
    messages.value = []
  }
}

const viewRelated = (message: Message) => {
  console.log('查看相关内容:', message.relatedId, message.type)
  // 实际实现中这里会跳转到相关页面
}

const saveSettings = () => {
  console.log('保存通知设置:', {
    notificationSettings: notificationSettings.value,
    receiveChannels: receiveChannels.value
  })
  // 实际实现中这里会调用API保存设置
}
</script>

<style scoped>
.notifications-page {
  /* 页面样式 */
}
</style>
